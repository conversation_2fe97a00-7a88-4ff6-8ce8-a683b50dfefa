#pragma once
#include <iostream>
#include <string>
#include <string.h>
#include <stdlib.h>

using namespace std;

#include <windows.h>

string GbkToUtf8(const char *src_str);
string Utf8ToGbk(const char *src_str);
wstring Utf8ToUnicode(const string& utf8_str);
string UnicodeToUtf8(const wstring& unicode_str);
string wstringToString(const std::wstring v_wstr);
wstring StringToWstring(const string v_str);
string toBase64(const char * src_str);
