// StrategyDlg.cpp : 实现文件
//

#include "stdafx.h"
#include "ldfcrTester.h"
#include "StrategyDlg.h"
#include "CodeT.h"
#include "afxdialogex.h"


// StrategyDlg 对话框

IMPLEMENT_DYNAMIC(StrategyDlg, CDialogEx)

StrategyDlg::StrategyDlg(CWnd* pParent /*=NULL*/)
	: CDialogEx(IDD_DIALOG_STATEGY_SELECT, pParent)
{

}

StrategyDlg::~StrategyDlg()
{
}

void StrategyDlg::OnInitDlg(const std::map<int, std::string>& v_map)
{
	m_map = v_map;
}

void StrategyDlg::DoDataExchange(CDataExchange* pDX)
{
	CDialogEx::DoDataExchange(pDX);
	DDX_Control(pDX, IDC_LIST2, m_stategySelect);
}

void StrategyDlg::OnOK()
{
}

void StrategyDlg::OnCancel()
{
}

void StrategyDlg::OnClose()
{
	// TODO: 在此添加消息处理程序代码和/或调用默认值
	EndDialog(IDCANCEL);
	CDialog::OnClose();
}


BEGIN_MESSAGE_MAP(StrategyDlg, CDialogEx)
	ON_BN_CLICKED(IDOK, &StrategyDlg::OnBnClickedOk)
END_MESSAGE_MAP()


// StrategyDlg 消息处理程序


void StrategyDlg::OnBnClickedOk()
{
	this->UpdateData(TRUE);
	GetClickedStrategy();
	CDialogEx::OnOK();  // 关闭对话框并返回IDOK
}

std::vector<CString> StrategyDlg::GetStrategyBeChosed()
{
	return m_selected;
}

void StrategyDlg::GetClickedStrategy()
{
	// 遍历检查哪些项被选中
	for (int i = 0; i < m_stategySelect.GetItemCount(); ++i)
	{
		if (m_stategySelect.GetCheck(i))
		{
			CString strText = m_stategySelect.GetItemText(i, 0);
			// 处理选中项...
			m_selected.push_back(strText);
		}
	}
}


BOOL StrategyDlg::OnInitDialog()
{
	CDialogEx::OnInitDialog();

	// TODO:  在此添加额外的初始化
		// 1. 设置ListCtrl样式（通常在OnInitDialog中）
	m_stategySelect.ModifyStyle(0, LVS_REPORT | LVS_SHOWSELALWAYS);
	m_stategySelect.SetExtendedStyle(LVS_EX_CHECKBOXES | LVS_EX_FULLROWSELECT);

	// 添加列（如果是Report视图）
	m_stategySelect.InsertColumn(0, _T("策略名称"), LVCFMT_LEFT, 200);

	for (const auto& pair : m_map)
	{
		CString cstrFirst;
		cstrFirst.Format(_T("%d"), pair.first);
		CString cstrSecond(Utf8ToGbk(pair.second.c_str()).c_str());
		CString cstrStrategy = cstrFirst + _T(".") + cstrSecond;
		int nIndex = m_stategySelect.InsertItem(pair.first, cstrStrategy);
		m_stategySelect.SetCheck(nIndex, FALSE); // 默认不选中
	}

	return TRUE;  // return TRUE unless you set the focus to a control
	// 异常: OCX 属性页应返回 FALSE
}
