#pragma once
#ifndef __LIB_PE_H__
#define __LIB_PE_H__

#if defined(_WIN32)
#  ifdef __LIB_PE_H__
#    define LIB_PE_API __declspec(dllexport)
#  else
#    define LIB_PE_API __declspec(dllimport)
#  endif
#endif

class LIB_PE_API PE
{
public:

	virtual bool CheckFile(const wchar_t* filePath) = 0;

	virtual bool getFilePath(char* outChar) = 0;

	virtual bool getFileSoftVersion(char* outChar) = 0;

	virtual bool getFileMachineVersion(char* outChar) = 0;

	virtual bool getFileProductVersion(char* outChar) = 0;

	virtual bool getFileCreateTime(char* outChar) = 0;

	virtual bool getFilePdbPath(char* outChar) = 0;

	virtual bool getFileGuid(char* outChar) = 0;

private:

	virtual void initLib() = 0;

	virtual void freeLib() = 0;
};

LIB_PE_API bool PEinitLib(PE** pe);

LIB_PE_API void PEfreeLib(PE* pe);
#endif


