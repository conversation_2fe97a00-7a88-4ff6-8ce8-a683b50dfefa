# 库依赖测试覆盖度分析功能文档

## 功能概述

本功能用于分析测试用例对ldfcr相关库的真实依赖情况，帮助了解哪些库被测试用例实际使用，从而评估测试覆盖度。

## 问题背景

### 原始问题
在测试执行过程中发现：
- **开头显示16个库**：测试开始前预加载了所有可能的库
- **结尾显示7个库**：测试结束后只剩下部分常驻库
- **无法区分**：哪些库是测试真正使用的，哪些只是预加载的

### 根本原因
```cpp
// 原来的代码在测试前就初始化了ldfcr引擎
ILDFcr* tempLdfcr = nullptr;
if (ldfcr_InitStartup()) {
    ldfcr_CreateInstance((void**)&tempLdfcr);  // 这里预加载了所有库！
}
```

这导致无法准确分析测试用例的真实库依赖。

## 解决方案

### 方案选择
经过分析，采用了**简单库收集器方案**：
- ✅ **接受数据不完整**：可能丢失一些被卸载的库
- ✅ **性能开销小**：只在关键时刻检测
- ✅ **实现简单**：不需要复杂的监听器

### 核心改动

#### 1. 新增SimpleLibraryCollector类
```cpp
class SimpleLibraryCollector {
private:
    static set<string> allLibrariesEverSeen;  // 累积收集的库
    
public:
    static void collectCurrentLibraries();    // 收集当前库状态
    static void printFinalReport();           // 打印累积报告
    static void reset();                      // 重置收集器
};
```

#### 2. 修改测试流程
```cpp
// 之前：预初始化ldfcr引擎
// 现在：不预初始化，让测试用例自己触发库加载

// 1. 收集基准状态（测试前）
SimpleLibraryCollector::collectCurrentLibraries();

// 2. 运行所有测试
RUN_ALL_TESTS();

// 3. 收集测试后状态
SimpleLibraryCollector::collectCurrentLibraries();

// 4. 打印累积报告
SimpleLibraryCollector::printFinalReport();
```

## 输出说明

### 新的输出格式
```
=== Starting Simple Library Collection (No Pre-initialization) ===
Collecting baseline libraries (before any tests)...
Collecting libraries... Found X libraries

[测试执行过程...]

Collecting libraries after all tests completed...
Collecting libraries... Found Y libraries

=== Cumulative Library Usage Report (Simple Method) ===
Total unique libraries encountered during testing: Z
1. LibraryName1 build version x.x.x.x on date
2. LibraryName2 no version number has been established
...
=== End of Cumulative Report ===
```

### 数据含义
- **基准状态库数量**：测试前系统中的ldfcr相关库（应该很少）
- **测试后库数量**：测试执行后加载的库
- **累积库总数**：整个测试过程中遇到的所有库（去重后）
- **库版本信息**：每个库的详细版本信息

## 预期效果

### 改动前
```
开头：16个库（预加载的所有库）
结尾：7个库（残留的库）
问题：无法知道哪些是测试真正用的
```

### 改动后
```
基准：0-2个库（系统基础库）
测试后：X个库（测试真正使用的库）
累积：Y个库（真正的测试覆盖度）
```

## 使用方法

### 编译
```bash
make ldfcr_gtest_advance
```

### 运行
```bash
./ldfcr_gtest_advance
```

### 分析结果
1. **查看基准状态**：确认测试前没有预加载库
2. **查看累积报告**：了解测试真正覆盖的库
3. **对比当前状态**：了解测试结束后的库状态

## 技术细节

### 核心机制
- 通过读取`/proc/self/maps`获取进程内存映射
- 过滤ldfcr相关的共享库文件
- 使用`set<string>`自动去重
- 累积收集，避免遗漏被卸载的库

### 关键函数
- `getLoadedLibraries()`：获取当前加载的库列表
- `isLdfcrRelatedLibrary()`：判断是否为ldfcr相关库
- `getSensitiveSoVersionPublic()`：获取库版本信息

### 性能考虑
- 只在测试前后各检测一次，性能开销最小
- 使用静态成员变量，避免重复创建对象

## 局限性

### 可能丢失的数据
- 在测试过程中被加载又被卸载的库
- 某些按需动态加载的临时库

### 不适用场景
- 需要实时监控库加载状态的场景
- 需要知道具体哪个测试用例使用了哪个库的场景

## 后续扩展

### 方案2：完整跟踪
如果需要更完整的数据，可以实现：
- 每个测试用例前后都检测
- 使用Google Test监听器
- 记录测试用例与库的对应关系

### 方案3：配置文件分析
- 分析各个.data配置文件
- 静态分析库依赖关系
- 更准确的功能覆盖度分析

## 维护说明

### 文件修改
- `libaryVersionInfo.h`：新增SimpleLibraryCollector类
- `main.cpp`：修改测试流程，移除预初始化

### 注意事项
- 不要在测试前初始化ldfcr引擎
- 保持SimpleLibraryCollector的静态成员定义
- 确保在Linux环境下运行（依赖/proc/self/maps）

---
**文档版本**：v1.0  
**创建时间**：2025-01-16  
**作者**：暴躁老哥  
**适用版本**：ldfcr_gtest_advance
