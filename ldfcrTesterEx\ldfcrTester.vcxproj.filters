﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <Filter Include="源文件">
      <UniqueIdentifier>{4FC737F1-C7A5-4376-A066-2A32D752A2FF}</UniqueIdentifier>
      <Extensions>cpp;c;cc;cxx;def;odl;idl;hpj;bat;asm;asmx</Extensions>
    </Filter>
    <Filter Include="头文件">
      <UniqueIdentifier>{93995380-89BD-4b04-88EB-625FBE52EBFB}</UniqueIdentifier>
      <Extensions>h;hh;hpp;hxx;hm;inl;inc;xsd</Extensions>
    </Filter>
    <Filter Include="资源文件">
      <UniqueIdentifier>{67DA6AB6-F800-4c08-8B7A-83BB121AAD01}</UniqueIdentifier>
      <Extensions>rc;ico;cur;bmp;dlg;rc2;rct;bin;rgs;gif;jpg;jpeg;jpe;resx;tiff;tif;png;wav;mfcribbon-ms</Extensions>
    </Filter>
    <Filter Include="ui">
      <UniqueIdentifier>{91455e8b-1339-4d6c-865b-496e44c4068d}</UniqueIdentifier>
    </Filter>
    <Filter Include="thread">
      <UniqueIdentifier>{a37e1625-7acd-4f56-986c-c096fb45d0c1}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
  <ItemGroup>
    <Text Include="ReadMe.txt" />
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="ldfcrTester.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="ldfcrTesterDlg.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="stdafx.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="targetver.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="Resource.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="Mode_P_Dialog.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="CldfcrLog.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="CodeT.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="viewStgy.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="DragEdit.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="InPutCheckTextDlg.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="PublicFunction.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="ui\XFileOpenListView.h">
      <Filter>ui</Filter>
    </ClInclude>
    <ClInclude Include="ui\XFolderDialog.h">
      <Filter>ui</Filter>
    </ClInclude>
    <ClInclude Include="ui\XFolderDialogRes.h">
      <Filter>ui</Filter>
    </ClInclude>
    <ClInclude Include="ui\XHistoryCombo.h">
      <Filter>ui</Filter>
    </ClInclude>
    <ClInclude Include="ui\XWinVer.h">
      <Filter>ui</Filter>
    </ClInclude>
    <ClInclude Include="thread\SafeQueue.h">
      <Filter>thread</Filter>
    </ClInclude>
    <ClInclude Include="thread\ThreadPool.h">
      <Filter>thread</Filter>
    </ClInclude>
    <ClInclude Include="DirNode.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="Setting_Dialog.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="ConfigLink.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="StrategyDlg.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="IniSetting.h">
      <Filter>头文件</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="ldfcrTester.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="ldfcrTesterDlg.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="stdafx.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="Mode_P_Dialog.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="CldfcrLog.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="CodeT.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="viewStgy.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="DragEdit.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="InPutCheckTextDlg.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="PublicFunction.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="ui\XFileOpenListView.cpp">
      <Filter>ui</Filter>
    </ClCompile>
    <ClCompile Include="ui\XFolderDialog.cpp">
      <Filter>ui</Filter>
    </ClCompile>
    <ClCompile Include="ui\XHistoryCombo.cpp">
      <Filter>ui</Filter>
    </ClCompile>
    <ClCompile Include="ui\XWinVer.cpp">
      <Filter>ui</Filter>
    </ClCompile>
    <ClCompile Include="Setting_Dialog.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="DirNode.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="ConfigLink.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="StrategyDlg.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="IniSetting.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ResourceCompile Include="ldfcrTester.rc">
      <Filter>资源文件</Filter>
    </ResourceCompile>
    <ResourceCompile Include="ui\XFolderDialog.rc">
      <Filter>ui</Filter>
    </ResourceCompile>
  </ItemGroup>
  <ItemGroup>
    <None Include="res\ldfcrTester.rc2">
      <Filter>资源文件</Filter>
    </None>
    <None Include="ui\XFolderDialog.aps">
      <Filter>ui</Filter>
    </None>
  </ItemGroup>
  <ItemGroup>
    <Image Include="res\ldfcrTester.ico">
      <Filter>资源文件</Filter>
    </Image>
  </ItemGroup>
</Project>