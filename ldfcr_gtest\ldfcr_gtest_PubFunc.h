#pragma once
#include <iostream>
#include <string>
#include <vector>

#ifdef WIN32
#include <windows.h>
#include "trcrt.h"
#include "UnicodeString.h"
#else
#include <sys/types.h>
#include <sys/stat.h>  
#include <unistd.h>
#endif

/*******************************************************************************************/
//									   Base64功能函数										//
/*******************************************************************************************/
// 检查字符是否为 Base64 字符
inline bool is_base64(unsigned char c) {
	return (isalnum(c) || (c == '+') || (c == '/'));
}

// Base64 解码函数
inline std::string Base64Decode(const std::string &encoded_string) {
	// Base64 编码字符集
	static const std::string base64_chars =
		"ABCDEFGHIJKLMNOPQRSTUVWXYZ"
		"abcdefghijklmnopqrstuvwxyz"
		"0123456789+/";
	int in_len = encoded_string.size();
	int i = 0;
	int j = 0;
	int in_ = 0;
	unsigned char char_array_4[4], char_array_3[3];
	std::string ret;
	while (in_len-- && (encoded_string[in_] != '=') && is_base64(encoded_string[in_])) {
		char_array_4[i++] = encoded_string[in_]; in_++;
		if (i == 4) {
			for (i = 0; i < 4; i++)
				char_array_4[i] = base64_chars.find(char_array_4[i]);
			char_array_3[0] = (char_array_4[0] << 2) + ((char_array_4[1] & 0x30) >> 4);
			char_array_3[1] = ((char_array_4[1] & 0xf) << 4) + ((char_array_4[2] & 0x3c) >> 2);
			char_array_3[2] = ((char_array_4[2] & 0x3) << 6) + char_array_4[3];
			for (i = 0; (i < 3); i++)
				ret += char_array_3[i];
			i = 0;
		}
	}
	if (i) {
		for (j = i; j < 4; j++)
			char_array_4[j] = 0;
		for (j = 0; j < 4; j++)
			char_array_4[j] = base64_chars.find(char_array_4[j]);
		char_array_3[0] = (char_array_4[0] << 2) + ((char_array_4[1] & 0x30) >> 4);
		char_array_3[1] = ((char_array_4[1] & 0xf) << 4) + ((char_array_4[2] & 0x3c) >> 2);
		char_array_3[2] = ((char_array_4[2] & 0x3) << 6) + char_array_4[3];
		for (j = 0; (j < i - 1); j++) ret += char_array_3[j];
	}
	return ret;
}

/*******************************************************************************************/
//								    字符编码转换功能函数							    	//
/*******************************************************************************************/
#ifdef WIN32
inline std::wstring str2ws(const char *ws, size_t iLen)
{
	if (ws == NULL)
		return L"";
	//trcrt::str::SafeString  strategy_sstr;
	//trcrt::str::tr_str_initString(&strategy_sstr);
	tr::str::CUnicodeString strategy_sstr;
	strategy_sstr.convertFromUtf8(ws);
	//strategy_sstr->set_fromUTF8(
	//    trcrt::str::IString::UTF16,
	//    ws, iLen
	//    );
	return std::wstring(strategy_sstr.wstr());
}

inline std::string WstringToString(const std::wstring v_wstr)
{// wstring转string
	unsigned len = v_wstr.size() * 4;
	setlocale(LC_CTYPE, "");
	char *p = new char[len];
	wcstombs(p, v_wstr.c_str(), len);
	std::string str1(p);
	delete[] p;
	return str1;
}

inline std::wstring StringToWstring(const std::string v_str)
{// string转wstring
	unsigned len = v_str.size() * 2;// 预留字节数
	setlocale(LC_CTYPE, "");     //必须调用此函数
	wchar_t *p = new wchar_t[len];// 申请一段内存存放转换后的字符串
	mbstowcs(p, v_str.c_str(), len);// 转换
	std::wstring str1(p);
	delete[] p;// 释放申请的内存
	return str1;
}
#endif

#ifdef WIN32
inline std::string PublicUnicodeToUTF8(const WCHAR * _from_str)
{

	std::string result;

	int utf8_len = WideCharToMultiByte(CP_UTF8, 0,
		_from_str, -1, //此处的-1表示一直转换到NULL结尾字符，相应地，utf8_len也会包含一个0结尾字符
		NULL, 0,		   //不指定输出缓冲区，表示是先计算所需缓冲区大小
		NULL, NULL
	);
	if (0 < utf8_len)
	{
		char *to_str = new(std::nothrow) char[utf8_len + 64]; //多分配一些余地
		if (NULL != to_str)
		{
			utf8_len = WideCharToMultiByte(CP_UTF8, 0, _from_str, -1, to_str, utf8_len + 64, NULL, NULL);
			utf8_len--;    //去除NULL结尾字符所占的一个字节数
			to_str[utf8_len] = 0; //这里必须补结尾符
			result.assign(to_str);
			delete[] to_str;
		}
	}
	return result;

}
#endif

#ifdef WIN32
inline std::string WstringToUtf8(const std::wstring &wstr)
{// wstring 转 UTF-8 
	if (wstr.empty()) return std::string();
	int size_needed = WideCharToMultiByte(CP_UTF8, 0, &wstr[0], (int)wstr.size(), NULL, 0, NULL, NULL);
	std::string strTo(size_needed, 0);
	WideCharToMultiByte(CP_UTF8, 0, &wstr[0], (int)wstr.size(), &strTo[0], size_needed, NULL, NULL);
	return strTo;
}
#else
inline std::string WstringToUtf8(const std::wstring &wstr)
{
	if (wstr.empty()) return std::string();

	std::string result;
	for (wchar_t wc : wstr) {
		if (wc <= 0x7F) {
			result.push_back(static_cast<char>(wc)); // 单字节字符 (ASCII)
		}
		else if (wc <= 0x7FF) {
			result.push_back(static_cast<char>(0xC0 | ((wc >> 6) & 0x1F))); // 第一字节
			result.push_back(static_cast<char>(0x80 | (wc & 0x3F)));         // 第二字节
		}
		else if (wc <= 0xFFFF) {
			result.push_back(static_cast<char>(0xE0 | ((wc >> 12) & 0x0F))); // 第一字节
			result.push_back(static_cast<char>(0x80 | ((wc >> 6) & 0x3F)));  // 第二字节
			result.push_back(static_cast<char>(0x80 | (wc & 0x3F)));         // 第三字节
		}
		else if (wc <= 0x10FFFF) {
			result.push_back(static_cast<char>(0xF0 | ((wc >> 18) & 0x07))); // 第一字节
			result.push_back(static_cast<char>(0x80 | ((wc >> 12) & 0x3F))); // 第二字节
			result.push_back(static_cast<char>(0x80 | ((wc >> 6) & 0x3F)));  // 第三字节
			result.push_back(static_cast<char>(0x80 | (wc & 0x3F)));         // 第四字节
		}
	}
	return result;
}
#endif

#ifdef WIN32
inline std::string Utf8ToGbk(const char *src_str)
{
	int len = MultiByteToWideChar(CP_UTF8, 0, src_str, -1, NULL, 0);
	wchar_t* wszGBK = new wchar_t[len + 1];
	memset(wszGBK, 0, len * 2 + 2);
	MultiByteToWideChar(CP_UTF8, 0, src_str, -1, wszGBK, len);
	len = WideCharToMultiByte(CP_ACP, 0, wszGBK, -1, NULL, 0, NULL, NULL);
	char* szGBK = new char[len + 1];
	memset(szGBK, 0, len + 1);
	WideCharToMultiByte(CP_ACP, 0, wszGBK, -1, szGBK, len, NULL, NULL);
	std::string strTemp(szGBK);

	if (wszGBK)
		delete[] wszGBK;

	if (szGBK)
		delete[] szGBK;
	return strTemp;
}

inline std::wstring Utf8ToWstring(const std::string &utf8_str)
{//UTF-8转wstring
	int size_needed = MultiByteToWideChar(CP_UTF8, 0, &utf8_str[0], (int)utf8_str.size(), NULL, 0);
	std::wstring wstrTo(size_needed, 0);
	MultiByteToWideChar(CP_UTF8, 0, &utf8_str[0], (int)utf8_str.size(), &wstrTo[0], size_needed);
	return wstrTo;
}

#endif

// 计数字节的开头位为1的数目
// count first 6 bits
static int NumLeadingBitsSet(char c)
{
	int i = 7, count = 0;
	while (i > 1 && c&(1 << i))
	{
		++count;
		--i;
	}
	return count;
}

// 判断字节是否是UTF-8字符的结尾字节
static bool IsUTF8EndByte(const char *start, const char *p)
{
	if (isascii(*p))
		return true;

	// begining byte 0x40 == 1 << 6, 左起第二位是否为1
	if ((*p & 0x40) == 0x40)
		return false;

	int count = 0;
	// 0xBF = 10XXXXXX
	while (p >= start && (*p & 0xBF) == (unsigned char)*p)
	{
		++count;
		--p;
	}
	if (!count)
		return false;

	return count + 1 == NumLeadingBitsSet(*p);
}


/*******************************************************************************************/
//									文件操作功能函数										//
/*******************************************************************************************/
#ifdef _WIN32
inline void getCurAllFiles(std::wstring &v_wstrPath, std::vector<std::wstring>& v_vecFiles)
{
	std::wstring p;
	// 文件句柄
	long long hFile = 0;
	// 文件信息
	struct _wfinddata_t fileinfo;

	if ((hFile = _wfindfirst(p.assign(v_wstrPath).append(L"\\*").c_str(), &fileinfo)) != -1) {
		do {
			if ((wcscmp(fileinfo.name, L".") == 0) || (wcscmp(fileinfo.name, L"..") == 0))  //过滤掉本代表本目录的.和上一级目录的.
			{
				continue;
			}
			// 保存文件的全路径
			v_vecFiles.push_back(p.assign(v_wstrPath).append(L"\\").append(fileinfo.name));

		} while (_wfindnext(hFile, &fileinfo) == 0);  //寻找下一个，成功返回0，否则-1

		_findclose(hFile);
	}
}

// 递归获取所有文件
inline void getAllFilesDFS(const std::wstring &directory, std::vector<std::wstring> &fileList)
{
	std::wstring searchPath = directory + L"\\*";
	WIN32_FIND_DATA findData;
	HANDLE hFind = FindFirstFile(searchPath.c_str(), &findData);
	if (hFind == INVALID_HANDLE_VALUE)
	{
		return;
	}
	do
	{
		const std::wstring fileOrDir = findData.cFileName;
		if (fileOrDir == L"." || fileOrDir == L"..")
		{
			continue;
		}
		std::wstring fullPath = directory + L"\\" + fileOrDir;
		if (findData.dwFileAttributes & FILE_ATTRIBUTE_DIRECTORY)
		{
			getAllFilesDFS(fullPath, fileList);
		}
		else
		{
			fileList.push_back(fullPath);
		}
	} while (FindNextFile(hFind, &findData) != 0);
	FindClose(hFind);
}
#else
inline void getAllFilesDFS(const std::string &directory, std::vector<std::string> &fileList)
{
	DIR *dir = opendir(directory.c_str());
	if (dir == nullptr)
	{
		return;
	}
	struct dirent *entry;
	while ((entry = readdir(dir)) != nullptr)
	{
		std::string fileOrDir = entry->d_name;
		if (fileOrDir == "." || fileOrDir == "..")
		{
			continue;
		}
		std::string fullPath = directory + "/" + fileOrDir;
		struct stat statbuf;
		if (stat(fullPath.c_str(), &statbuf) == -1)
		{
			continue;
		}
		if (S_ISDIR(statbuf.st_mode))
		{
			getAllFilesDFS(fullPath, fileList);
		}
		else
		{
			fileList.push_back(fullPath);
		}
	}
	closedir(dir);
}
#endif

// 修改文件后缀名，附加新的扩展名
#ifdef WIN32
inline std::wstring modifyFileExtension(const std::wstring &filePath, const std::wstring &newExtension) {
	std::wstring newFilePath = filePath + newExtension;
	if (MoveFile(filePath.c_str(), newFilePath.c_str()) == 0) {
		std::wcerr << L"Error renaming file: " << GetLastError() << std::endl;
		return L""; // 空字符串表示失败
	}
	return newFilePath;
}
#else
inline std::string modifyFileExtension(const std::string &filePath, const std::string &newExtension) {
	std::string newFilePath = filePath + newExtension;
	if (rename(filePath.c_str(), newFilePath.c_str()) != 0) {
		std::cerr << "Error renaming file: " << strerror(errno) << std::endl;
		return ""; // 空字符串表示失败
	}
	return newFilePath;
}
#endif

// 恢复文件原始后缀名
#ifdef WIN32
inline bool restoreFileExtension(std::wstring& filePath, const std::wstring& extensionToRemove) {
	size_t pos = filePath.rfind(extensionToRemove);
	if (pos != std::wstring::npos && pos == (filePath.length() - extensionToRemove.length())) {
		std::wstring originalFilePath = filePath.substr(0, pos);
		if (MoveFile(filePath.c_str(), originalFilePath.c_str())) {
			filePath = originalFilePath;
			return true;
		}
		else
		{
			std::wcerr << L"Error renaming file: " << GetLastError() << std::endl;
			return false;
		}
	}
	return false;
}
#else
inline bool restoreFileExtension(std::string& filePath, const std::string& extensionToRemove) {
	size_t pos = filePath.rfind(extensionToRemove);
	if (pos != std::string::npos && pos == (filePath.length() - extensionToRemove.length())) {
		std::string originalFilePath = filePath.substr(0, pos);

		if (rename(filePath.c_str(), originalFilePath.c_str()) == 0) {
			filePath = originalFilePath;
			return true;
		}
		else
		{
			std::cerr << "Error renaming file: " << strerror(errno) << std::endl;
			return false;
		}
	}
	return false;
}
#endif


#ifdef __GNUC__
inline std::string GetCurPath() //linux下获取文件当前路径
{
	const int MAXPATH = 250;
	char buffer[MAXPATH];
	getcwd(buffer, MAXPATH);
	std::string strPath = buffer;
	strPath += "/";
	return strPath;
}

inline std::vector<std::string> read_dir(const char *dir)
{
	std::string str_filePath = dir;
	std::string m_strTestFilePath = GetCurPath() + str_filePath;
	//printf("%s\n",m_strTestFilePath.c_str());
	std::vector<std::string> fvec;
	DIR *dh = opendir(m_strTestFilePath.c_str());
	if (NULL == dh) {
		return fvec;
	}

	std::string path = m_strTestFilePath;
	if (path[path.size() - 1] != '/')
		path += "/";
	struct dirent *dirp;
	while ((dirp = readdir(dh)) != NULL) {
		if (dirp->d_type & DT_REG) {
			fvec.push_back(str_filePath + "/" + dirp->d_name);
		}
	}

	closedir(dh);

	return fvec;
}


#endif


#ifdef WIN32

inline std::vector<std::wstring> read_dir(const TCHAR *dir)
{
	TCHAR szDir[MAX_PATH];
	WIN32_FIND_DATA ffd;
	LARGE_INTEGER filesize;
	DWORD dwError = 0;
	HANDLE hFind = INVALID_HANDLE_VALUE;
	std::vector<std::wstring> fvec;

	WCHAR wcsAppDir[MAX_PATH];
	::GetModuleFileNameW(NULL, wcsAppDir, sizeof(wcsAppDir) / sizeof(WCHAR));
	int  iLen = ::wcslen(wcsAppDir);
	while (0 < iLen)
	{
		if (L'\\' == wcsAppDir[iLen - 1]) {
			wcsAppDir[iLen - 1] = 0;
			break;
		}
		iLen--;
	}

	std::wstring m_strStrategyFilePath = wcsAppDir;    //构造策略文件的路径
	m_strStrategyFilePath += L'\\';
	m_strStrategyFilePath += dir;

	for (size_t i = 0; i < m_strStrategyFilePath.size(); ++i)
	{
		if (m_strStrategyFilePath[i] == L'/')
			m_strStrategyFilePath[i] = L'\\';
	}
	if (m_strStrategyFilePath[m_strStrategyFilePath.size() - 1] != L'\\')
	{
		m_strStrategyFilePath += L'\\';
	}

	// wprintf(TEXT("open dir %s  w\n"), m_strStrategyFilePath.c_str());

	wcscpy(szDir, m_strStrategyFilePath.c_str());
	wcscat(szDir, TEXT("*"));

	hFind = FindFirstFile(szDir, &ffd);
	if (INVALID_HANDLE_VALUE == hFind)
	{
		return std::vector<std::wstring>();
	}
	do
	{
		if (ffd.dwFileAttributes & FILE_ATTRIBUTE_DIRECTORY)
		{
			// wprintf(TEXT("  %s   <DIR>\n"), ffd.cFileName);
		}
		else
		{
			filesize.LowPart = ffd.nFileSizeLow;
			filesize.HighPart = ffd.nFileSizeHigh;
			// wprintf(TEXT("  %s   %ld bytes\n"), ffd.cFileName, filesize.QuadPart);

			TCHAR szFullPath[MAX_PATH];
			wcscpy(szFullPath, m_strStrategyFilePath.c_str());
			//StringCchCat(szFullPath, MAX_PATH, TEXT("\\"));
			wcscat(szFullPath, ffd.cFileName);
			fvec.push_back(szFullPath);
		}
	} while (FindNextFile(hFind, &ffd) != 0);

	FindClose(hFind);
	return fvec;
}

// 获取文件内容
static char* getFileContent(const WCHAR *filePath, long *psize)
{
	if (filePath == NULL)
		return NULL;
	std::wstring m_strStrategyFilePath;
	WCHAR c = tolower(filePath[0]);
	if (c >= L'a' && c <= L'z' && filePath[1] == L':')
	{
	}
	else
	{
		WCHAR wcsAppDir[MAX_PATH];
		::GetModuleFileNameW(NULL, wcsAppDir, sizeof(wcsAppDir) / sizeof(WCHAR));
		int  iLen = ::wcslen(wcsAppDir);
		while (0 < iLen)
		{
			if (L'\\' == wcsAppDir[iLen - 1]) {
				wcsAppDir[iLen - 1] = 0;
				break;
			}
			iLen--;
		}

		m_strStrategyFilePath = wcsAppDir;
		m_strStrategyFilePath += L'\\';
	}

	m_strStrategyFilePath += filePath;

	size_t i = 0;
	while (i < m_strStrategyFilePath.size())
	{
		if (L'/' == m_strStrategyFilePath[i])
		{
			m_strStrategyFilePath[i] = L'\\';
		}
		++i;
	}

	FILE *fp = NULL;
	_wfopen_s(&fp, m_strStrategyFilePath.c_str(), L"rb");
	if(!fp)
	      return NULL;

	fseek(fp, 0, SEEK_END);
	long size = ftell(fp);
	fseek(fp, 0, SEEK_SET);

	char *stra_content = new char[size + 1];
	memset(stra_content, 0, size + 1);

	size_t nret = fread(stra_content, 1, size, fp);
	EXPECT_EQ(nret, (size_t)size);
	//EXPECT_NE(psize, NULL);
	*psize = size;

	fclose(fp);
	return stra_content;
}
#else
// 获取文件内容
static char* getFileContent(const char *filePath, long *psize)
{
	
	//std::string str_filePath = filePath;
	//std::string m_strTestFilePath = GetCurPath() + str_filePath;
	//printf("path is %s\n",filePath);
	FILE *fp = fopen(filePath, "r");
	if (!fp)
	{
		return nullptr;
	}

	fseek(fp, 0, SEEK_END);
	long size = ftell(fp);
	fseek(fp, 0, SEEK_SET);

	char *stra_content = new char[size + 1];
	memset(stra_content, 0, size + 1);

	size_t nret = fread(stra_content, 1, size, fp);

	EXPECT_EQ(nret, (size_t)size);
	*psize = size;

	fclose(fp);
	return stra_content;
}

#endif

//获取文件扩展名
#ifdef WIN32
inline std::wstring getFileExtension(const std::wstring &filePath)
{
	size_t pos = filePath.find_last_of(L".");
	if (pos == std::wstring::npos)
	{
		return L""; // 没有扩展名
	}
	return filePath.substr(pos + 1);
}
#else
inline std::string getFileExtension(const std::string &filePath)
{
	size_t pos = filePath.find_last_of(".");
	if (pos == std::string::npos)
	{
		return ""; // 没有扩展名
	}
	return filePath.substr(pos + 1);
}
#endif

#ifdef WIN32
//获取WString类型的文件路径
inline void getWStringFilePath(const TCHAR *path, std::wstring& m_wstrTestFilePath)
{
	std::wstring wstrPath(path);
	if (path[1] != L':')
	{
		wchar_t wcsAppDir[MAX_PATH];
		::GetModuleFileNameW(NULL, wcsAppDir, sizeof(wcsAppDir) / sizeof(wchar_t));
		int  iLen = ::wcslen(wcsAppDir);
		while (0 < iLen)
		{
			if (L'\\' == wcsAppDir[iLen - 1]) {
				wcsAppDir[iLen - 1] = 0;
				break;
			}
			iLen--;
		}

		m_wstrTestFilePath = std::wstring(wcsAppDir) + L'\\' + wstrPath;
	}
	else {
		m_wstrTestFilePath = wstrPath;
	}
}

//获取String类型的文件路径
inline void getStringFilePath(const TCHAR *path, std::string& m_strTestFilePath)
{
	std::wstring wsPath;
	getWStringFilePath(path, wsPath);
	m_strTestFilePath = WstringToUtf8(wsPath);
}

#else

inline void getStringFilePath(const char *path, std::string& m_strTestFilePath)
{
	std::string str_file = path;
	m_strTestFilePath = GetCurPath() + str_file;
}
#endif

/*
获取策略完整路径  20230810
wchar * 改为char* 适配linux 2023 8 15
*/
inline std::string GetCompletePath(const char* v_subPath)
{
#ifdef WIN32
	wchar_t temp[MAX_PATH] = {};
	GetModuleFileNameW(GetModuleHandle(NULL), temp, MAX_PATH);
	std::wstring parentPathW = temp;
	size_t lastSlashPos = parentPathW.find_last_of(L'\\');
	if (lastSlashPos != std::wstring::npos)
	{
		parentPathW.erase(lastSlashPos); // 删除最后一个文件名及其之后的部分
	}
	parentPathW += StringToWstring(v_subPath);
	return WstringToString(parentPathW);
#elif __GNUC__
	const int MAXPATH = 250;
	char buffer[MAXPATH];
	getcwd(buffer, MAXPATH);
	std::string strPath = buffer;
	//确保路径以斜杠结尾
	if (strPath.back() != '/')
	{
		strPath += '/';
	}
	strPath += v_subPath;//拼接子路径
	return strPath;
#endif
}

#ifdef WIN32

//创建一个501MB文件
inline bool createMBEmptyFile(const TCHAR *path,int sizeM)
{
	if (path == NULL)
	{
		return false;
	}

	std::wstring m_strTestFilePath;
	getWStringFilePath(path, m_strTestFilePath);

	FILE * fp = _wfopen(m_strTestFilePath.c_str(), L"r+");
	if (fp == NULL)
	{
		return false;
	}

	char* cTemp = new char[1024 * 1024];
	memset(cTemp, 0x31, 1024 * 1024);
	for (int i = 0; i < sizeM; ++i)
	{
		fwrite(cTemp, strlen(cTemp), 1, fp);
	}
	delete[] cTemp;
	fclose(fp);
	return true;

}

//创建201M文件
inline bool createMBCnFile(const TCHAR *path, int sizeM)
{
	if (path == NULL)
	{
		return false;
	}
	std::wstring m_strTestFilePath;
	getWStringFilePath(path, m_strTestFilePath);

	FILE * fp = _wfopen(m_strTestFilePath.c_str(), L"r+");
	if (fp == NULL)
	{
		return false;
	}

	char* cTemp = new char[1024 * 1024];
	memset(cTemp, 0, 1024 * 1024);

	std::string cnWord("测试");
	size_t size = 1024 * 1024 - 1;

	for (int i = 0; i < size; i++)
	{
		if((i%cnWord.size()==0)&& (size-i < cnWord.size()))
			break;
		cTemp[i] = cnWord.at(i%cnWord.size());
	}

	for (int i = 0; i < sizeM+1; ++i)
	{
		fwrite(cTemp, strlen(cTemp), 1, fp);
	}
	delete[] cTemp;
	fclose(fp);
	return true;

}

//清空文件
inline bool deleteEmptyFile(const TCHAR *path)
{
	if (path == NULL)
	{
		return false;
	}
	std::wstring m_strTestFilePath;
	getWStringFilePath(path, m_strTestFilePath);

	FILE * fp = _wfopen(m_strTestFilePath.c_str(), L"w+");
	if (fp == NULL)
		return false;
	fclose(fp);
	return true;
}

#else

//创建一个501MB文件
inline bool createMBEmptyFile(const char *path, int sizeM)
{
	if (path == NULL)
	{
		return false;
	}

	std::string m_strTestFilePath;
	getStringFilePath(path, m_strTestFilePath);

	FILE * fp = fopen(m_strTestFilePath.c_str(), "w+");
	if (fp == NULL)
	{
		return false;
	}

	char* cTemp = new char[1024 * 1024];
	memset(cTemp, 0x31, 1024 * 1024);
	for (int i = 0; i < sizeM; ++i)
	{
		fwrite(cTemp, strlen(cTemp), 1, fp);
	}
	delete[] cTemp;
	fclose(fp);
	return true;

}

//创建201M文件
inline bool createMBCnFile(const char *path, int sizeM)
{
	if (path == NULL)
	{
		return false;
	}
	std::string m_strTestFilePath;
	getStringFilePath(path, m_strTestFilePath);

	FILE * fp = fopen(m_strTestFilePath.c_str(), "w+");
	if (fp == NULL)
	{
		return false;
	}

	char* cTemp = new char[1024 * 1024];
	memset(cTemp, 0, 1024 * 1024);

	std::string cnWord("测试");
	int size = 1024 * 1024 - 1;

	for (int i = 0; i < size; i++)
	{
		if ((i%cnWord.size() == 0) && (size - i < cnWord.size()))
			break;
		cTemp[i] = cnWord.at(i%cnWord.size());
	}

	for (int i = 0; i < sizeM + 1; ++i)
	{
		fwrite(cTemp, strlen(cTemp), 1, fp);
	}
	delete[] cTemp;
	fclose(fp);
	return true;

}

//清空文件
inline bool deleteEmptyFile(const char *path)
{
	if (path == NULL)
	{
		return false;
	}
	std::string m_strTestFilePath;
	getStringFilePath(path, m_strTestFilePath);

	FILE * fp = fopen(m_strTestFilePath.c_str(), "w+");
	if (fp == NULL)
		return false;
	fclose(fp);
	return true;
}

#endif

/*******************************************************************************************/
//									时间操作功能函数										//
/*******************************************************************************************/
//获取当前时间
#ifdef WIN32
inline std::wstring getCurrentTimeString()
{
	std::time_t now = std::time(nullptr);
	wchar_t buf[20];
	std::wcsftime(buf, sizeof(buf) / sizeof(wchar_t), L"%Y-%m-%d %H:%M:%S", std::localtime(&now));
	return std::wstring(buf);
}
#else
inline std::string getCurrentTimeString() {
	auto now = std::chrono::system_clock::now();
	std::time_t now_time = std::chrono::system_clock::to_time_t(now);
	char buf[20];
	std::strftime(buf, sizeof(buf), "%Y-%m-%d %H:%M:%S", std::localtime(&now_time));
	return std::string(buf);
}
#endif

//睡眠
inline void LdfcrSleep(int sec)
{
#ifdef __GNUC__
	sleep(sec);
#else
	Sleep(sec * 1000);
#endif
}

/*******************************************************************************************/
//										系统操作功能函数									//
/*******************************************************************************************/
//判断系统版本是不是windwosXP
inline bool operSysJugXP()
{
	bool XP = false;
#ifdef WIN32
	OSVERSIONINFO osversion;
	ZeroMemory(&osversion, sizeof(OSVERSIONINFO));
	osversion.dwOSVersionInfoSize = sizeof(OSVERSIONINFO);
	GetVersionEx(&osversion);
	if (osversion.dwMajorVersion == 5 && osversion.dwMinorVersion == 1) // Windows XP是版本5.1
	{
		XP = true;
	}
#endif
	return XP;
}

//判断系统是不是Linux 32位系统
inline bool operSysJugLinux32Bit()
{
#ifdef __linux__  
	if (sizeof(void*) == 4) {
		return true; // 是Linux 32位  
	}
	else {
		return false;
	}
#else   
	return false;
#endif  
}

// 获取当前操作系统信息
inline std::string getOS() {
	std::string osType;
#if defined(_WIN32)
	osType = "Windows";
#elif defined(__APPLE__)
	osType = "Mac";
#elif defined(__linux__)
	// 判断具体 Linux 发行版
	std::ifstream osRelease("/etc/os-release");
	if (osRelease) {
		std::string line;
		while (std::getline(osRelease, line)) {
			if (line.find("CentOS Linux 7") != std::string::npos) {
				osType = "CentOS 7";
				break;
			}
			else if (line.find("Ubuntu") != std::string::npos) {
				osType = "Ubuntu";
				break;
			}
			else if (line.find("Debian") != std::string::npos) {
				osType = "Debian";
				break;
			}
		}
	}
	else {
		// 检查是否是 CentOS 6 系统
		std::ifstream centosRelease("/etc/centos-release");
		if (centosRelease) {
			std::string line;
			std::getline(centosRelease, line);
			if (line.find("CentOS release 6") != std::string::npos) {
				osType = "CentOS 6";
			}
			else if (line.find("CentOS Linux release 7") != std::string::npos) {
				osType = "CentOS 7";
			}
		}
	}
	// 如果未确定具体发行版，则标记为通用 Linux
	if (osType.empty()) {
		osType = "Unknown Linux";
	}
#else
	osType = "Unknown OS";
#endif
	// 进一步判断架构
#if defined(__x86_64__) || defined(_M_X64)
	osType += " (x86_64)";
#elif defined(__i386__) || defined(_M_IX86)
	osType += " (x86)";
#elif defined(__aarch64__)
	osType += " (aarch64)";
#endif
	return osType;
}
