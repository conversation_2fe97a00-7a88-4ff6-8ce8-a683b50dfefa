#pragma once
#include "afxcmn.h"


// CldfcrLog 对话框

class CldfcrLog : public CDialog
{
	DECLARE_DYNAMIC(CldfcrLog)

public:
	CldfcrLog(CWnd* pParent = NULL);   // 标准构造函数
	CldfcrLog(CString v_cstr,CWnd* pParent = NULL);   // 标准构造函数
	virtual ~CldfcrLog();

// 对话框数据
#ifdef AFX_DESIGN_TIME
	enum { IDD = IDD_DIALOG1 };
#endif

protected:
	virtual void DoDataExchange(CDataExchange* pDX);    // DDX/DDV 支持

	DECLARE_MESSAGE_MAP()
public:
	CEdit m_editLogCtrl;	//日志编辑框
	CString m_showLogPath;	//日志路径
	afx_msg void OnBnClickedLogRefresh();
	afx_msg int CountLogLine();
	afx_msg CString GetProPath();
	virtual BOOL OnInitDialog();
	afx_msg void OnEnChangeEditLog();
};

