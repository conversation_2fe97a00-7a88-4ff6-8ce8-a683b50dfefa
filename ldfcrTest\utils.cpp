#include "utils.h"
#include <fstream>
#include <sstream>
#include <string.h>
#include <sys/stat.h>
#if defined(__GNUC__)
#include <dirent.h>
#include <unistd.h>
#include <stdexcept>
#elif defined(_WIN32)
#include <io.h>
#include <windows.h>
#endif

std::string GetCurPath()
{
	std::string strPath;
#if defined(__GNUC__)
	//linux下获取文件当前路径
	tChar link[100];
	tChar path[100];

	sprintf(link, "/proc/%d/exe", getpid());
	readlink(link, path, sizeof(path));
	strPath = path;
	int npos = strPath.find_last_of("/");
	strPath = strPath.substr(0, npos + 1);//文件路径
#elif defined(_WIN32)
	tChar wcsAppDir[MAX_PATH_LEN];
	::GetModuleFileNameW(NULL, wcsAppDir, sizeof(wcsAppDir) / sizeof(WCHAR));
	int  iLen = ::wcslen(wcsAppDir);
	while (0 < iLen)
	{
		if (L'\\' == wcsAppDir[iLen - 1])
		{
			wcsAppDir[iLen - 1] = 0;
			break;
		}

		iLen--;
	}
	strPath = wstring_to_string(wcsAppDir);
	strPath = strPath + "/";
#endif
	return strPath;
}

#if defined(__GNUC__)
//std::vector<std::string> read_dir(const tChar *dir)
//{
//	std::vector<std::string> fvec;
//	DIR *dh = opendir(dir);
//	if (dh == NULL) {
//		return fvec;
//	}
//
//	std::string path = dir;
//	if (path[path.size() - 1] != '/')
//		path += "/";
//	struct dirent *dirp;
//	while ((dirp = readdir(dh)) != NULL) {
//		if (dirp->d_type & DT_REG) {
//			fvec.push_back(path + dirp->d_name);
//		}
//	}
//	closedir(dh);
//	return fvec;
//}

std::vector<std::string> trave_dir(const tChar * dir)
{
	std::vector<std::string> g_fvec;  //全局文件夹 递归遍历文件使用
	DIR *d = NULL;
	struct dirent *dp = NULL; /* readdir函数的返回值就存放在这个结构体中 */
	struct stat st;
	char p[MAX_PATH_LEN] = { 0 };

	if (stat(dir, &st) < 0 || !S_ISDIR(st.st_mode)) {
		printf("invalid path: %s\n", dir);
		return g_fvec;
	}

	if (!(d = opendir(dir))) {
		printf("opendir[%s] error: %m\n", dir);
		return g_fvec;
	}

	std::string path = dir;
	if (path[path.size() - 1] != '/')
		path += "/";
	while ((dp = readdir(d)) != NULL) {
		/* 把当前目录.，上一级目录..及隐藏文件都去掉，避免死循环遍历目录 */
		if ((!strncmp(dp->d_name, ".", 1)) || (!strncmp(dp->d_name, "..", 2)))
			continue;

		snprintf(p, sizeof(p) - 1, "%s/%s", dir, dp->d_name);
		stat(p, &st);
		if (!S_ISDIR(st.st_mode)) {
			g_fvec.push_back(path + dp->d_name);
		}
		else {
			std::string strDir = p;
			trave_dir(p);
		}
	}
	closedir(d);

	return g_fvec;
}
#endif

std::string ContextRead(const tChar * filePath)
{
	// 原先getline的读取 在TCR模式下 国产环境下读取的文本格式导致SCR解析错误 xiugai 2024 8 9
	std::ifstream ifile(filePath, std::ios::binary); // 以二进制模式打开文件
	if (!ifile) {
		throw std::runtime_error("Could not open file");
	}
	std::ostringstream buf;
	buf << ifile.rdbuf();
	std::string content = buf.str();
	ifile.close();
	return content;
}

void printToGbk(const char * data)
{
#if defined(_WIN32)
	std::string strGbk = UtfToGbk(data);
	printf("%s", strGbk.c_str());
#elif defined(__GNUC__)
	printf("%s", data);
#endif
}

void printToGbkWithN(const char * data)
{
#if defined(_WIN32)
	std::string strGbk = UtfToGbk(data);
	printf("%s\n", strGbk.c_str());
#elif defined(__GNUC__)
	printf("%s\n", data);
#endif
}

std::string charToString(const char * data)
{
	std::string str = data;
	return str;
}

#if defined(_WIN32)
std::wstring p;
std::vector<std::wstring> read_dir(const tChar * dir)
{
	std::vector<std::wstring> m_vecFilePath;
	// 文件句柄
	long long hFile = 0;
	// 文件信息
	struct _wfinddata_t fileinfo;

	if ((hFile = _wfindfirst(p.assign(dir).append(L"\\*").c_str(), &fileinfo)) != -1) {
		do {
			if ((wcscmp(fileinfo.name, L".") == 0) || (wcscmp(fileinfo.name, L"..") == 0))  //过滤掉本代表本目录的.和上一级目录的.
			{
				continue;
			}
			// 保存文件的全路径
			m_vecFilePath.push_back(p.assign(dir).append(L"\\").append(fileinfo.name));

		} while (_wfindnext(hFile, &fileinfo) == 0);  //寻找下一个，成功返回0，否则-1

		_findclose(hFile);
	}
	return m_vecFilePath;
}
std::string wstring_to_string(const wchar_t * data)
{
	std::string res_data;
	int iSize;
	// 宽字符串转换
	iSize = WideCharToMultiByte(CP_ACP, 0, data, -1, NULL, 0, NULL, NULL);
	char* pCapacity = (char*)malloc(iSize * sizeof(char));
	if (pCapacity == NULL) return res_data;
	pCapacity[0] = 0;
	WideCharToMultiByte(CP_ACP, 0, data, -1, pCapacity, iSize, NULL, NULL);
	res_data = pCapacity;
	free(pCapacity);
	return res_data;
}

std::wstring string_to_wstring(const std::string v_str)
{// string转wstring
	unsigned len = v_str.size() * 2;// 预留字节数
	setlocale(LC_CTYPE, "");     //必须调用此函数
	wchar_t *p = new wchar_t[len];// 申请一段内存存放转换后的字符串
	mbstowcs(p, v_str.c_str(), len);// 转换
	std::wstring str1(p);
	delete[] p;// 释放申请的内存
	return str1;
}

std::string UtfToGbk(const std::string v_strUtf)
{
	int len = MultiByteToWideChar(CP_UTF8, 0, v_strUtf.c_str(), -1, NULL, 0);
	wchar_t* wstr = new wchar_t[len + 1];
	memset(wstr, 0, len + 1);
	MultiByteToWideChar(CP_UTF8, 0, v_strUtf.c_str(), -1, wstr, len);
	len = WideCharToMultiByte(CP_ACP, 0, wstr, -1, NULL, 0, NULL, NULL);
	char* str = new char[len + 1];
	memset(str, 0, len + 1);
	WideCharToMultiByte(CP_ACP, 0, wstr, -1, str, len, NULL, NULL);
	if (wstr) delete[] wstr;
	return str;
}
#endif
