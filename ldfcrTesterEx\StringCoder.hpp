﻿//这是个类CStringCoder (StringCoder.h文件)
#pragma once

#include <string.h>
#include <string>
#include <codecvt>
#include <locale>
#if _WIN32
	#include "windows.h"
#else
	#include <iconv.h>
#endif

class CStringCoder
{
private:
#ifdef _WIN32
#else
	/*
	* =====================================================================================
	*  编码方式:
	*  "UTF-8"
	*  "UNICODE
	*  "WINDOWS-1252"
	*   ....
	*
	*  注: 在将"WINDOWS-1252"转"UTF-8"时，有些字符转码报错："Illegal byte sequence"，
	*     原因是"WINDOWS-1252"中有些字符在“UTF-8”中找不到对应字符，如: 0x81, 0x8D, 0x8F, 0x90, 0x9D
	*     解决方法: 调用iconv_open()函数时，加入"//IGNORE"。如： iconv_open("UTF-8//IGNORE", "WINDOWS-1252");
	*
	*  注：其他转码也是相同的解决方法
	* =====================================================================================
	*/

	/*
	* =====================================================================================
	* function:本函数是利用Linux/Mac下的iconv函数族进行转码
	*
	*  @param encTo 目标编码方式 TRANSLIT：遇到无法转换的字符就找相近字符替换
	*                           IGNORE ：遇到无法转换字符跳过
	*  @param encFrom 源编码方式
	*
	*  @inbuf: 需要转换的字符串
	*  @inlen: 需要转换的字符串的长度
	*
	*  @outbuf:存放转换后的字符串
	*  @outlen:存放转换后,outbuf剩余的空间
	* =====================================================================================
	*/
	static bool iconvConvert(const char *encFrom, const char *encTo, const char *inbuf, size_t *inlen, char *outbuf, size_t *outlen)
	{
        if (nullptr == inbuf || nullptr == inlen ||
		        nullptr == outbuf || nullptr == outlen)
		{
			return false;
		}
        iconv_t cd = iconv_open(encTo, encFrom);
		if (cd == (iconv_t)-1)
		{
			perror("iconv_open");
			return false;
		}
		char *tmpin = (char *)inbuf;
		char *tmpout = outbuf;
		size_t ret = iconv(cd, &tmpin, inlen, &tmpout, outlen);
		if (ret == -1)
		{
            switch(errno)
            {
                case  E2BIG:
			perror("iconv");
                      iconv_close(cd);
			return false;
                      break;
                case  EILSEQ:
                      printf("EILSEQ");     //输入中含无效的多字节序列
                      break;
                case  EINVAL:
                      printf("EINVAL");     //输入中以不完整多字节序列作结尾。
                      break;
            }
		}
		iconv_close(cd);
		return true;
	}
#endif

public:

	static void GBKToUTF8(const std::string &strGBK, std::string &strUTF8)
    {
        if (strGBK.empty()) return ;
		std::wstring wstr;
		GBKToUnicode(strGBK, wstr);
		UnicodeToUTF8(wstr, strUTF8);
		return;
	}

	// 多字节转宽字节
	static bool GBKToUnicode(const std::string &str, std::wstring &wstr)
    {
        if (str.empty()) return true;
#ifdef _WIN32
		int nLen = (int)str.length();
		wstr.resize(nLen, L' ');

		int nResult = MultiByteToWideChar(CP_ACP, 0, (LPCSTR)str.c_str(), nLen, (LPWSTR)wstr.c_str(), nLen);
		if (nResult == 0)
		{
			return false;
		}
		wstr.resize(nResult);

		return true;
#else
        size_t out_len = str.size()*sizeof(wchar_t)+4;
		char * out = new char[out_len];
		memset(out, 0x00, out_len);
        size_t in_len = str.size();
        if (iconvConvert("GBK", "UCS-2LE", str.c_str(), &in_len, out, &out_len))
		{
			wstr = (wchar_t *)out;
        }
		delete[] out;
		out = nullptr;
		return true;
#endif
	}

	// 宽字节转多字节
	static bool UnicodeToGBK(const std::wstring &wstr, std::string &str)
    {
        if (wstr.empty()) return true;
#ifdef _WIN32
		int nLen = (int)wstr.length();
		// 有中文时，一个宽字节会转成两个单字节，因此开辟两倍的长度比较保险
		str.resize(nLen * 2, ' ');

		int nResult = WideCharToMultiByte(CP_ACP, 0, (LPCWSTR)wstr.c_str(), nLen, (LPSTR)str.c_str(), nLen * 2, NULL, NULL);
		if (nResult == 0)
		{
			return false;
		}
		str.resize(nResult);
		return true;
#else
        size_t out_len = str.size()*sizeof(wchar_t)+4;
		char * out = new char[out_len];
		memset(out, 0x00, out_len);
		const char * in = (const char *)wstr.c_str();
        size_t in_len = str.size()*sizeof(wchar_t);
        if (iconvConvert("UCS-2LE", "GBK", in, &in_len, out, &out_len))
		{
			str = out;
		}
		delete[] out;
		out = nullptr;
		return true;
#endif
	}

	// 宽字节转UTF-8 （wchar_t * -> char*）
    static bool UnicodeToUTF8(const std::wstring &wstr, std::string &str, bool big_endian = false)
    {
        if (wstr.empty()) return true;
#ifdef _WIN32
		int n = WideCharToMultiByte(CP_UTF8, 0, wstr.c_str(), -1, 0, 0, 0, 0);
		char *pData = new char[n + 1];
		memset(pData, 0, n + 1);

		::WideCharToMultiByte(CP_UTF8, 0, wstr.c_str(), -1, pData, n, 0, 0);
		str = pData;
		if (pData) delete[] pData;
		//std::wstring_convert<std::codecvt_utf8<wchar_t>> utf8_convert;
		//str = utf8_convert.to_bytes(wstr);
		return true;
#else
        std::string endian_type = "UCS-2LE";
        if (big_endian)
        {
            endian_type = "UCS-2BE";
        }
        const char * in = (const char *)wstr.c_str();
        size_t in_len = wstr.size()*sizeof(wchar_t);      // 字节数
        size_t out_len = in_len*4;                       // 确保足够的缓冲
        char * out = new char[out_len+4];
        memset(out, 0x00, out_len+4);
        if (iconvConvert(endian_type.c_str(), "UTF-8", in, &in_len, out, &out_len))
        {
            str = out;
        }
        delete[] out;
        out = nullptr;
        return true;
#endif
	}

    // 宽字节转UTF-8 （char * -> char*）
    static bool UnicodeToUTF8(const std::string &srcstr, std::string &str, bool big_endian = false)
    {
        if (srcstr.empty()) return true;
#ifdef _WIN32
		char *pData = new char[srcstr.size() + 4];
		std::wstring wstr;
		if (NULL != pData)
		{
			memset(pData, 0, srcstr.size() + 4);
			memcpy(pData, srcstr.c_str(), srcstr.size());
			wstr = (wchar_t *)pData;
			delete[] pData;
			pData = NULL;
		}
		else
		{
			return false;
		}

        int n = WideCharToMultiByte(CP_UTF8, 0, wstr.c_str(), -1, 0, 0, 0, 0);
		pData = new char[n + 1];
		if (NULL != pData)
		{
			memset(pData, 0, n + 1);
			::WideCharToMultiByte(CP_UTF8, 0, wstr.c_str(), -1, pData, n, 0, 0);
			str = pData;
			delete[] pData;
			pData = NULL;
		}
		else
		{
			return false;
		}
        //std::wstring_convert<std::codecvt_utf8<wchar_t>> utf8_convert;
        //str = utf8_convert.to_bytes(wstr);
        return true;
#else
        std::string endian_type = "UCS-2LE";
        if (big_endian)
        {
            endian_type = "UCS-2BE";
        }
        const char * in = srcstr.c_str();
        size_t in_len = srcstr.size();                    // 字节数
        size_t out_len = in_len*4;                        // 确保足够的缓冲
        char * out = new char[out_len+4];
        memset(out, 0x00, out_len+4);
        if (iconvConvert(endian_type.c_str(), "UTF-8", in, &in_len, out, &out_len))
        {
            str = out;
        }
        delete[] out;
        out = nullptr;
        return true;
#endif
	}

	// UTF-8转宽字节
	static bool UTF8ToUnicode(const std::string &str, std::wstring &wstr)
	{
        if (str.empty()) return true;
#ifdef _WIN32
		int n = MultiByteToWideChar(CP_UTF8, 0, str.c_str(), -1, NULL, 0);
		wstr.resize(n);
		::MultiByteToWideChar(CP_UTF8, 0, str.c_str(), -1, (LPWSTR)wstr.c_str(), wstr.length());

		//std::wstring_convert<std::codecvt_utf8<wchar_t>> utf8_convert;
		//wstr = utf8_convert.from_bytes(str);
		return true;
#else
        size_t out_len = str.size()*sizeof(wchar_t);
        char * out = new char[out_len];
        memset(out, 0x00, out_len);
        size_t in_len = str.size();
        if (iconvConvert("UTF-8", "UCS-2LE", str.c_str(), &in_len, out, &out_len))
        {
            wstr = (wchar_t *)out;
        }
        delete[] out;
        out = nullptr;
        return true;
#endif
	}
	// 多字节转宽字节
	static bool StringToWString(const std::string &str, std::wstring &wstr)
    {
        if (str.empty()) return true;
#ifdef _WIN32
		int nLen = (int)str.length();
		wstr.resize(nLen, L' ');

		int nResult = MultiByteToWideChar(CP_ACP, 0, (LPCSTR)str.c_str(), nLen, (LPWSTR)wstr.c_str(), nLen);
		if (nResult == 0)
		{
			return false;
		}
		wstr.resize(nResult);
#else
#endif
		return true;
	}

	// 宽字节转多字节
	static bool WStringToString(const std::wstring &wstr, std::string &str)
    {
        if (wstr.empty()) return true;
#ifdef _WIN32
		int nLen = (int)wstr.length();
		// 有中文时，一个宽字节会转成两个单字节，因此开辟两倍的长度比较保险
		str.resize(nLen * 2, ' ');

		int nResult = WideCharToMultiByte(CP_ACP, 0, (LPCWSTR)wstr.c_str(), nLen, (LPSTR)str.c_str(), nLen * 2, NULL, NULL);
		if (nResult == 0)
		{
			return false;
		}
		str.resize(nResult);
#else
#endif
		return true;
	}

	/*******************************************************************************
	*   函数名：
	* 功能简介：将uint16_t的Unicode字符串转换成utf_8
	*     参数：src，Unicode字符串数组，每2个字节为一个字符；size，字节数
	*   返回值：
	*******************************************************************************/
	static const char * uint16ToUtf8(uint16_t* src, uint32_t size, std::string& str_utf8)
	{
		if (sizeof(wchar_t) != sizeof(uint16_t))
		{
			// Linux x64的wchar_t为4个字节，需要将2个字节的Unicode转换为4个字节。
			std::wstring unicode_str;
			wchar_t temp;
			for (uint32_t index = 0; index < size / 2; ++index)
			{
				memset(&temp, 0x00, sizeof(wchar_t));
				memcpy(&temp, (void *)(src + index), 2);
				unicode_str += temp;
			}
			UnicodeToUTF8(unicode_str, str_utf8);
		}
		else
		{
			UnicodeToUTF8((wchar_t *)src, str_utf8);
		}


		return str_utf8.c_str();
	}

    /*******************************************************************************
    *   函数名：
    * 功能简介：将utf_8转换成uint16_t的Unicode字符串
    *     参数：src，utf8字符串数组；dest，uint16_t数组；size，数组元素个数
    *   返回值：unicode字符数，不包括\0
    *******************************************************************************/
    static uint32_t utf8ToUint16(const char * src, uint16_t dest[], uint32_t dest_size)
    {
        std::wstring temp;
        UTF8ToUnicode(src, temp);
        uint32_t character_count = temp.length() - 1;//unicode字符数，不包括\0
        if (sizeof(wchar_t) != sizeof(uint16_t))
        {
            // Linux x64的wchar_t为4个字节，需要将wchar_t高位2个字节的拷贝到dest。
            for (uint32_t index = 0; index < character_count; ++index)
            {
                memcpy(&temp.at(index), (void *)(dest + index), 2);
            }
        }
        else
        {
            memcpy(dest, temp.c_str(), temp.size()*sizeof(wchar_t));
        }
        return character_count;
    }
};