# 全程依赖跟踪实现

## 问题分析

用户发现这些组件：
```
TrCadFilter, TrCompressFilter, TrOLEFilter, TrOOXMLFilter,
TrPdfFilter, TrRtfFilter, TrTextExtractor, TrTxtFilter,
TrArchive, TrODFFilter, DlpOCR, DlpSCR, TrOCRFilter
```

在**整个执行过程中是有依赖的**，但在**程序结束时**已经被释放，所以当前目录扫描找不到。

**需求**：统计整个过程中依赖的组件信息，然后统一输出。

## 解决方案

### 参考Linux/Mac的成功模式

从main.cpp中可以看到Linux/Mac的做法：

```cpp
// Linux/Mac的成功模式
if (ldfcr_InitStartup()) {
    usleep(100000); // 等待所有动态库加载完毕
    
    // 程序启动时
    g_baselineLibVersion = new libaryVersionInfo();
    g_baselineLibVersion->GetVersionInfoByOrder();  // 收集信息
    g_baselineLibVersion->PrintVersionInfo();       // 输出信息
}

// 程序结束时
g_baselineLibVersion->PrintVersionInfo();           // 再次输出相同信息
```

**关键特点：**
1. **在ldfcr_InitStartup()后收集** - 此时所有库都已加载
2. **收集一次，输出多次** - 避免重复扫描，节省CPU
3. **等待加载完毕** - 确保捕获所有动态加载的组件

### Windows的新实现

模仿Linux/Mac的模式，为Windows实现相同的功能：

#### 1. 新增方法（类似Linux/Mac）
```cpp
// PEinfo.h
void GetVersionInfoByOrder();  // 收集组件版本信息（类似Linux/Mac）
void PrintVersionInfo();       // 输出已收集的版本信息（类似Linux/Mac）
```

#### 2. 内部存储容器
```cpp
// PEinfo.h private部分
vector<string> m_collectedComponentInfos;  // 存储格式化的组件信息
vector<string> m_collectedComponentPaths;  // 存储对应的完整路径
vector<string> m_collectedComponentNames;  // 存储组件名称
```

#### 3. 收集逻辑（GetVersionInfoByOrder）
```cpp
void PEinfo::GetVersionInfoByOrder()
{
    // 1. 扫描当前目录，收集所有DLL信息
    // 2. 按预定义顺序匹配组件
    // 3. 将匹配结果存储到内部容器
    // 4. 未找到的组件也要记录（用于显示"no version number"）
}
```

#### 4. 输出逻辑（PrintVersionInfo）
```cpp
void PEinfo::PrintVersionInfo()
{
    // 1. 遍历内部容器
    // 2. 输出找到的组件：序号 + 四列信息 + 完整路径
    // 3. 输出未找到的组件：序号 + "no version number has been established"
}
```

#### 5. main.cpp调用方式
```cpp
// Windows的新调用方式（与Linux/Mac一致）
if (ldfcr_InitStartup()) {
    Sleep(100); // 等待所有动态库加载完毕
    
    // 程序启动时
    printf("\n=== Baseline Libraries (Program Startup) ===\n");
    g_baselinePEinfo = new PEinfo();
    g_baselinePEinfo->GetVersionInfoByOrder();  // 收集信息
    g_baselinePEinfo->PrintVersionInfo();       // 输出信息
}

// 程序结束时
printf("\n=== Baseline Libraries (Program End) ===\n");
g_baselinePEinfo->PrintVersionInfo();           // 再次输出相同信息
```

## 技术优势

### 1. 最佳时机收集 ✅
- **在ldfcr_InitStartup()后收集**：此时所有动态库都已加载到当前目录
- **等待100ms**：确保所有异步加载完成
- **捕获完整依赖**：包括那些后来会被释放的组件

### 2. 高效的CPU使用 ✅
- **收集一次**：只在程序启动时扫描一次文件系统
- **输出多次**：程序开始和结束时都输出相同信息
- **避免重复扫描**：不会在程序结束时再次扫描（此时组件可能已释放）

### 3. 跨平台一致性 ✅
- **相同的方法名**：GetVersionInfoByOrder() 和 PrintVersionInfo()
- **相同的调用流程**：收集一次，输出多次
- **相同的时机**：都在ldfcr_InitStartup()后收集

### 4. 完整的依赖跟踪 ✅
- **捕获全程依赖**：在组件最活跃的时候收集信息
- **包含临时组件**：那些后来被释放的组件也会被记录
- **准确的状态**：反映程序运行期间的真实依赖关系

## 预期效果

### 程序启动时输出
```
=== Baseline Libraries (Program Startup) ===
1. ldfcr.dll             5.2.2507.18   2025-07-23 11:25:03  {cc4452c8...}
   Location: D:\TEST_BAG\Detector_x86Win32\ldfcr.dll
...
9. TrCadFilter.dll       1.2.3.4       2025-07-30 10:15:30  {abcd1234...}  ✅ 被捕获！
   Location: D:\TEST_BAG\Detector_x86Win32\TrCadFilter.dll
10. TrCompressFilter.dll 2.1.5.8       2025-07-30 10:16:45  {efgh5678...}  ✅ 被捕获！
    Location: D:\TEST_BAG\Detector_x86Win32\TrCompressFilter.dll
...
```

### 程序结束时输出
```
=== Baseline Libraries (Program End) ===
1. ldfcr.dll             5.2.2507.18   2025-07-23 11:25:03  {cc4452c8...}
   Location: D:\TEST_BAG\Detector_x86Win32\ldfcr.dll
...
9. TrCadFilter.dll       1.2.3.4       2025-07-30 10:15:30  {abcd1234...}  ✅ 显示相同信息！
   Location: D:\TEST_BAG\Detector_x86Win32\TrCadFilter.dll
10. TrCompressFilter.dll 2.1.5.8       2025-07-30 10:16:45  {efgh5678...}  ✅ 显示相同信息！
    Location: D:\TEST_BAG\Detector_x86Win32\TrCompressFilter.dll
...
```

## 总结

通过模仿Linux/Mac的成功模式，Windows现在能够：

- ✅ **在最佳时机收集**：ldfcr_InitStartup()后，所有组件都已加载
- ✅ **捕获全程依赖**：包括那些后来被释放的临时组件
- ✅ **高效CPU使用**：收集一次，输出多次，避免重复扫描
- ✅ **跨平台一致**：与Linux/Mac使用相同的方法名和调用流程
- ✅ **完整信息显示**：程序开始和结束时显示相同的完整依赖信息

这样就能真正实现"统计整个过程中依赖的组件信息，然后统一输出"的需求！
