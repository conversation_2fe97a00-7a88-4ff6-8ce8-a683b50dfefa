#ifndef MAINWINDOW_H
#define MAINWINDOW_H

#include <QMainWindow>
#include <unistd.h>
#include <sys/stat.h>
#include "../ldfcr/ldfcr.h"

namespace Ui {
class MainWindow;
}

class MainWindow : public QMainWindow
{
    Q_OBJECT

public slots:
    void viewStrategy();
    void updateStrategy();
    void TextTest();
    void selectFile();
    void selectDir();
    void startDetectFile();
    void startDetectDir();

    void thread_detect_file();  //跑文件检测
    void thread_detect_dir();   //跑目录检测

    void recv_sign_done_detect_file(double dwTick,bool bIsSensitive);
    void recv_sign_done_detect_dir(double dwTick);

signals:
    void done_detect_file(double dwTick,bool bIsSensitive);
    void done_detect_dir(double dwTick);

    void textedit_detecting_append(QString file);
    void lineedit_totalcount_set(QString number);
    void lineedit_senscount_set(QString count);

public:
    explicit MainWindow(QWidget *parent = nullptr);
    ~MainWindow();
    
    BOOL dlp_checkSingleFile(const char* file_path);
    void detecting_file();
    void detecting_dir();
    static int fn(const  char *file, const struct stat *st, int flag);
    static std::list<std::string>  filename_list;

    QString m_strStrategyFilePath;
    QString m_strStrategy;
    QString m_strFilePath4FCR;
    int m_iFcrCount;
    QString m_strSingleFcrTime;
    QString m_strFolderPath4Fcr;
    QString m_strFcrFilePaths;
    int m_iFileCount;
    int m_iSensFileCount;

private:
    Ui::MainWindow *ui;
    
    bool Init();
    void SetAllBtn(bool state);
    ILDFcr*   m_pILDFcr;
    BOOL      m_bDoingDirFcr;
};

#endif // MAINWINDOW_H
