@echo off
echo ========================================
echo Windows组件检测功能测试脚本
echo ========================================
echo.

echo 1. 编译项目...
msbuild ldfcr_gtest.sln /p:Configuration=Release /p:Platform=x86 /m

if %ERRORLEVEL% neq 0 (
    echo 编译失败！
    pause
    exit /b 1
)

echo.
echo 2. 运行测试程序...
echo.

echo === 运行ldfcr_gtest ===
ldfcr_gtest.exe --gtest_filter=*ComponentDetection*

echo.
echo === 运行ldfcr_gtest_advance ===
ldfcr_gtest_advance.exe --gtest_filter=*ComponentDetection*

echo.
echo 3. 测试完成！
echo.
echo 请检查输出中的组件信息：
echo - 应该显示更多组件（特别是Tr系列过滤器）
echo - 应该减少"no version number has been established"的数量
echo - 应该显示实际加载的模块路径
echo.
pause
