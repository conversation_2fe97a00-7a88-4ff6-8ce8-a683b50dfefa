#pragma once
#include <iostream>
#include <stdio.h>
#include "Setting.h"
#if defined(__GNUC__)
#include <unistd.h>
#elif defined(_WIN32)
#include <windows.h>
#endif
#include "ldfcr.h"
#include "../thread/ThreadPool.h"
#include "platformDefined.h"

class ldfcrtest
{
public:
	ldfcrtest(/* args */);
	~ldfcrtest();

	FCRPARAM fcrParam;

public:
	void initSetting();

	void initLog();

	void GetSetting(INISetting &iset);

	void Gdetect(std::string v_strategy, std::vector<tString> vec, INISetting &iset);

	void Pdetect(std::string v_strategy, std::vector<tString> vec, INISetting &iset);

private:
	bool g_init(tString def_test_file);

	bool p_init(ILDFcr *pILDFcr, tString def_test_file);

	bool g_detect(tString path, void **result_p, void **dripResult_p, LPFCRPARAM pfcrParam, INISetting &iset);

	bool p_detect(ILDFcr *pILDFcr, tString path, void **result_p, void **dripResult_p, LPFCRPARAM pfcrParam, INISetting &iset);

	void MatchStrategyP(tString v_strategyFile, tString v_strFile, ILDFcr *pILDFcr, INISetting &iset, int n_Count, int n_curCount);

	void MatchStrategyG(tString v_strategyFile, tString v_strFile, INISetting &iset, int n_Count, int n_curCount);

	BOOL MatchFunc(const tString v_strFile, ILDFcr *pILDFcr, void **result_p, void **dripResult_p, LPFCRPARAM pfcrParam, INISetting &iset); // 全部匹配函数的封装

	BOOL MatchLog(bool MatchTypeP, bool matchFuncF, const std::string v_strFile, void** result_p, void** dripResult_p);
};
