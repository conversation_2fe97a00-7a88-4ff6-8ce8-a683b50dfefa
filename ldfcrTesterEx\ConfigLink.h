#pragma once
#include <string>
#include "ldfcr.h"
#include "IniSetting.h"
class ConfigLink
{
public:
	ConfigLink();
	~ConfigLink();

public:
	void GetIniConfig(const IniSetting &v_instance);

	void SetIniConfig();

	FCRPARAM* Getfcrparm();

	int GetCheckCount();

	std::string GetPassWordJson();

private:
	// 2024 8 29 设置一个任务中断按钮 通过fcrparam中的interrupt 需要将每次进入配置的fcrparam设置为类成员对象
	FCRPARAM* m_fcrParam;

	// 检测时配置信息
	int ini_pwd_error;
	int ini_optype;
	unsigned long long ini_outTimeJug;
	int ini_outTime_b;
	int ini_iCount;
	int ini_bR_json_tag;  // 只返回标签策略 2025 1 21
	int ini_bCheck_ALL;   // 全文检测 2025 1 22
	int ini_Context_count; // 敏感匹配返回上下文字节个数
	unsigned int ini_disableTEModules;


	std::string ini_password;
	std::string ini_key;

	std::string passwordJson;

	/**
	 * @brief 文本提取模块枚举
	 */
	enum _enFilterModule
	{
		_enFM_TEXT = 0x01,				///< 禁用纯文本模块
		_enFM_OLE = 0x02,				///< 禁用OLE模块
		_enFM_COMPRESS = 0x04,			///< 禁用解压缩模块
		_enFM_OOXML = 0x08,				///< 禁用OOXML模块
		_enFM_PDF = 0x10,				///< 禁用PDF模块
		_enFM_RTF = 0x20,				///< 禁用RTF模块
		_enFM_COMMON = 0x40,			///< 禁用IFilter模块
		_enFM_OCR = 0x80,				///< OCR模块，禁用原生图片文件
		_enFM_OCR_IN_CHILD = 0x100,		///< OCR模块，禁用嵌套图片文件
		_enFM_CAD = 0x200				///< 禁用CAD模块
	};
};

