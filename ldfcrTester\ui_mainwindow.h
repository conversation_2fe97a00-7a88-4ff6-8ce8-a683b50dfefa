/********************************************************************************
** Form generated from reading UI file 'mainwindow.ui'
**
** Created by: Qt User Interface Compiler version 5.13.0
**
** WARNING! All changes made in this file will be lost when recompiling UI file!
********************************************************************************/

#ifndef UI_MAINWINDOW_H
#define UI_MAINWINDOW_H

#include <QtCore/QVariant>
#include <QtWidgets/QApplication>
#include <QtWidgets/QFrame>
#include <QtWidgets/QGridLayout>
#include <QtWidgets/QHBoxLayout>
#include <QtWidgets/QLabel>
#include <QtWidgets/QLineEdit>
#include <QtWidgets/QMainWindow>
#include <QtWidgets/QPushButton>
#include <QtWidgets/QSpacerItem>
#include <QtWidgets/QTextEdit>
#include <QtWidgets/QVBoxLayout>
#include <QtWidgets/QWidget>

QT_BEGIN_NAMESPACE

class Ui_MainWindow
{
public:
    QWidget *centralWidget;
    QGridLayout *gridLayout_5;
    QVBoxLayout *verticalLayout_4;
    QFrame *frame;
    QGridLayout *gridLayout;
    QVBoxLayout *verticalLayout;
    QHBoxLayout *horizontalLayout;
    QLabel *label;
    QTextEdit *textEdit_StrategyFilPath;
    QHBoxLayout *horizontalLayout_2;
    QPushButton *btnViewStrategy;
    QPushButton *btnUpdateStrategy;
    QPushButton *btnTextTest;
    QSpacerItem *horizontalSpacer;
    QFrame *frame_2;
    QGridLayout *gridLayout_2;
    QHBoxLayout *horizontalLayout_3;
    QLabel *label_2;
    QTextEdit *textEdit_DetectedFilePath;
    QPushButton *btnSelectFile;
    QHBoxLayout *horizontalLayout_4;
    QPushButton *btnDetectFile;
    QLineEdit *lineEdit_file_count;
    QLabel *label_3;
    QLineEdit *lineEdit_file_time;
    QLabel *label_4;
    QSpacerItem *horizontalSpacer_2;
    QFrame *frame_3;
    QGridLayout *gridLayout_6;
    QVBoxLayout *verticalLayout_2;
    QHBoxLayout *horizontalLayout_6;
    QLabel *label_5;
    QTextEdit *textEdit_DetectedDir;
    QPushButton *btnSelectDir;
    QHBoxLayout *horizontalLayout_5;
    QPushButton *btnDetectDir;
    QLabel *label_8;
    QLineEdit *lineEdit_dir_totallCount;
    QLabel *label_7;
    QLineEdit *lineEdit_dir_sensFilesCounts;
    QLabel *label_9;
    QLineEdit *lineEdit_Dir_time;
    QLabel *label_6;
    QSpacerItem *horizontalSpacer_3;
    QGridLayout *gridLayout_3;
    QLabel *label_11;
    QSpacerItem *horizontalSpacer_5;
    QTextEdit *textEdit_detecting;
    QFrame *frame_4;
    QGridLayout *gridLayout_4;
    QVBoxLayout *verticalLayout_3;
    QHBoxLayout *horizontalLayout_7;
    QLabel *label_10;
    QSpacerItem *horizontalSpacer_4;
    QTextEdit *textEdit_log;

    void setupUi(QMainWindow *MainWindow)
    {
        if (MainWindow->objectName().isEmpty())
            MainWindow->setObjectName(QString::fromUtf8("MainWindow"));
        MainWindow->resize(792, 558);
        centralWidget = new QWidget(MainWindow);
        centralWidget->setObjectName(QString::fromUtf8("centralWidget"));
        gridLayout_5 = new QGridLayout(centralWidget);
        gridLayout_5->setSpacing(6);
        gridLayout_5->setContentsMargins(11, 11, 11, 11);
        gridLayout_5->setObjectName(QString::fromUtf8("gridLayout_5"));
        verticalLayout_4 = new QVBoxLayout();
        verticalLayout_4->setSpacing(6);
        verticalLayout_4->setObjectName(QString::fromUtf8("verticalLayout_4"));
        frame = new QFrame(centralWidget);
        frame->setObjectName(QString::fromUtf8("frame"));
        frame->setFrameShape(QFrame::StyledPanel);
        frame->setFrameShadow(QFrame::Raised);
        gridLayout = new QGridLayout(frame);
        gridLayout->setSpacing(6);
        gridLayout->setContentsMargins(11, 11, 11, 11);
        gridLayout->setObjectName(QString::fromUtf8("gridLayout"));
        verticalLayout = new QVBoxLayout();
        verticalLayout->setSpacing(6);
        verticalLayout->setObjectName(QString::fromUtf8("verticalLayout"));
        horizontalLayout = new QHBoxLayout();
        horizontalLayout->setSpacing(6);
        horizontalLayout->setObjectName(QString::fromUtf8("horizontalLayout"));
        label = new QLabel(frame);
        label->setObjectName(QString::fromUtf8("label"));
        label->setEnabled(true);

        horizontalLayout->addWidget(label);

        textEdit_StrategyFilPath = new QTextEdit(frame);
        textEdit_StrategyFilPath->setObjectName(QString::fromUtf8("textEdit_StrategyFilPath"));
        textEdit_StrategyFilPath->setEnabled(false);

        horizontalLayout->addWidget(textEdit_StrategyFilPath);


        verticalLayout->addLayout(horizontalLayout);

        horizontalLayout_2 = new QHBoxLayout();
        horizontalLayout_2->setSpacing(6);
        horizontalLayout_2->setObjectName(QString::fromUtf8("horizontalLayout_2"));
        btnViewStrategy = new QPushButton(frame);
        btnViewStrategy->setObjectName(QString::fromUtf8("btnViewStrategy"));

        horizontalLayout_2->addWidget(btnViewStrategy);

        btnUpdateStrategy = new QPushButton(frame);
        btnUpdateStrategy->setObjectName(QString::fromUtf8("btnUpdateStrategy"));

        horizontalLayout_2->addWidget(btnUpdateStrategy);

        btnTextTest = new QPushButton(frame);
        btnTextTest->setObjectName(QString::fromUtf8("btnTextTest"));

        horizontalLayout_2->addWidget(btnTextTest);

        horizontalSpacer = new QSpacerItem(40, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        horizontalLayout_2->addItem(horizontalSpacer);

        horizontalLayout_2->setStretch(3, 1);

        verticalLayout->addLayout(horizontalLayout_2);

        verticalLayout->setStretch(0, 1);

        gridLayout->addLayout(verticalLayout, 0, 0, 1, 1);


        verticalLayout_4->addWidget(frame);

        frame_2 = new QFrame(centralWidget);
        frame_2->setObjectName(QString::fromUtf8("frame_2"));
        frame_2->setFrameShape(QFrame::StyledPanel);
        frame_2->setFrameShadow(QFrame::Raised);
        gridLayout_2 = new QGridLayout(frame_2);
        gridLayout_2->setSpacing(6);
        gridLayout_2->setContentsMargins(11, 11, 11, 11);
        gridLayout_2->setObjectName(QString::fromUtf8("gridLayout_2"));
        horizontalLayout_3 = new QHBoxLayout();
        horizontalLayout_3->setSpacing(6);
        horizontalLayout_3->setObjectName(QString::fromUtf8("horizontalLayout_3"));
        label_2 = new QLabel(frame_2);
        label_2->setObjectName(QString::fromUtf8("label_2"));

        horizontalLayout_3->addWidget(label_2);

        textEdit_DetectedFilePath = new QTextEdit(frame_2);
        textEdit_DetectedFilePath->setObjectName(QString::fromUtf8("textEdit_DetectedFilePath"));
        textEdit_DetectedFilePath->setEnabled(false);

        horizontalLayout_3->addWidget(textEdit_DetectedFilePath);

        btnSelectFile = new QPushButton(frame_2);
        btnSelectFile->setObjectName(QString::fromUtf8("btnSelectFile"));

        horizontalLayout_3->addWidget(btnSelectFile);


        gridLayout_2->addLayout(horizontalLayout_3, 0, 0, 1, 1);

        horizontalLayout_4 = new QHBoxLayout();
        horizontalLayout_4->setSpacing(6);
        horizontalLayout_4->setObjectName(QString::fromUtf8("horizontalLayout_4"));
        btnDetectFile = new QPushButton(frame_2);
        btnDetectFile->setObjectName(QString::fromUtf8("btnDetectFile"));

        horizontalLayout_4->addWidget(btnDetectFile);

        lineEdit_file_count = new QLineEdit(frame_2);
        lineEdit_file_count->setObjectName(QString::fromUtf8("lineEdit_file_count"));

        horizontalLayout_4->addWidget(lineEdit_file_count);

        label_3 = new QLabel(frame_2);
        label_3->setObjectName(QString::fromUtf8("label_3"));

        horizontalLayout_4->addWidget(label_3);

        lineEdit_file_time = new QLineEdit(frame_2);
        lineEdit_file_time->setObjectName(QString::fromUtf8("lineEdit_file_time"));
        lineEdit_file_time->setEnabled(false);

        horizontalLayout_4->addWidget(lineEdit_file_time);

        label_4 = new QLabel(frame_2);
        label_4->setObjectName(QString::fromUtf8("label_4"));

        horizontalLayout_4->addWidget(label_4);

        horizontalSpacer_2 = new QSpacerItem(40, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        horizontalLayout_4->addItem(horizontalSpacer_2);

        horizontalLayout_4->setStretch(5, 1);

        gridLayout_2->addLayout(horizontalLayout_4, 1, 0, 1, 1);


        verticalLayout_4->addWidget(frame_2);

        frame_3 = new QFrame(centralWidget);
        frame_3->setObjectName(QString::fromUtf8("frame_3"));
        frame_3->setFrameShape(QFrame::StyledPanel);
        frame_3->setFrameShadow(QFrame::Raised);
        gridLayout_6 = new QGridLayout(frame_3);
        gridLayout_6->setSpacing(6);
        gridLayout_6->setContentsMargins(11, 11, 11, 11);
        gridLayout_6->setObjectName(QString::fromUtf8("gridLayout_6"));
        verticalLayout_2 = new QVBoxLayout();
        verticalLayout_2->setSpacing(6);
        verticalLayout_2->setObjectName(QString::fromUtf8("verticalLayout_2"));
        horizontalLayout_6 = new QHBoxLayout();
        horizontalLayout_6->setSpacing(6);
        horizontalLayout_6->setObjectName(QString::fromUtf8("horizontalLayout_6"));
        label_5 = new QLabel(frame_3);
        label_5->setObjectName(QString::fromUtf8("label_5"));

        horizontalLayout_6->addWidget(label_5);

        textEdit_DetectedDir = new QTextEdit(frame_3);
        textEdit_DetectedDir->setObjectName(QString::fromUtf8("textEdit_DetectedDir"));
        textEdit_DetectedDir->setEnabled(false);

        horizontalLayout_6->addWidget(textEdit_DetectedDir);

        btnSelectDir = new QPushButton(frame_3);
        btnSelectDir->setObjectName(QString::fromUtf8("btnSelectDir"));

        horizontalLayout_6->addWidget(btnSelectDir);


        verticalLayout_2->addLayout(horizontalLayout_6);

        horizontalLayout_5 = new QHBoxLayout();
        horizontalLayout_5->setSpacing(6);
        horizontalLayout_5->setObjectName(QString::fromUtf8("horizontalLayout_5"));
        btnDetectDir = new QPushButton(frame_3);
        btnDetectDir->setObjectName(QString::fromUtf8("btnDetectDir"));

        horizontalLayout_5->addWidget(btnDetectDir);

        label_8 = new QLabel(frame_3);
        label_8->setObjectName(QString::fromUtf8("label_8"));

        horizontalLayout_5->addWidget(label_8);

        lineEdit_dir_totallCount = new QLineEdit(frame_3);
        lineEdit_dir_totallCount->setObjectName(QString::fromUtf8("lineEdit_dir_totallCount"));
        lineEdit_dir_totallCount->setEnabled(false);

        horizontalLayout_5->addWidget(lineEdit_dir_totallCount);

        label_7 = new QLabel(frame_3);
        label_7->setObjectName(QString::fromUtf8("label_7"));

        horizontalLayout_5->addWidget(label_7);

        lineEdit_dir_sensFilesCounts = new QLineEdit(frame_3);
        lineEdit_dir_sensFilesCounts->setObjectName(QString::fromUtf8("lineEdit_dir_sensFilesCounts"));
        lineEdit_dir_sensFilesCounts->setEnabled(false);

        horizontalLayout_5->addWidget(lineEdit_dir_sensFilesCounts);

        label_9 = new QLabel(frame_3);
        label_9->setObjectName(QString::fromUtf8("label_9"));

        horizontalLayout_5->addWidget(label_9);

        lineEdit_Dir_time = new QLineEdit(frame_3);
        lineEdit_Dir_time->setObjectName(QString::fromUtf8("lineEdit_Dir_time"));
        lineEdit_Dir_time->setEnabled(false);

        horizontalLayout_5->addWidget(lineEdit_Dir_time);

        label_6 = new QLabel(frame_3);
        label_6->setObjectName(QString::fromUtf8("label_6"));

        horizontalLayout_5->addWidget(label_6);

        horizontalSpacer_3 = new QSpacerItem(40, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        horizontalLayout_5->addItem(horizontalSpacer_3);

        horizontalLayout_5->setStretch(8, 1);

        verticalLayout_2->addLayout(horizontalLayout_5);

        gridLayout_3 = new QGridLayout();
        gridLayout_3->setSpacing(6);
        gridLayout_3->setObjectName(QString::fromUtf8("gridLayout_3"));
        label_11 = new QLabel(frame_3);
        label_11->setObjectName(QString::fromUtf8("label_11"));

        gridLayout_3->addWidget(label_11, 0, 0, 1, 1);

        horizontalSpacer_5 = new QSpacerItem(40, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        gridLayout_3->addItem(horizontalSpacer_5, 0, 1, 1, 1);

        textEdit_detecting = new QTextEdit(frame_3);
        textEdit_detecting->setObjectName(QString::fromUtf8("textEdit_detecting"));
        textEdit_detecting->setEnabled(false);

        gridLayout_3->addWidget(textEdit_detecting, 1, 0, 1, 2);


        verticalLayout_2->addLayout(gridLayout_3);


        gridLayout_6->addLayout(verticalLayout_2, 0, 0, 1, 1);


        verticalLayout_4->addWidget(frame_3);

        frame_4 = new QFrame(centralWidget);
        frame_4->setObjectName(QString::fromUtf8("frame_4"));
        frame_4->setFrameShape(QFrame::StyledPanel);
        frame_4->setFrameShadow(QFrame::Raised);
        gridLayout_4 = new QGridLayout(frame_4);
        gridLayout_4->setSpacing(6);
        gridLayout_4->setContentsMargins(11, 11, 11, 11);
        gridLayout_4->setObjectName(QString::fromUtf8("gridLayout_4"));
        verticalLayout_3 = new QVBoxLayout();
        verticalLayout_3->setSpacing(6);
        verticalLayout_3->setObjectName(QString::fromUtf8("verticalLayout_3"));
        horizontalLayout_7 = new QHBoxLayout();
        horizontalLayout_7->setSpacing(6);
        horizontalLayout_7->setObjectName(QString::fromUtf8("horizontalLayout_7"));
        label_10 = new QLabel(frame_4);
        label_10->setObjectName(QString::fromUtf8("label_10"));

        horizontalLayout_7->addWidget(label_10);

        horizontalSpacer_4 = new QSpacerItem(40, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        horizontalLayout_7->addItem(horizontalSpacer_4);


        verticalLayout_3->addLayout(horizontalLayout_7);

        textEdit_log = new QTextEdit(frame_4);
        textEdit_log->setObjectName(QString::fromUtf8("textEdit_log"));
        textEdit_log->setEnabled(false);

        verticalLayout_3->addWidget(textEdit_log);


        gridLayout_4->addLayout(verticalLayout_3, 0, 0, 1, 1);


        verticalLayout_4->addWidget(frame_4);

        verticalLayout_4->setStretch(0, 2);
        verticalLayout_4->setStretch(1, 2);
        verticalLayout_4->setStretch(2, 4);
        verticalLayout_4->setStretch(3, 2);

        gridLayout_5->addLayout(verticalLayout_4, 0, 0, 1, 1);

        MainWindow->setCentralWidget(centralWidget);

        retranslateUi(MainWindow);
        QObject::connect(btnViewStrategy, SIGNAL(clicked()), MainWindow, SLOT(viewStrategy()));
        QObject::connect(btnUpdateStrategy, SIGNAL(clicked()), MainWindow, SLOT(updateStrategy()));
        QObject::connect(btnTextTest, SIGNAL(clicked()), MainWindow, SLOT(TextTest()));
        QObject::connect(btnSelectFile, SIGNAL(clicked()), MainWindow, SLOT(selectFile()));
        QObject::connect(btnDetectFile, SIGNAL(clicked()), MainWindow, SLOT(startDetectFile()));
        QObject::connect(btnDetectDir, SIGNAL(clicked()), MainWindow, SLOT(startDetectDir()));
        QObject::connect(btnSelectDir, SIGNAL(clicked()), MainWindow, SLOT(selectDir()));

        QMetaObject::connectSlotsByName(MainWindow);
    } // setupUi

    void retranslateUi(QMainWindow *MainWindow)
    {
        MainWindow->setWindowTitle(QCoreApplication::translate("MainWindow", "\346\226\207\344\273\266\345\206\205\345\256\271\350\257\206\345\210\253\347\273\204\344\273\266\346\265\213\350\257\225\347\250\213\345\272\217", nullptr));
        label->setText(QCoreApplication::translate("MainWindow", "\351\273\230\350\256\244\347\255\226\347\225\245\346\226\207\344\273\266", nullptr));
        btnViewStrategy->setText(QCoreApplication::translate("MainWindow", "\346\237\245\347\234\213\347\255\226\347\225\245", nullptr));
        btnUpdateStrategy->setText(QCoreApplication::translate("MainWindow", "\346\233\264\346\226\260\347\255\226\347\225\245", nullptr));
        btnTextTest->setText(QCoreApplication::translate("MainWindow", "\346\243\200\346\265\213\346\226\207\346\234\254", nullptr));
        label_2->setText(QCoreApplication::translate("MainWindow", "\345\276\205\346\243\200\346\265\213\346\226\207\344\273\266", nullptr));
        btnSelectFile->setText(QCoreApplication::translate("MainWindow", "\342\200\246", nullptr));
        btnDetectFile->setText(QCoreApplication::translate("MainWindow", "\346\243\200\346\265\213\346\226\207\344\273\266", nullptr));
        label_3->setText(QCoreApplication::translate("MainWindow", "\346\254\241\357\274\214\350\200\227\346\227\266", nullptr));
        label_4->setText(QCoreApplication::translate("MainWindow", "\347\247\222", nullptr));
        label_5->setText(QCoreApplication::translate("MainWindow", "\345\276\205\346\243\200\346\265\213\347\233\256\345\275\225", nullptr));
        btnSelectDir->setText(QCoreApplication::translate("MainWindow", "\342\200\246", nullptr));
        btnDetectDir->setText(QCoreApplication::translate("MainWindow", "\346\243\200\346\265\213\346\226\207\344\273\266", nullptr));
        label_8->setText(QCoreApplication::translate("MainWindow", "\345\267\262\346\211\253\346\217\217", nullptr));
        label_7->setText(QCoreApplication::translate("MainWindow", "\344\270\252\346\226\207\344\273\266\357\274\214\346\243\200\346\265\213\345\210\260", nullptr));
        label_9->setText(QCoreApplication::translate("MainWindow", "\344\270\252\346\225\217\346\204\237\346\226\207\344\273\266\357\274\214\350\200\227\346\227\266", nullptr));
        label_6->setText(QCoreApplication::translate("MainWindow", "\347\247\222", nullptr));
        label_11->setText(QCoreApplication::translate("MainWindow", "\346\255\243\345\234\250\346\243\200\346\265\213", nullptr));
        label_10->setText(QCoreApplication::translate("MainWindow", "\350\277\220\350\241\214\346\227\245\345\277\227", nullptr));
    } // retranslateUi

};

namespace Ui {
    class MainWindow: public Ui_MainWindow {};
} // namespace Ui

QT_END_NAMESPACE

#endif // UI_MAINWINDOW_H
