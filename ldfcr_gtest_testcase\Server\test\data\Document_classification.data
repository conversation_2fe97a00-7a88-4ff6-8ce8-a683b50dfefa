{"respondRule": [{"sendMail": false, "enableCancel": 0, "filters": [{"type": "deviceType", "relation": "either", "object": ["terminal-agent"]}, {"type": "lossType", "relation": "either", "object": ["8", "9", "26", "25", "2", "3", "4", "14", "15", "16", "11", "1", "18", "5", "17", "7", "23", "24", "13", "0", "20", "27", "29"]}], "alarmInfo": {"alarmLimit": 3, "msgFormType": 0, "deleted": 0, "configMethod": 1, "name": "违规响应", "msgFormPosition": 0, "id": 1, "msgFormClose": 30}, "outSendApply": 0, "stopOutgoing": 0, "takeScreenshot": false, "stopOutgoingEx": 0, "createTime": 1688538427000, "name": "敏感响应触发", "id": 3, "warnContent": ""}], "checkRule": [{"exceptWord": "", "file": "fa33269d95db94a3fb696165df6e7cce", "createTime": 1688970802000, "ruleType": "5", "name": "文档分类规则测试10", "id": 14, "type": 5}], "strategy": [{"severity": "1", "respondRule": "3", "createTime": 1688972359089, "checkRule": "14", "name": "文档归类中文测试", "dripScan": false, "id": 5, "classification": [{"checkExpr": "14", "name": "文档归类规则2", "exceptRule": "", "id": 6}]}], "businessType": {"opTypes": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 20], "type": 99}}