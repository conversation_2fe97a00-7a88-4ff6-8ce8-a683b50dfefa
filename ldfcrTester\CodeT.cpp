#include "stdafx.h"
#include "CodeT.h"

string GbkToUtf8(const char *src_str)
{
	int len = MultiByteToWideChar(CP_ACP, 0, src_str, -1, NULL, 0);
	wchar_t* wstr = new wchar_t[len + 1];
	memset(wstr, 0, len + 1);
	MultiByteToWideChar(CP_ACP, 0, src_str, -1, wstr, len);
	len = WideCharToMultiByte(CP_UTF8, 0, wstr, -1, NULL, 0, NULL, NULL);
	char* str = new char[len + 1];
	memset(str, 0, len + 1);
	WideCharToMultiByte(CP_UTF8, 0, wstr, -1, str, len, NULL, NULL);
	string strTemp = str;
	if (wstr) delete[] wstr;
	if (str) delete[] str;
	return strTemp;
}

string Utf8ToGbk(const char *src_str)
{
	int len = MultiByteToWideChar(CP_UTF8, 0, src_str, -1, NULL, 0);
	wchar_t* wszGBK = new wchar_t[len + 1];
	memset(wszGBK, 0, len * 2 + 2);
	MultiByteToWideChar(CP_UTF8, 0, src_str, -1, wszGBK, len);
	len = WideCharToMultiByte(CP_ACP, 0, wszGBK, -1, NULL, 0, NULL, NULL);
	char* szGBK = new char[len + 1];
	memset(szGBK, 0, len + 1);
	WideCharToMultiByte(CP_ACP, 0, wszGBK, -1, szGBK, len, NULL, NULL);
	string strTemp(szGBK);

	if (wszGBK) 
		delete[] wszGBK;

	if (szGBK) 
		delete[] szGBK;
	return strTemp;
}

wstring Utf8ToUnicode(const string& utf8_str)
{
	int len = MultiByteToWideChar(CP_UTF8, 0, utf8_str.c_str(), -1, NULL, 0);
	wchar_t* wszUnicode = new wchar_t[len];
	memset(wszUnicode, 0, len * sizeof(wchar_t));
	MultiByteToWideChar(CP_UTF8, 0, utf8_str.c_str(), -1, wszUnicode, len);
	wstring unicodeStr(wszUnicode);
	delete[] wszUnicode;
	return unicodeStr;
}

string UnicodeToUtf8(const wstring& unicode_str)
{
	int len = WideCharToMultiByte(CP_UTF8, 0, unicode_str.c_str(), -1, NULL, 0, NULL, NULL);
	char* szUtf8 = new char[len];
	memset(szUtf8, 0, len * sizeof(char));
	WideCharToMultiByte(CP_UTF8, 0, unicode_str.c_str(), -1, szUtf8, len, NULL, NULL);
	string utf8Str(szUtf8);
	delete[] szUtf8;
	return utf8Str;
}



