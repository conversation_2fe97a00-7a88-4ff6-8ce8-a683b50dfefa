#include <mainwindow.h>
#include "ui_mainwindow.h"
#include "../trcrt/trcrt.h"
#include "DirNode.h"
#include <QFileDialog>
#include <QMessageBox>
#include <locale.h>
#include <time.h>
#include <dirent.h>
#include <sys/stat.h>
#include <ftw.h>
#include <QInputDialog>
#include <QtConcurrent/QtConcurrentRun>

std::list<std::string>  MainWindow::filename_list;

void MainWindow::viewStrategy(){

}
void MainWindow::updateStrategy(){

}
void MainWindow::TextTest(){

    bool ok = FALSE;
    QString text = QInputDialog::getText(
                this,
                tr( "输入文本" ),
                tr( "请输入待检测的文本" ),QLineEdit::Normal,QString(),&ok);


    if( (!ok) || text.isEmpty() )
    {
        return;
    }

    if( nullptr == m_pILDFcr ) return;

    BOOL bIsSensitive = FALSE;

    FCRPARAM fcrParam;

    fcrParam.devType = 1;
    fcrParam.opType  = 1;
    fcrParam.use_all_rule = TRUE;   //启用所有规则
    fcrParam.target_class_code = 3; //达到或超过代码3，即停止
    fcrParam.output_detail = TRUE;  //要求返回时输出策略的细节信息

    IFCRResult*  result_p = nullptr;
    bIsSensitive = m_pILDFcr->executeTCR(
                text.toUtf8(), -1,
                &fcrParam, (void**)&result_p
                );

    if( bIsSensitive ){
        QMessageBox::warning(this,"检测结果","检测为敏感文件！");
    }else{
        QMessageBox::warning(this,"检测结果","检测无敏感内容");
    }

}
void MainWindow::selectFile(){
    //定义文件对话框类
    QFileDialog *fileDialog = new QFileDialog(this);
    //定义文件对话框标题
    fileDialog->setWindowTitle(tr("打开文件"));
    //设置默认文件路径
    fileDialog->setDirectory(".");
    //设置文件过滤器
    fileDialog->setNameFilter(tr("AnyFile(*.*)"));

    fileDialog->exec();

    QString strFile;
    strFile = fileDialog->selectedFiles().first();

    ui->textEdit_DetectedFilePath->setText(strFile);
}

void MainWindow::startDetectFile(){
    SetAllBtn(false);

    ////////////////////////////
    //起线程跑检测
    ////////////////////////////

    QFuture<void> thread1 = QtConcurrent::run(this,&MainWindow::thread_detect_file);
}

void MainWindow::selectDir(){

    QFileDialog *fileDialog = new QFileDialog(this);
    fileDialog->setFileMode(QFileDialog::Directory);

    fileDialog->exec();

    ui->textEdit_DetectedDir->setText(fileDialog->selectedFiles().first());
}

void MainWindow::startDetectDir(){
    ui->textEdit_detecting->clear();
    ui->lineEdit_Dir_time->setText("0");

    SetAllBtn(false);
    ui->btnDetectDir->setEnabled(true);
    if( m_bDoingDirFcr )
    {
        m_bDoingDirFcr = FALSE;
        SetAllBtn(true);
        ui->btnDetectDir->setText(tr("开始检测"));
        return;
    }

    m_bDoingDirFcr = TRUE;
    ui->btnDetectDir->setText(tr("停止检测"));
    ui->lineEdit_dir_totallCount->setText("0");
    ui->lineEdit_dir_sensFilesCounts->setText("0");


    /////////////////////////////
    //启动检测线程
    //
    /////////////////////////////

    QFuture<void> thread2 = QtConcurrent::run(this,&MainWindow::thread_detect_dir);

}

//ftw()回调使用，返回非0值时作为ftw()的中断返回值，ftw()正常结束返回0
int MainWindow::fn(const char *file, const struct stat *st, int flag){
    if(FTW_F == flag)
        filename_list.push_back(file);

    return 0;
}

void MainWindow::SetAllBtn(bool state){
    ui->btnTextTest->setEnabled(state);
    ui->btnDetectDir->setEnabled(state);
    ui->btnSelectDir->setEnabled(state);
    ui->btnDetectFile->setEnabled(state);
    ui->btnSelectFile->setEnabled(state);
    ui->btnViewStrategy->setEnabled(state);
    ui->btnUpdateStrategy->setEnabled(state);
}

void MainWindow::thread_detect_file(){
    if( 0 >= ui->lineEdit_file_count->text().toInt()){
        return;
    }

    if( nullptr == m_pILDFcr )
    {
        return;
    }

    BOOL bIsSensitive = FALSE;

    FCRPARAM fcrParam;

    fcrParam.devType = 1;
    fcrParam.opType  = 1;
    fcrParam.use_all_rule = TRUE;   //启用所有规则
    fcrParam.target_class_code = 3; //达到或超过代码3，即停止
    fcrParam.output_detail = TRUE;  //要求返回时输出策略的细节信息

    struct timespec start,end;
    clock_gettime(CLOCK_MONOTONIC, &start);   //linux下获得系统启动时间

    for( int i = 0; i < m_iFcrCount; i ++ ){
        IFCRResult*  result_p = nullptr;
        BOOL res = m_pILDFcr->executeFCR(
                    ui->textEdit_DetectedFilePath->toPlainText().toUtf8(),
                    &fcrParam,
                    (void**)&result_p
                    );
        //大文件崩溃,executeFCRW返回0

        if( nullptr != result_p )
        {
            int n = 0;
            for( int i = 0; i < 3; i ++)
            {
                result_p->moveFirstPart();
                while( nullptr != result_p->moveNextPart() )
                {
                    n ++;
                }
            }

            result_p->Release();
            result_p = nullptr;
        }
        bIsSensitive |= res;
    }

    clock_gettime(CLOCK_MONOTONIC, &end);
    double dwTick = 1000*(end.tv_sec-start.tv_sec)+(end.tv_nsec-start.tv_nsec)/1000000;
    emit(done_detect_file(dwTick,bIsSensitive));
}
void MainWindow::recv_sign_done_detect_file(double dwTick,bool bIsSensitive){

    ui->lineEdit_file_time->setText(QString::number(dwTick/1000,10,6));

    if( bIsSensitive ){
        QMessageBox::warning(this,"检测结果","检测为敏感文件！");
    }else{
        QMessageBox::warning(this,"检测结果","检测无敏感内容");
    }
    SetAllBtn(true);
}
void MainWindow::thread_detect_dir(){

    int  file_count_scaned = 0;
    int  file_count_sens   = 0;
    struct timespec start,end;
    clock_gettime(CLOCK_MONOTONIC, &start);   //linux下获得系统启动时间

    char log_dir[MAX_PATH];

    trcrt::path::tr_path_get_app_path(log_dir,MAX_PATH);

    strcat(log_dir,"files.log");

    FILE* fp = fopen( log_dir, "wt" );

    if( nullptr != fp )
    {
        setlocale(0, "chs");
    }

    //遍历和检测，与windows版本有较大差异
    filename_list.clear();

    ftw(ui->textEdit_DetectedDir->toPlainText().toUtf8(),fn,500);
    emit(textedit_detecting_append("解析路径中......"));

    std::list<std::string>::iterator iterFileName;
    for( iterFileName = filename_list.begin();
         iterFileName != filename_list.end(); iterFileName ++ ){

        if(FALSE == m_bDoingDirFcr) return;


        const std::string& fileName = *iterFileName;
        printf("%s\n",fileName.c_str());


        if( nullptr != fp )
        {
            fprintf(fp, "%s\n", fileName.c_str() );   //在这里写入files.log
        }
        //检测文件
        if( this->dlp_checkSingleFile(fileName.c_str()) )
        {
            file_count_sens ++;
        }
        //避免在线程里操作ui
//        ui->textEdit_detecting->setText(QString::fromStdString(fileName));
//        ui->lineEdit_dir_totallCount->setText(QString::number(++file_count_scaned));
//        ui->lineEdit_dir_sensFilesCounts->setText(QString::number(file_count_sens));

        emit(textedit_detecting_append(QString::fromStdString(fileName)));
        emit(lineedit_totalcount_set(QString::number(++file_count_scaned)));
        emit(lineedit_senscount_set(QString::number(file_count_sens)));

    }

    if( nullptr != fp )
    {
        fclose(fp);
    }

    clock_gettime(CLOCK_MONOTONIC, &end);
    double dwTick = 1000*(end.tv_sec-start.tv_sec)+(end.tv_nsec-start.tv_nsec)/1000000;

    emit(done_detect_dir(dwTick));
}
void MainWindow::recv_sign_done_detect_dir(double dwTick){
    ui->lineEdit_Dir_time->setText(QString::number(dwTick/1000,10,6));

    QMessageBox::warning(this,"检测结果","检测完毕");
    m_bDoingDirFcr = FALSE;
    ui->btnDetectDir->setText(tr("开始检测"));
    SetAllBtn(true);
}
