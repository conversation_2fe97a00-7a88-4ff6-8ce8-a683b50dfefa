{"respondRule": [{"sendMail": false, "enableCancel": 0, "filters": [{"type": "deviceType", "relation": "either", "object": ["terminal-agent"]}, {"type": "lossType", "relation": "either", "object": ["8", "9", "26", "25", "2", "3", "4", "14", "15", "16", "11", "1", "18", "5", "17", "7", "23", "24", "13", "0", "20", "27"]}], "alarmInfo": {"alarmLimit": 3, "msgFormType": 0, "configMethod": 1, "name": "外发", "msgFormPosition": 0, "id": 1, "isDel": false, "msgFormClose": 30}, "outSendApply": 0, "stopOutgoing": 0, "takeScreenshot": false, "stopOutgoingEx": 0, "createTime": 1668582478000, "name": "外发", "id": 2, "warnContent": ""}], "checkRule": [{"mode": 2, "exceptWord": "", "checkTimes": 1, "createTime": 1669010712000, "ruleType": "3", "name": "正则测试", "lua": "function func(str)\n\tstrLocal = string.sub(str,1,6)\n\tstrXM = {\"110101\",\"110102\",\"110105\",\"110106\",\"110107\",\"110108\",\"110109\",\"110111\",\"110112\",\"110113\",\"110114\",\"110115\",\"110116\",\"110117\",\"110118\",\"110119\"}\n\tfor i in ipairs(strXM) do\n\t\tif strLocal == strXM[i] then\n\t\t\treturn true\n\t\tend\n\tend\n\treturn false\nend", "id": 7, "type": 3, "regular": "[1-9]\\d{5}(18|19|([23]\\d))\\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\\d{3}[0-9Xx]"}], "strategy": [{"severity": "1", "respondRule": "2", "createTime": 1669012100618, "checkRule": "7", "name": "正则测试", "dripScan": false, "id": 10, "classification": [{"checkExpr": "7", "name": "正则测试", "exceptRule": "", "id": 5}]}], "businessType": {"opTypes": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 20], "type": 99}}