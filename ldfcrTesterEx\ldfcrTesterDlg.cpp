
// ldfcrTesterDlg.cpp : 实现文件
//
#pragma once
#include "stdafx.h"
#include "ldfcrTester.h"
#include "ldfcrTesterDlg.h"
#include "afxdialogex.h"
#include "trcrt.h"
#include "../INC/DlpULog.h"
#include "CldfcrLog.h"
#include "IniSetting.h"

#ifdef _DEBUG
#define new DEBUG_NEW
#endif

using namespace std;
LOG_HANDLE   g_log_handle = -1;
#define MAX_FILE_COUNT          1024

// 用于应用程序“关于”菜单项的 CAboutDlg 对话框

class CAboutDlg : public CDialogEx
{
public:
	CAboutDlg();

// 对话框数据
#ifdef AFX_DESIGN_TIME
	enum { IDD = IDD_ABOUTBOX };
#endif

	protected:
	virtual void DoDataExchange(CDataExchange* pDX);    // DDX/DDV 支持

// 实现
protected:
	DECLARE_MESSAGE_MAP()
	virtual void OnOK();
};

CAboutDlg::CAboutDlg() : CDialogEx(IDD_ABOUTBOX)
{
}

void CAboutDlg::DoDataExchange(CDataExchange* pDX)
{
	CDialogEx::DoDataExchange(pDX);
}

BEGIN_MESSAGE_MAP(CAboutDlg, CDialogEx)
END_MESSAGE_MAP()


// CldfcrTesterDlg 对话框



CldfcrTesterDlg::CldfcrTesterDlg(CWnd* pParent /*=NULL*/)
	: CDialogEx(IDD_LDFCRTESTER_DIALOG, pParent)
	, m_strShowLogPath(_T(""))
{
	m_hIcon = AfxGetApp()->LoadIcon(IDR_MAINFRAME);
}

void CldfcrTesterDlg::DoDataExchange(CDataExchange* pDX)
{
	CDialogEx::DoDataExchange(pDX);
	DDX_Control(pDX, IDC_TAB1, m_tab);
	DDX_Control(pDX, IDC_EDIT1, m_CShowLogPathEdit);
	DDX_Text(pDX, IDC_EDIT1, m_strShowLogPath);
}

BEGIN_MESSAGE_MAP(CldfcrTesterDlg, CDialogEx)
	ON_WM_SYSCOMMAND()
	ON_WM_PAINT()
	ON_WM_QUERYDRAGICON()
	ON_NOTIFY(TCN_SELCHANGE, IDC_TAB1, &CldfcrTesterDlg::OnTcnSelchangeTab1)
	ON_BN_CLICKED(IDC_LOG_CHOOSE, &CldfcrTesterDlg::OnBnClickedLogChoose)
	ON_BN_CLICKED(IDC_LOG, &CldfcrTesterDlg::OnBnClickedLog)
	ON_WM_CLOSE()
	ON_BN_CLICKED(IDC_BTN_DIR, &CldfcrTesterDlg::OnBnClickedBtnDir)
END_MESSAGE_MAP()


// CldfcrTesterDlg 消息处理程序

BOOL CldfcrTesterDlg::OnInitDialog()
{
	initLog();
	CDialogEx::OnInitDialog();


	// 将“关于...”菜单项添加到系统菜单中。

	// IDM_ABOUTBOX 必须在系统命令范围内。
	ASSERT((IDM_ABOUTBOX & 0xFFF0) == IDM_ABOUTBOX);
	ASSERT(IDM_ABOUTBOX < 0xF000);

	CMenu* pSysMenu = GetSystemMenu(FALSE);
	if (pSysMenu != NULL)
	{
		BOOL bNameValid;
		CString strAboutMenu;
		bNameValid = strAboutMenu.LoadString(IDS_ABOUTBOX);
		ASSERT(bNameValid);
		if (!strAboutMenu.IsEmpty())
		{
			pSysMenu->AppendMenu(MF_SEPARATOR);
			pSysMenu->AppendMenu(MF_STRING, IDM_ABOUTBOX, strAboutMenu);
		}
	}

	// 设置此对话框的图标。  当应用程序主窗口不是对话框时，框架将自动
	//  执行此操作
	SetIcon(m_hIcon, TRUE);			// 设置大图标
	SetIcon(m_hIcon, FALSE);		// 设置小图标

	// TODO: 在此添加额外的初始化代码
	// 链接测试
	std::string str_ip = "************";
	bool link_res = testLink(str_ip.c_str());
	if (!link_res)
	{
		exit(0);
	}

	IniSetting::getInstance().initSetting();

	CSize size, size1;
	size.cx = 150;
	size.cy = 40;
	size1 = m_tab.SetItemSize(size);

	// 选项卡初始化参数
	//为Tab Control增加两个页面
	m_tab.InsertItem(0, _T("   敏感识别 检测界面 "));
	m_tab.InsertItem(2, _T("   SETTING 配置界面  "));

	//创建两个对话框
	p_page.Create(IDD_LDFCRTESTER_P, &m_tab);
	s_page.Create(IDD_SETTING, &m_tab);
	//设定在Tab内显示的范围
	CRect rc;
	m_tab.GetClientRect(rc);
	rc.top += 40;
	rc.bottom -= 0;
	rc.left += 0;
	rc.right -= 0;
	p_page.MoveWindow(&rc);
	s_page.MoveWindow(&rc);

	//把对话框对象指针保存起来
	pDialog[0] = &p_page;
	pDialog[1] = &s_page;
	//显示初始页面
	pDialog[0]->ShowWindow(SW_SHOW);
	pDialog[1]->ShowWindow(SW_HIDE);
	//保存当前选择
	m_CurSelTab = 0;

	CString cstr = L"程序初始化完成，还未加载策略";
	//outPutOperaLog(cstr);
	//outPutOperaLog(cstr);
	OnNewDocument();
	this->UpdateData(FALSE);
	return TRUE;  // 除非将焦点设置到控件，否则返回 TRUE
}

void CldfcrTesterDlg::OnSysCommand(UINT nID, LPARAM lParam)
{
	if ((nID & 0xFFF0) == IDM_ABOUTBOX)
	{
		CAboutDlg dlgAbout;
		dlgAbout.DoModal();
	}
	else
	{
		CDialogEx::OnSysCommand(nID, lParam);
	}
}

// 如果向对话框添加最小化按钮，则需要下面的代码
//  来绘制该图标。  对于使用文档/视图模型的 MFC 应用程序，
//  这将由框架自动完成。

void CldfcrTesterDlg::OnPaint()
{
	if (IsIconic())
	{
		CPaintDC dc(this); // 用于绘制的设备上下文

		SendMessage(WM_ICONERASEBKGND, reinterpret_cast<WPARAM>(dc.GetSafeHdc()), 0);

		// 使图标在工作区矩形中居中
		int cxIcon = GetSystemMetrics(SM_CXICON);
		int cyIcon = GetSystemMetrics(SM_CYICON);
		CRect rect;
		GetClientRect(&rect);
		int x = (rect.Width() - cxIcon + 1) / 2;
		int y = (rect.Height() - cyIcon + 1) / 2;

		// 绘制图标
		dc.DrawIcon(x, y, m_hIcon);
	}
	else
	{
		CDialogEx::OnPaint();
	}
}

//当用户拖动最小化窗口时系统调用此函数取得光标
//显示。
HCURSOR CldfcrTesterDlg::OnQueryDragIcon()
{
	return static_cast<HCURSOR>(m_hIcon);
}



void CAboutDlg::OnOK()
{
	// TODO: 在此添加专用代码和/或调用基类

	//CDialogEx::OnOK();
}


void CldfcrTesterDlg::OnTcnSelchangeTab1(NMHDR *pNMHDR, LRESULT *pResult)
{
	// TODO: 在此添加控件通知处理程序代码
	//把当前的页面隐藏起来
	pDialog[m_CurSelTab]->ShowWindow(SW_HIDE);
	//得到新的页面索引
	m_CurSelTab = m_tab.GetCurSel();
	//把新的页面显示出来
	pDialog[m_CurSelTab]->ShowWindow(SW_SHOW);

	*pResult = 0;
}


void CldfcrTesterDlg::OnBnClickedLogChoose()
{
	// TODO: 在此添加控件通知处理程序代码
	CString strInitialDir;
	m_CShowLogPathEdit.GetWindowText(strInitialDir);
	CString initialPath;


	// 如果编辑框中的路径为空，将log文件夹所在的目录作为初始路径
	char logDir[MAX_PATH];
	trcrt::app::tr_app_get_log_directory(logDir, MAX_PATH);
	initialPath = CString(logDir);

	static WCHAR T;
	//CString strFile = _T("R:\\");
	CFileDialog   dlgFile(TRUE, _T("log"), NULL, OFN_HIDEREADONLY, _T("Log (*.*)|*.*||"), NULL);
	dlgFile.m_ofn.lpstrInitialDir = initialPath;
	if (dlgFile.DoModal())
	{
		strInitialDir = dlgFile.GetPathName();
	}

	if (strInitialDir.IsEmpty())
	{
		return;
	}

	m_strShowLogPath = strInitialDir;

	this->UpdateData(FALSE);

	// 记录当前使用的策略路径
	BOOL blR = IniSetting::getInstance().recordCStr2INI("LOG", "log_path", m_strShowLogPath);
	if (blR < 0)
	{
		dlplog_error(g_log_handle, "Setting ini record failed");
	}
}


void CldfcrTesterDlg::OnBnClickedLog()
{
	// TODO: 在此添加控件通知处理程序代码
	this->UpdateData(TRUE);

	CldfcrLog dlgLog(m_strShowLogPath);
	dlgLog.DoModal();

	// 记录当前使用的策略路径
	BOOL blR = IniSetting::getInstance().recordCStr2INI("LOG", "log_path", m_strShowLogPath);
	if (blR < 0)
	{
		dlplog_error(g_log_handle, "Setting ini record failed");
	}
}

/*
* 函数：获取运行程序目录
* 功能：可获取运行程序的目录
*/
CString CldfcrTesterDlg::GetProPath()
{

	WCHAR wcsAppDir[MAX_PATH];
	::GetModuleFileNameW(NULL, wcsAppDir, sizeof(wcsAppDir) / sizeof(WCHAR));
	int  iLen = ::wcslen(wcsAppDir);
	while (0 < iLen)
	{
		if (L'\\' == wcsAppDir[iLen - 1])
		{
			wcsAppDir[iLen - 1] = 0;
			break;
		}

		iLen--;
	}

	CString _str = wcsAppDir;
	return _str;
}

/*
* 日志初始化
*/
void CldfcrTesterDlg::initLog()
{
	trcrt::trcrt_init();
	char config_file_path[1024] = { 0 };
	trcrt::app::tr_app_get_config_directory(config_file_path, sizeof(config_file_path));
	trcrt::path::tr_path_make_sub_path(config_file_path, sizeof(config_file_path), "log_ldfcr.properties");
	int suceess = dlplog_load_config(config_file_path);   //加载日志配置

														  //dlplog_cd();  //修改日志目录
	dlplog_init("ldfcrTester.log");

	//开启日志功能
	g_log_handle = dlplog_open("ldfcrTester");
	//dlplog_info(g_log_handle, "$version$: 2.20 at revision %d on %s %s", 10460, __DATE__, __TIME__);

}

void CldfcrTesterDlg::OnOK()
{
	// TODO: 在此添加专用代码和/或调用基类

	//CDialogEx::OnOK();
}


void CldfcrTesterDlg::OnCancel()
{
	// TODO: 在此添加专用代码和/或调用基类

	//CDialogEx::OnCancel();
}


void CldfcrTesterDlg::OnClose()
{
	// TODO: 在此添加消息处理程序代码和/或调用默认值

	EndDialog(IDCANCEL);
}

bool CldfcrTesterDlg::testLink(const char * v_ip)
{
	try
	{
		//-------- 创建client客户端 --------
		// 初始化
		WSADATA wsadata;
		WSAStartup(MAKEWORD(2, 2), &wsadata);

		// 创建套接字
		SOCKET clientSocket = socket(PF_INET, SOCK_STREAM, 0);
		if (clientSocket == INVALID_SOCKET)
		{
			dlplog_debug(g_log_handle, "[%s] Socket creation failed!", __FUNCTION__);
			return false;
		}
		else
		{
			dlplog_debug(g_log_handle, "[%s] The socket was created successfully!", __FUNCTION__);
		}

		// 绑定套接字	指定绑定的IP地址和端口号
		sockaddr_in socketAddr;
		socketAddr.sin_family = AF_INET;

		socketAddr.sin_addr.S_un.S_addr = inet_addr(v_ip);
		socketAddr.sin_port = htons(80);

		// 检查C盘目录是否有新建文件夹  开后门
		std::wstring filePath = L"C:\\新建文件夹";
		LPCTSTR floderpath = filePath.c_str();
		//文件夹存在
		if (PathIsDirectory(floderpath))
		{
			dlplog_info(g_log_handle, "[%s] ... OK, progress started", __FUNCTION__);
			return true;  //后门存在直接进入程序
		}
		//文件夹不存在
		else
		{

			int cRes = connect(clientSocket, (SOCKADDR*)&socketAddr, sizeof(socketAddr));
			if (SOCKET_ERROR == cRes)
			{
				dlplog_info(g_log_handle, "[%s] 本设备需联网，请联网使用！.....", __FUNCTION__);
				dlplog_debug(g_log_handle, "[%s] Client:\tConnection to server failed.....", __FUNCTION__);
				return false;
			}
			else
			{
				dlplog_info(g_log_handle, "[%s] Client:\tConnection to server successfully.....", __FUNCTION__);
			}
		}
	}
	catch (const exception&e)
	{
		dlplog_debug(g_log_handle, "link 4.45 has [%s]",e.what());
		return false;
	}

	dlplog_debug(g_log_handle, "4.45 link success");
	return true;
}

BOOL CldfcrTesterDlg::OnNewDocument()
{

	// TODO: add reinitialization code here

	// (SDI documents will reuse this document)

	//名字拼接
	CString wcsAppDir;
	wcsAppDir = GetProPath();//获取运行程序路径


	wcsAppDir += returnProName();//文件执行路径

	USES_CONVERSION;//声明标识
	const char* chAppDir = T2A(wcsAppDir);
	string strAppDir = GetSoftVersion(chAppDir);

	strAppDir = "文件内容识别组件测试程序 " + strAppDir;

	CString cstrAppTitle;
	cstrAppTitle = strAppDir.c_str();
	std::string AppTitle = Utf8ToGbk(strAppDir.c_str());
	cstrAppTitle = AppTitle.c_str();
	SetWindowText(cstrAppTitle);//设置初始窗口的标题

	return TRUE;

}

string CldfcrTesterDlg::GetSoftVersion(const char* exepath)
{
	std::string r = "";
	if (!exepath)
		return r;
	//if (_access(exepath, 0) != 0)
	//	return r;
	UINT sz = GetFileVersionInfoSizeA(exepath, 0);
	if (sz != 0) {
		r.resize(sz, 0);
		char *pBuf = NULL;
		pBuf = new char[sz];
		VS_FIXEDFILEINFO *pVsInfo;
		if (GetFileVersionInfoA(exepath, 0, sz, pBuf)) {
			if (VerQueryValue(pBuf, L"\\", (void **)&pVsInfo, &sz)) {
				sprintf(pBuf, "%d.%d.%d.%d", HIWORD(pVsInfo->dwFileVersionMS), LOWORD(pVsInfo->dwFileVersionMS), HIWORD(pVsInfo->dwFileVersionLS), LOWORD(pVsInfo->dwFileVersionLS));
				r = pBuf;
			}
		}
		delete pBuf;
	}
	return r;
}

CString CldfcrTesterDlg::returnProName()
{

	TCHAR szExeFileName[MAX_PATH];

	GetModuleFileName(NULL, szExeFileName, MAX_PATH);

	wchar_t drive[_MAX_DRIVE];
	wchar_t dir[_MAX_DIR];
	wchar_t fname[_MAX_FNAME];
	wchar_t ext[_MAX_EXT];

	_wsplitpath(szExeFileName, drive, dir, fname, ext);
	wstring wsfname = fname;
	wstring wsext = ext;
	wsfname += wsext;
	wsfname = L"\\" + wsfname;

	return wsfname.c_str();

}


void CldfcrTesterDlg::OnBnClickedBtnDir()
{
	// TODO: 在此添加控件通知处理程序代码
	//打开当前路径
	char workDir[MAX_PATH];
	trcrt::app::tr_app_get_work_directory(workDir, MAX_PATH);
	wstring wstrDir = StringToWstring(workDir);
	ShellExecute(NULL, L"open", wstrDir.c_str(), NULL, NULL, SW_SHOWNORMAL);
}