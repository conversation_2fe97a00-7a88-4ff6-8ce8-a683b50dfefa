@echo off
echo ========================================
echo 组件缺失诊断脚本
echo ========================================
echo.

echo 正在检查缺失组件的状态...
echo.

set TARGET_DIR=D:\TEST_BAG\Detector_x86Win32

echo === 检查TrCompressFilter ===
if exist "%TARGET_DIR%\TrCompressFilter.dll" (
    echo [存在] TrCompressFilter.dll
    dir "%TARGET_DIR%\TrCompressFilter.dll"
) else (
    echo [缺失] TrCompressFilter.dll 不存在
)
echo.

echo === 检查TrArchive ===
if exist "%TARGET_DIR%\TrArchive.dll" (
    echo [存在] TrArchive.dll
    dir "%TARGET_DIR%\TrArchive.dll"
) else (
    echo [缺失] TrArchive.dll 不存在
)
echo.

echo === 检查DlpSCR ===
if exist "%TARGET_DIR%\DlpSCR.dll" (
    echo [存在] DlpSCR.dll
    dir "%TARGET_DIR%\DlpSCR.dll"
) else (
    echo [缺失] DlpSCR.dll 不存在
)
echo.

echo === 检查lua相关文件 ===
if exist "%TARGET_DIR%\lua.dll" (
    echo [存在] lua.dll
    dir "%TARGET_DIR%\lua.dll"
) else (
    echo [缺失] lua.dll 不存在
)

if exist "%TARGET_DIR%\liblua.dll" (
    echo [存在] liblua.dll
    dir "%TARGET_DIR%\liblua.dll"
) else (
    echo [缺失] liblua.dll 不存在
)
echo.

echo === 检查所有Tr系列过滤器 ===
for %%f in (TrCadFilter.dll TrCompressFilter.dll TrOLEFilter.dll TrOOXMLFilter.dll TrPdfFilter.dll TrRtfFilter.dll TrTextExtractor.dll TrTxtFilter.dll TrArchive.dll TrODFFilter.dll TrOCRFilter.dll) do (
    if exist "%TARGET_DIR%\%%f" (
        echo [存在] %%f
    ) else (
        echo [缺失] %%f
    )
)
echo.

echo === 检查OCR相关组件 ===
for %%f in (DlpOCR.dll DlpSCR.dll TrOCRFilter.dll) do (
    if exist "%TARGET_DIR%\%%f" (
        echo [存在] %%f
    ) else (
        echo [缺失] %%f
    )
)
echo.

echo === 检查基础库组件 ===
for %%f in (lua.dll liblua.dll pcre.dll libpcre.dll jieba.dll libjieba.dll libsvm.dll svm.dll) do (
    if exist "%TARGET_DIR%\%%f" (
        echo [存在] %%f
    ) else (
        echo [缺失] %%f
    )
)
echo.

echo === 检查lib子目录 ===
if exist "%TARGET_DIR%\lib" (
    echo [存在] lib子目录
    echo lib目录内容：
    dir "%TARGET_DIR%\lib" /b
    
    if exist "%TARGET_DIR%\lib\dlpcomm" (
        echo [存在] lib\dlpcomm子目录
        echo lib\dlpcomm目录内容：
        dir "%TARGET_DIR%\lib\dlpcomm" /b
    ) else (
        echo [缺失] lib\dlpcomm子目录不存在
    )
) else (
    echo [缺失] lib子目录不存在
)
echo.

echo === 使用ProcessExplorer风格的DLL检查 ===
echo 正在检查当前进程加载的DLL...
echo 注意：某些组件可能是按需加载的，只有在实际使用时才会被加载到进程中。
echo.

echo === 建议 ===
echo 1. 如果组件文件存在但显示"no version number has been established"：
echo    - 可能是按需加载的组件，只有在实际使用时才会被加载
echo    - 可能是版本信息获取失败
echo.
echo 2. 如果组件文件不存在：
echo    - 检查部署是否完整
echo    - 检查组件是否在其他目录中
echo.
echo 3. 对于lua组件：
echo    - 检查是否为lua.dll或liblua.dll
echo    - 某些版本可能使用不同的命名
echo.

pause
