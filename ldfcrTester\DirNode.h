#pragma once
 
#include <string>

//遍历文件夹的树形节点结构
class CDirNode
{
public:
#ifdef _WIN32
    CDirNode(const wchar_t* path_or_name);
    CDirNode(const std::wstring path_or_name);
#elif defined __GNUC__
    CDirNode(const char* path_or_name);
    CDirNode(const std::string path_or_name);
#endif

public:
    void release();//释放并删除节点

public:
    bool isDealed() { return this->dealed_; }
    void setDealed(bool dealed){ this->dealed_ = dealed; }
    CDirNode* getChilds(){ return this->childs_; }

#ifdef _WIN32
    std::wstring getFullPath();
#elif defined (__GNUC__)
    std::string getFullPath();
#endif

public:
    CDirNode* detach();
#ifdef _WIN32
    void addChild(const std::wstring& dir_name);
#elif defined __GNUC__
    void addChild(const std::string& dir_name);
#endif

private:
    CDirNode*    prev_;      //链表上一个节点，平行目录
    CDirNode*    next_;      //链表下一个节点，平行目录

private:
    CDirNode*    parent_;    //父节点
    CDirNode*    childs_;    //第一个孩子节点
    CDirNode*    last_child_;//最后一个孩子节点

private:
    bool            dealed_;   //已经检索过
#ifdef WIN32
    std::wstring    path_;  //路径（可以是全路径，也可以是当前目录名）
#elif defined (__GNUC__)
    std::string    path_;
#endif
};
