<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>MainWindow</class>
 <widget class="QMainWindow" name="MainWindow">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>792</width>
    <height>558</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>文件内容识别组件测试程序</string>
  </property>
  <widget class="QWidget" name="centralWidget">
   <layout class="QGridLayout" name="gridLayout_5">
    <item row="0" column="0">
     <layout class="QVBoxLayout" name="verticalLayout_4" stretch="2,2,4,2">
      <item>
       <widget class="QFrame" name="frame">
        <property name="frameShape">
         <enum>QFrame::StyledPanel</enum>
        </property>
        <property name="frameShadow">
         <enum>QFrame::Raised</enum>
        </property>
        <layout class="QGridLayout" name="gridLayout">
         <item row="0" column="0">
          <layout class="QVBoxLayout" name="verticalLayout" stretch="1,0">
           <item>
            <layout class="QHBoxLayout" name="horizontalLayout" stretch="0,0">
             <item>
              <widget class="QLabel" name="label">
               <property name="enabled">
                <bool>true</bool>
               </property>
               <property name="text">
                <string>默认策略文件</string>
               </property>
              </widget>
             </item>
             <item>
              <widget class="QTextEdit" name="textEdit_StrategyFilPath">
               <property name="enabled">
                <bool>false</bool>
               </property>
              </widget>
             </item>
            </layout>
           </item>
           <item>
            <layout class="QHBoxLayout" name="horizontalLayout_2" stretch="0,0,0,1">
             <item>
              <widget class="QPushButton" name="btnViewStrategy">
               <property name="text">
                <string>查看策略</string>
               </property>
              </widget>
             </item>
             <item>
              <widget class="QPushButton" name="btnUpdateStrategy">
               <property name="text">
                <string>更新策略</string>
               </property>
              </widget>
             </item>
             <item>
              <widget class="QPushButton" name="btnTextTest">
               <property name="text">
                <string>检测文本</string>
               </property>
              </widget>
             </item>
             <item>
              <spacer name="horizontalSpacer">
               <property name="orientation">
                <enum>Qt::Horizontal</enum>
               </property>
               <property name="sizeHint" stdset="0">
                <size>
                 <width>40</width>
                 <height>20</height>
                </size>
               </property>
              </spacer>
             </item>
            </layout>
           </item>
          </layout>
         </item>
        </layout>
       </widget>
      </item>
      <item>
       <widget class="QFrame" name="frame_2">
        <property name="frameShape">
         <enum>QFrame::StyledPanel</enum>
        </property>
        <property name="frameShadow">
         <enum>QFrame::Raised</enum>
        </property>
        <layout class="QGridLayout" name="gridLayout_2">
         <item row="0" column="0">
          <layout class="QHBoxLayout" name="horizontalLayout_3">
           <item>
            <widget class="QLabel" name="label_2">
             <property name="text">
              <string>待检测文件</string>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QTextEdit" name="textEdit_DetectedFilePath">
             <property name="enabled">
              <bool>false</bool>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QPushButton" name="btnSelectFile">
             <property name="text">
              <string>…</string>
             </property>
            </widget>
           </item>
          </layout>
         </item>
         <item row="1" column="0">
          <layout class="QHBoxLayout" name="horizontalLayout_4" stretch="0,0,0,0,0,1">
           <item>
            <widget class="QPushButton" name="btnDetectFile">
             <property name="text">
              <string>检测文件</string>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QLineEdit" name="lineEdit_file_count"/>
           </item>
           <item>
            <widget class="QLabel" name="label_3">
             <property name="text">
              <string>次，耗时</string>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QLineEdit" name="lineEdit_file_time">
             <property name="enabled">
              <bool>false</bool>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QLabel" name="label_4">
             <property name="text">
              <string>秒</string>
             </property>
            </widget>
           </item>
           <item>
            <spacer name="horizontalSpacer_2">
             <property name="orientation">
              <enum>Qt::Horizontal</enum>
             </property>
             <property name="sizeHint" stdset="0">
              <size>
               <width>40</width>
               <height>20</height>
              </size>
             </property>
            </spacer>
           </item>
          </layout>
         </item>
        </layout>
       </widget>
      </item>
      <item>
       <widget class="QFrame" name="frame_3">
        <property name="frameShape">
         <enum>QFrame::StyledPanel</enum>
        </property>
        <property name="frameShadow">
         <enum>QFrame::Raised</enum>
        </property>
        <layout class="QGridLayout" name="gridLayout_6">
         <item row="0" column="0">
          <layout class="QVBoxLayout" name="verticalLayout_2">
           <item>
            <layout class="QHBoxLayout" name="horizontalLayout_6">
             <item>
              <widget class="QLabel" name="label_5">
               <property name="text">
                <string>待检测目录</string>
               </property>
              </widget>
             </item>
             <item>
              <widget class="QTextEdit" name="textEdit_DetectedDir">
               <property name="enabled">
                <bool>false</bool>
               </property>
              </widget>
             </item>
             <item>
              <widget class="QPushButton" name="btnSelectDir">
               <property name="text">
                <string>…</string>
               </property>
              </widget>
             </item>
            </layout>
           </item>
           <item>
            <layout class="QHBoxLayout" name="horizontalLayout_5" stretch="0,0,0,0,0,0,0,0,1">
             <item>
              <widget class="QPushButton" name="btnDetectDir">
               <property name="text">
                <string>检测文件</string>
               </property>
              </widget>
             </item>
             <item>
              <widget class="QLabel" name="label_8">
               <property name="text">
                <string>已扫描</string>
               </property>
              </widget>
             </item>
             <item>
              <widget class="QLineEdit" name="lineEdit_dir_totallCount">
               <property name="enabled">
                <bool>false</bool>
               </property>
              </widget>
             </item>
             <item>
              <widget class="QLabel" name="label_7">
               <property name="text">
                <string>个文件，检测到</string>
               </property>
              </widget>
             </item>
             <item>
              <widget class="QLineEdit" name="lineEdit_dir_sensFilesCounts">
               <property name="enabled">
                <bool>false</bool>
               </property>
              </widget>
             </item>
             <item>
              <widget class="QLabel" name="label_9">
               <property name="text">
                <string>个敏感文件，耗时</string>
               </property>
              </widget>
             </item>
             <item>
              <widget class="QLineEdit" name="lineEdit_Dir_time">
               <property name="enabled">
                <bool>false</bool>
               </property>
              </widget>
             </item>
             <item>
              <widget class="QLabel" name="label_6">
               <property name="text">
                <string>秒</string>
               </property>
              </widget>
             </item>
             <item>
              <spacer name="horizontalSpacer_3">
               <property name="orientation">
                <enum>Qt::Horizontal</enum>
               </property>
               <property name="sizeHint" stdset="0">
                <size>
                 <width>40</width>
                 <height>20</height>
                </size>
               </property>
              </spacer>
             </item>
            </layout>
           </item>
           <item>
            <layout class="QGridLayout" name="gridLayout_3">
             <item row="0" column="0">
              <widget class="QLabel" name="label_11">
               <property name="text">
                <string>正在检测</string>
               </property>
              </widget>
             </item>
             <item row="0" column="1">
              <spacer name="horizontalSpacer_5">
               <property name="orientation">
                <enum>Qt::Horizontal</enum>
               </property>
               <property name="sizeHint" stdset="0">
                <size>
                 <width>40</width>
                 <height>20</height>
                </size>
               </property>
              </spacer>
             </item>
             <item row="1" column="0" colspan="2">
              <widget class="QTextEdit" name="textEdit_detecting">
               <property name="enabled">
                <bool>false</bool>
               </property>
              </widget>
             </item>
            </layout>
           </item>
          </layout>
         </item>
        </layout>
       </widget>
      </item>
      <item>
       <widget class="QFrame" name="frame_4">
        <property name="frameShape">
         <enum>QFrame::StyledPanel</enum>
        </property>
        <property name="frameShadow">
         <enum>QFrame::Raised</enum>
        </property>
        <layout class="QGridLayout" name="gridLayout_4">
         <item row="0" column="0">
          <layout class="QVBoxLayout" name="verticalLayout_3">
           <item>
            <layout class="QHBoxLayout" name="horizontalLayout_7">
             <item>
              <widget class="QLabel" name="label_10">
               <property name="text">
                <string>运行日志</string>
               </property>
              </widget>
             </item>
             <item>
              <spacer name="horizontalSpacer_4">
               <property name="orientation">
                <enum>Qt::Horizontal</enum>
               </property>
               <property name="sizeHint" stdset="0">
                <size>
                 <width>40</width>
                 <height>20</height>
                </size>
               </property>
              </spacer>
             </item>
            </layout>
           </item>
           <item>
            <widget class="QTextEdit" name="textEdit_log">
             <property name="enabled">
              <bool>false</bool>
             </property>
            </widget>
           </item>
          </layout>
         </item>
        </layout>
       </widget>
      </item>
     </layout>
    </item>
   </layout>
  </widget>
 </widget>
 <layoutdefault spacing="6" margin="11"/>
 <resources/>
 <connections>
  <connection>
   <sender>btnViewStrategy</sender>
   <signal>clicked()</signal>
   <receiver>MainWindow</receiver>
   <slot>viewStrategy()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>70</x>
     <y>114</y>
    </hint>
    <hint type="destinationlabel">
     <x>785</x>
     <y>147</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>btnUpdateStrategy</sender>
   <signal>clicked()</signal>
   <receiver>MainWindow</receiver>
   <slot>updateStrategy()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>151</x>
     <y>116</y>
    </hint>
    <hint type="destinationlabel">
     <x>786</x>
     <y>129</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>btnTextTest</sender>
   <signal>clicked()</signal>
   <receiver>MainWindow</receiver>
   <slot>TextTest()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>261</x>
     <y>117</y>
    </hint>
    <hint type="destinationlabel">
     <x>788</x>
     <y>106</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>btnSelectFile</sender>
   <signal>clicked()</signal>
   <receiver>MainWindow</receiver>
   <slot>selectFile()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>729</x>
     <y>195</y>
    </hint>
    <hint type="destinationlabel">
     <x>786</x>
     <y>210</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>btnDetectFile</sender>
   <signal>clicked()</signal>
   <receiver>MainWindow</receiver>
   <slot>startDetectFile()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>66</x>
     <y>262</y>
    </hint>
    <hint type="destinationlabel">
     <x>96</x>
     <y>283</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>btnDetectDir</sender>
   <signal>clicked()</signal>
   <receiver>MainWindow</receiver>
   <slot>startDetectDir()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>81</x>
     <y>397</y>
    </hint>
    <hint type="destinationlabel">
     <x>182</x>
     <y>421</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>btnSelectDir</sender>
   <signal>clicked()</signal>
   <receiver>MainWindow</receiver>
   <slot>selectDir()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>711</x>
     <y>334</y>
    </hint>
    <hint type="destinationlabel">
     <x>787</x>
     <y>339</y>
    </hint>
   </hints>
  </connection>
 </connections>
 <slots>
  <slot>viewStrategy()</slot>
  <slot>updateStrategy()</slot>
  <slot>TextTest()</slot>
  <slot>selectFile()</slot>
  <slot>startDetectFile()</slot>
  <slot>startDetectDir()</slot>
  <slot>selectDir()</slot>
 </slots>
</ui>
