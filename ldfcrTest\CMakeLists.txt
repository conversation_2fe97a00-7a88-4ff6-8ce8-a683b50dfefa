#cmake_minimum_required (VERSION 3.0)
cmake_minimum_required (VERSION 2.8)
project(ldfcrTest)

set(CMAKE_CXX_STANDARD 11)
add_definitions("-DUSING_INSTANCE")
add_definitions(-std=c++11)

SET(CMAKE_BUILD_TYPE Debug) #debug模式

# set(CMAKE_RUNTIME_OUTPUT_DIRECTORY ${CMAKE_SOURCE_DIR})


add_executable(ldfcrTest
    ${CMAKE_CURRENT_SOURCE_DIR}/src/ldfcrtest.h
    ${CMAKE_CURRENT_SOURCE_DIR}/src/ldfcrtest.cpp
	${CMAKE_CURRENT_SOURCE_DIR}/utils.h
    ${CMAKE_CURRENT_SOURCE_DIR}/utils.cpp
    #${CMAKE_CURRENT_SOURCE_DIR}/include/utils/JsonValueAdapter.cpp
    main.cpp
)

target_include_directories(ldfcrTest PRIVATE
    ${CMAKE_CURRENT_SOURCE_DIR}/../../../__devLibs_ldfcr/inc
)

#DlpULog ldfcr trcrt 拷贝到./lib/dlpcomm目录下
if (APPLE)
	find_library(ldfcr_LIBRARY ldfcr HINTS ${CMAKE_CURRENT_SOURCE_DIR}/../../../__devLibs_ldfcr/libs_x64)
    target_link_libraries(ldfcrTest PUBLIC ${ldfcr_LIBRARY})

	find_library(DlpULog_LIBRARY DlpULog HINTS ${CMAKE_CURRENT_SOURCE_DIR}/../../../__devLibs_ldfcr/libs_x64)
    target_link_libraries(ldfcrTest PUBLIC ${DlpULog_LIBRARY})
elseif(UNIX)
	IF(${CMAKE_SYSTEM_PROCESSOR} MATCHES "aarch64")
		message("linux arch")
		find_library(ldfcr_LIBRARY ldfcr HINTS ${CMAKE_CURRENT_SOURCE_DIR}/../../../__devLibs_ldfcr/aarch64/libs_x64)
		target_link_libraries(ldfcrTest PUBLIC ${ldfcr_LIBRARY})

        find_library(trcrt_LIBRARY trcrt HINTS ${CMAKE_CURRENT_SOURCE_DIR}/../../../__devLibs_ldfcr/aarch64/libs_x64)
        target_link_libraries(ldfcrTest PUBLIC ${trcrt_LIBRARY})

	find_library(z_LIBRARY z HINTS ${CMAKE_CURRENT_SOURCE_DIR}/../../../__devLibs_ldfcr/aarch64/lib3rds_x64)
    target_link_libraries(ldfcrTest PUBLIC ${z_LIBRARY})

	find_library(DlpULog_LIBRARY DlpULog HINTS ${CMAKE_CURRENT_SOURCE_DIR}/../../../__devLibs_ldfcr/aarch64/libs_x64)
        target_link_libraries(ldfcrTest PUBLIC ${DlpULog_LIBRARY})

		set(CMAKE_CXX_FLAGS "-fvisibility=hidden -l rt -pthread")	
	ELSEIF(${CMAKE_SYSTEM_PROCESSOR} MATCHES "x86_64")
		message("linux 64 bit")
		find_library(ldfcr_LIBRARY ldfcr HINTS ${CMAKE_CURRENT_SOURCE_DIR}/../../../__devLibs_ldfcr/libs_x64)
		target_link_libraries(ldfcrTest PUBLIC ${ldfcr_LIBRARY})	

		find_library(DlpULog_LIBRARY DlpULog HINTS ${CMAKE_CURRENT_SOURCE_DIR}/../../../__devLibs_ldfcr/libs_x64)
        target_link_libraries(ldfcrTest PUBLIC ${DlpULog_LIBRARY})

		set(CMAKE_CXX_FLAGS "-fvisibility=hidden -m64 -l rt -pthread")
	ELSEIF(${CMAKE_SYSTEM_PROCESSOR} MATCHES "i686")
		message("linux 32 bit")
		find_library(ldfcr_LIBRARY ldfcr HINTS ${CMAKE_CURRENT_SOURCE_DIR}/../../../__devLibs_ldfcr/libs_x86)
		target_link_libraries(ldfcrTest PUBLIC ${ldfcr_LIBRARY})

		find_library(DlpULog_LIBRARY DlpULog HINTS ${CMAKE_CURRENT_SOURCE_DIR}/../../../__devLibs_ldfcr/libs_x86)
        target_link_libraries(ldfcrTest PUBLIC ${DlpULog_LIBRARY})

		set(CMAKE_CXX_FLAGS "-fvisibility=hidden -l rt -pthread")
	ENDIF()
endif()

target_link_libraries(ldfcrTest PUBLIC m)

set_target_properties(${PROJECT_NAME} PROPERTIES LINK_FLAGS "-Wl,-rpath,./ -Wl,-rpath,./libs_x64/ -Wl,-rpath,./lib/dlpcomm -Wl,-rpath,../lib/dlpcomm")



#set(CMAKE_INSTALL_PREFIX ${CMAKE_CURRENT_SOURCE_DIR}/../..)

#install(TARGETS ${PROJECT_NAME} RUNTIME DESTINATION bin)
