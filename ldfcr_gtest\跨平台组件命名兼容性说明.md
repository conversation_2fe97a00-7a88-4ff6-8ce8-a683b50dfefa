# 跨平台组件命名兼容性说明

## 🎯 问题背景

在跨平台开发中，同一个组件在不同操作系统下的文件命名规则不同：

### Linux/Mac命名规则
- 大多数组件：`lib{组件名}.so` 或 `lib{组件名}.dylib`
- 特殊组件：`{组件名}.so` 或 `{组件名}.dylib`

### Windows命名规则
- 大多数组件：`{组件名}.dll`
- 某些组件：`lib{组件名}.dll`（保持与Linux/Mac一致）

## 📊 具体命名对比

| 预定义组件名 | Linux/Mac文件名 | Windows文件名 | 说明 |
|-------------|----------------|---------------|------|
| `ldfcr` | `libldfcr.so` | `ldfcr.dll` | Windows去掉lib前缀 |
| `svm` | `libsvm.so` | `libsvm.dll` | Windows保持lib前缀 |
| `lua` | `liblua.so` | `lua.dll` | Windows去掉lib前缀 |
| `jieba` | `libjieba.so` | `jieba.dll` | Windows去掉lib前缀 |
| `TrArchive` | `TrArchive.so` | `TrArchive.dll` | 跨平台一致 |
| `DlpPolicyEngine` | `libDlpPolicyEngine.so` | `DlpPolicyEngine.dll` | Windows去掉lib前缀 |

## 🔧 解决方案

### 1. 智能匹配逻辑

```cpp
// 对于预定义组件名"svm"，会尝试匹配：
// 1. 精确匹配：actualName == "svm"
// 2. lib前缀匹配：actualName == "libsvm"  
// 3. 包含匹配：actualName.find("svm") != string::npos

for (int i = 0; i < actualModuleNames.size(); ++i) {
    string actualName = actualModuleNames[i];
    
    // 精确匹配
    if (actualName == libName) {
        exactFound = true;
        break;
    }
    
    // Windows跨平台命名兼容性检查
    string expectedLibName = "lib" + libName;
    if (actualName == expectedLibName) {
        exactFound = true;
        break;
    }
    
    // 改名检测
    if (actualName.find(libName) != string::npos && 
        actualName != libName && 
        actualName != expectedLibName) {
        renamedFound = true;
    }
}
```

### 2. 匹配优先级

1. **精确匹配**：`actualName == libName`
2. **lib前缀匹配**：`actualName == "lib" + libName`
3. **改名检测**：包含原名但不是上述两种情况

## 📝 测试用例

### 测试用例1：libsvm.dll
```
预定义组件名：svm
实际文件名：libsvm.dll
匹配结果：lib前缀匹配成功
输出：26. libsvm.dll 3.50.2402.4 2025-03-12 17:21:09 {guid}
```

### 测试用例2：lua.dll
```
预定义组件名：lua
实际文件名：lua.dll
匹配结果：精确匹配成功
输出：24. lua.dll 5.4.3.0 2021-03-30 02:25:53 {guid}
```

### 测试用例3：libsvm_v2.dll（改名）
```
预定义组件名：svm
实际文件名：libsvm_v2.dll
匹配结果：改名检测
输出：26. svm ERROR: Component has been renamed to libsvm_v2.dll
```

### 测试用例4：TrArchive.dll
```
预定义组件名：TrArchive
实际文件名：TrArchive.dll
匹配结果：精确匹配成功
输出：17. TrArchive.dll 1.0.0.1 2025-07-23 11:25:03 {guid}
```

## 🚀 技术优势

### 1. 跨平台一致性
- 使用相同的预定义组件列表
- 智能处理不同平台的命名差异
- 保持输出格式的一致性

### 2. 向后兼容性
- 支持原有的精确匹配
- 新增lib前缀匹配
- 不影响现有功能

### 3. 智能错误检测
- 能够区分正常命名和改名
- 对改名组件提供明确的错误信息
- 避免误报

## 🔍 实际应用场景

### 场景1：标准部署
```
=== Baseline Libraries (Program End) ===
24. lua.dll               5.4.3.0       2021-03-30 02:25:53  {guid}
   Location: D:\TEST_BAG\Detector_x86Win32\lua.dll
26. libsvm.dll            3.50.2402.4   2025-03-12 17:21:09  {guid}
   Location: D:\TEST_BAG\Detector_x86Win32\libsvm.dll
```

### 场景2：组件改名
```
=== Baseline Libraries (Program End) ===
24. lua.dll               5.4.3.0       2021-03-30 02:25:53  {guid}
   Location: D:\TEST_BAG\Detector_x86Win32\lua.dll
26. svm ERROR: Component has been renamed to libsvm_new.dll
   Location: D:\TEST_BAG\Detector_x86Win32\libsvm_new.dll
```

### 场景3：组件缺失
```
=== Baseline Libraries (Program End) ===
24. lua.dll               5.4.3.0       2021-03-30 02:25:53  {guid}
   Location: D:\TEST_BAG\Detector_x86Win32\lua.dll
26. svm no version number has been established
```

## 📋 验证清单

在测试时，请验证以下组件的正确匹配：

- [ ] `libsvm.dll` 应该匹配预定义的 `svm`
- [ ] `lua.dll` 应该匹配预定义的 `lua`
- [ ] `jieba.dll` 应该匹配预定义的 `jieba`
- [ ] `trcrt.dll` 应该匹配预定义的 `trcrt`
- [ ] `memstream.dll` 应该匹配预定义的 `memstream`
- [ ] `TrArchive.dll` 应该匹配预定义的 `TrArchive`

## 🎉 总结

通过实现跨平台命名兼容性处理，解决了以下问题：

✅ **正确识别libsvm.dll**：不再显示"no version number has been established"
✅ **保持跨平台一致性**：使用相同的预定义组件列表
✅ **智能改名检测**：能够区分正常命名和改名情况
✅ **向后兼容**：不影响现有的匹配逻辑

这个改进确保了Windows版本能够正确识别所有实际加载的组件，特别是那些在不同平台下有不同命名规则的组件。
