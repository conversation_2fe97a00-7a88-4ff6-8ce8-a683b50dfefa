#pragma once
#include "DragEdit.h"
#include "afxwin.h"
#include <string>
#include <fstream>

// InPutCheckTextDlg 对话框

class InPutCheckTextDlg : public CDialog
{
	DECLARE_DYNAMIC(InPutCheckTextDlg)

public:
	InPutCheckTextDlg(CWnd* pParent = NULL);   // 标准构造函数
	virtual ~InPutCheckTextDlg();

// 对话框数据
#ifdef AFX_DESIGN_TIME
	enum { IDD = IDD_DIALOG_TEXT_CHECK };
#endif

protected:
	virtual void DoDataExchange(CDataExchange* pDX);    // DDX/DDV 支持

	DECLARE_MESSAGE_MAP()
public:
	virtual void OnOK();
	virtual void OnCancel();

	CDragEdit m_cEdit;
	CString m_Address;
	CEdit m_EditContext;

	afx_msg std::string GbkToUtf8(const char *src_str);
	afx_msg std::string Utf8ToGbk(const char *src_str);
	afx_msg void OnBnClickedOpen();
	afx_msg void OnBnClickedInput();
	CString m_strCheckText;
	afx_msg void OnClose();
};
