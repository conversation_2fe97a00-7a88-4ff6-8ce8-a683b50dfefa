# Windows组件信息输出功能说明

## 功能概述

为Windows系统实现了与Linux/Mac平台一致的组件版本信息输出功能，支持程序头部和尾部的基线库信息对比，便于检测运行过程中动态加载的组件变化。

## 实现方案

### 设计思路
- **复用现有代码**：基于已有的PEinfo类进行扩展，而非重新实现
- **保持格式一致**：输出格式与用户要求的四列格式完全一致
- **跨平台统一**：Windows下使用PEinfo，Linux/Mac下使用libaryVersionInfo

### 核心功能
1. **按预定义顺序输出**：严格按照26个核心组件的优先级顺序
2. **序号和路径显示**：每个组件带序号，下一行显示完整路径
3. **动态检测能力**：能够检测程序运行过程中加载的新组件
4. **头尾对比功能**：程序开始和结束时都输出，便于对比

## 代码修改

### 1. PEinfo.h 新增方法
```cpp
// 新增方法：按预定义顺序输出组件信息，包含序号和完整路径
void GetPEinfoOrdered();

// 获取预定义的组件顺序（与libaryVersionInfo保持一致）
static const vector<string> getOrderedLibs();
```

### 2. PEinfo.cpp 核心实现
- **getOrderedLibs()**：返回26个组件的预定义顺序
- **GetPEinfoOrdered()**：按顺序输出，包含序号和完整路径

### 3. main.cpp 调用逻辑
```cpp
#ifdef WIN32
// 程序开始时
printf("\n=== Baseline Libraries (Program Startup) ===\n");
g_baselinePEinfo = new PEinfo();
g_baselinePEinfo->GetPEinfoOrdered();

// 程序结束时
printf("\n=== Baseline Libraries (Program End) ===\n");
g_baselinePEinfo->GetPEinfoOrdered();
#endif
```

## 输出格式

### 标准输出格式
```
=== Baseline Libraries (Program Startup) ===
1. ldfcr.dll             5.2.2507.18   2025-07-23 11:25:03  {cc4452c8-d92b-49e6-9505-09600b620d74}
   Location: D:\TEST_BAG\ldfcr2025-07-23[09.22.42]_5.02.250716.SC\ldfcr.dll
2. DlpPolicyEngine.dll   5.2.2507.22   2025-07-23 11:25:19  {8ce80b3e-2f6a-4260-8987-faf900013b31}
   Location: D:\TEST_BAG\ldfcr2025-07-23[09.22.42]_5.02.250716.SC\DlpPolicyEngine.dll
...
```

### 格式说明
- **序号**：从1开始递增
- **四列数据**：文件名、版本号、时间、GUID
- **完整路径**：每个组件下一行显示"Location: 完整路径"
- **预定义顺序**：按getOrderedLibs()中定义的26个组件顺序

## 组件顺序

按以下优先级顺序输出（共26个核心组件）：
```
1. ldfcr
2. DlpPolicyEngine  
3. KWRuleEngine
4. RegexRuleEngine
5. FilePropEngine
6. FileFpRuleEngine
7. SVMRuleEngine
8. FpDbRuleEngine
9. TrCadFilter
10. TrCompressFilter
... (完整列表见代码)
```

## 动态检测能力

### 检测原理
- **程序启动时**：扫描当前目录，记录已加载的组件
- **程序结束时**：再次扫描，对比组件变化
- **动态加载检测**：能够发现测试过程中新加载的DLL

### 实际效果
从测试结果可以看到：
- 程序启动时：12个组件
- 程序结束时：15个组件
- 新增组件：FileFpRuleEngine.dll、jieba.dll、libsvm.dll等

## 技术特点

### 1. 轻量级实现
- **零新增依赖**：完全基于现有PEinfo类
- **最小代码修改**：只添加了必要的新方法
- **保持兼容性**：不影响原有GetPEinfo()方法

### 2. 跨平台一致性
- **统一接口**：Windows和Linux/Mac使用相同的调用方式
- **一致顺序**：所有平台按相同的组件优先级顺序输出
- **格式统一**：输出格式在所有平台保持一致

### 3. 可维护性
- **清晰注释**：所有新增代码都有详细的功能说明
- **模块化设计**：新功能独立封装，不影响原有逻辑
- **易于扩展**：可以方便地添加新的组件或修改输出格式

## 使用场景

1. **版本管理**：确保部署环境中组件版本的一致性
2. **问题诊断**：快速定位组件版本或加载问题
3. **回归测试**：对比测试前后的组件加载情况
4. **部署验证**：验证所有必需组件是否正确部署

## 总结

此功能成功实现了Windows下的组件版本信息输出，完全满足了用户的需求：
- ✅ 序号输出
- ✅ 完整路径显示  
- ✅ 按预定义顺序输出
- ✅ 只显示实际依赖的组件
- ✅ 头尾对比功能
- ✅ 动态检测能力

通过复用现有PEinfo类，实现了轻量级、高效、可维护的解决方案。
