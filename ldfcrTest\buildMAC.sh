#!/bin/bash

# set -x

BUILD_TYPE="Release"
#BUILD_TYPE="Debug"
build_dir=build
olddir=`pwd`

export CC=/usr/bin/gcc
export CXX=/usr/bin/g++

#rm -f libs/*

# 创建构建目录
if [ ! -e $build_dir ]
then
	mkdir $build_dir
#else
	#rm -rf $build_dir/*
fi

# 构建项目
mkdir -p $build_dir
cd $build_dir

cmake -DCMAKE_BUILD_TYPE=$BUILD_TYPE \
      -DCMAKE_EXPORT_COMPILE_COMMANDS=ON \
      -DCMAKE_INSTALL_RPATH:STRING=".;./lib/;./lib32/;./libs_x64/;../libs" \
      $olddir
	  #$olddir/$src

# 编译和安装
make -j4
make install
		
# 复制文件到安装目录
cp -f $olddir/ldfcrTest  $olddir/bin/ldfcrTestMac

# 返回原始工作目录
cd $olddir

echo `date`
