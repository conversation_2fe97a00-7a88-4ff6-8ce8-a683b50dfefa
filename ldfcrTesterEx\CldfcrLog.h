#pragma once
#include "afxwin.h"


// CldfcrLog 对话框

class CldfcrLog : public CDialog
{
	DECLARE_DYNAMIC(CldfcrLog)

public:
	CldfcrLog(CWnd* pParent = NULL);   // 标准构造函数
	CldfcrLog(CString v_vstr,CWnd* pParent = NULL);   // 标准构造函数
	virtual ~CldfcrLog();

// 对话框数据
#ifdef AFX_DESIGN_TIME
	enum { IDD = IDD_DIALOG_LOG };
#endif

protected:
	virtual void DoDataExchange(CDataExchange* pDX);    // DDX/DDV 支持

	DECLARE_MESSAGE_MAP()
public:
	CString m_showLogPath;  //日志路径
	CEdit m_editLog;
	afx_msg void OnBnClickedLogFlash();
	afx_msg int CountLogLine();
	virtual BOOL OnInitDialog();
	afx_msg void OnEnChangeEditLog();
	afx_msg void OnClose();
	afx_msg void OnBnClickedLogExit();
	afx_msg void OnDestroy();
	virtual void OnCancel();
};
