#include "stdafx.h"
#include "CodeT.h"

string GbkToUtf8(const char *src_str)
{
	int len = MultiByteToWideChar(CP_ACP, 0, src_str, -1, NULL, 0);
	wchar_t* wstr = new wchar_t[len + 1];
	memset(wstr, 0, len + 1);
	MultiByteToWideChar(CP_ACP, 0, src_str, -1, wstr, len);
	len = WideCharToMultiByte(CP_UTF8, 0, wstr, -1, NULL, 0, NULL, NULL);
	char* str = new char[len + 1];
	memset(str, 0, len + 1);
	WideCharToMultiByte(CP_UTF8, 0, wstr, -1, str, len, NULL, NULL);
	string strTemp = str;
	if (wstr) delete[] wstr;
	if (str) delete[] str;
	return strTemp;
}

string Utf8ToGbk(const char *src_str)
{
	int len = MultiByteToWideChar(CP_UTF8, 0, src_str, -1, NULL, 0);
	wchar_t* wszGBK = new wchar_t[len + 1];
	memset(wszGBK, 0, len * 2 + 2);
	MultiByteToWideChar(CP_UTF8, 0, src_str, -1, wszGBK, len);
	len = WideCharToMultiByte(CP_ACP, 0, wszGBK, -1, NULL, 0, NULL, NULL);
	char* szGBK = new char[len + 1];
	memset(szGBK, 0, len + 1);
	WideCharToMultiByte(CP_ACP, 0, wszGBK, -1, szGBK, len, NULL, NULL);
	string strTemp(szGBK);

	if (wszGBK)
		delete[] wszGBK;

	if (szGBK)
		delete[] szGBK;
	return strTemp;
}

wstring Utf8ToUnicode(const string& utf8_str)
{
	int len = MultiByteToWideChar(CP_UTF8, 0, utf8_str.c_str(), -1, NULL, 0);
	wchar_t* wszUnicode = new wchar_t[len];
	memset(wszUnicode, 0, len * sizeof(wchar_t));
	MultiByteToWideChar(CP_UTF8, 0, utf8_str.c_str(), -1, wszUnicode, len);
	wstring unicodeStr(wszUnicode);
	delete[] wszUnicode;
	return unicodeStr;
}

string UnicodeToUtf8(const wstring& unicode_str)
{
	int len = WideCharToMultiByte(CP_UTF8, 0, unicode_str.c_str(), -1, NULL, 0, NULL, NULL);
	char* szUtf8 = new char[len];
	memset(szUtf8, 0, len * sizeof(char));
	WideCharToMultiByte(CP_UTF8, 0, unicode_str.c_str(), -1, szUtf8, len, NULL, NULL);
	string utf8Str(szUtf8);
	delete[] szUtf8;
	return utf8Str;
}

string wstringToString(const std::wstring v_wstr)
{
	unsigned len = v_wstr.size() * 4;
	setlocale(LC_CTYPE, "");
	char *p = new char[len];
	wcstombs(p, v_wstr.c_str(), len);
	std::string str1(p);
	delete[] p;
	return str1;
}

wstring StringToWstring(const string v_str)
{// string转wstring
	unsigned len = v_str.size() * 2;// 预留字节数
	setlocale(LC_CTYPE, "");     //必须调用此函数
	wchar_t* p = new wchar_t[len];// 申请一段内存存放转换后的字符串
	mbstowcs(p, v_str.c_str(), len);// 转换
	wstring str1(p);
	delete[] p;// 释放申请的内存
	return str1;
}

string toBase64(const char * src_str)
{
	string str = src_str;

	int len = MultiByteToWideChar(CP_ACP, 0, str.c_str(), -1, NULL, 0);
	wchar_t* wstr = new wchar_t[len + 1];
	memset(wstr, 0, len + 1);
	MultiByteToWideChar(CP_ACP, 0, str.c_str(), -1, wstr, len);
	len = WideCharToMultiByte(CP_UTF8, 0, wstr, -1, NULL, 0, NULL, NULL);
	char* Data1 = new char[len + 1];
	memset(Data1, 0, len + 1);
	WideCharToMultiByte(CP_UTF8, 0, wstr, -1, Data1, len, NULL, NULL);

	int DataByte = len - 1;
	unsigned char * Data = new unsigned char[DataByte];
	memcpy(Data, Data1, DataByte);

	//编码表
	const char EncodeTable[] = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/";
	//返回值
	std::string strEncode;
	unsigned char Tmp[4] = { 0 };
	int LineLength = 0;
	for (int i = 0; i < (int)(DataByte / 3); i++)
	{
		Tmp[1] = *Data++;
		Tmp[2] = *Data++;
		Tmp[3] = *Data++;
		strEncode += EncodeTable[Tmp[1] >> 2];
		strEncode += EncodeTable[((Tmp[1] << 4) | (Tmp[2] >> 4)) & 0x3F];
		strEncode += EncodeTable[((Tmp[2] << 2) | (Tmp[3] >> 6)) & 0x3F];
		strEncode += EncodeTable[Tmp[3] & 0x3F];
		//if (LineLength += 4, LineLength == 76) { strEncode += "\r\n"; LineLength = 0; }
	}
	//对剩余数据进行编码
	int Mod = DataByte % 3;
	if (Mod == 1)
	{
		Tmp[1] = *Data++;
		strEncode += EncodeTable[(Tmp[1] & 0xFC) >> 2];
		strEncode += EncodeTable[((Tmp[1] & 0x03) << 4)];
		strEncode += "==";
	}
	else if (Mod == 2)
	{
		Tmp[1] = *Data++;
		Tmp[2] = *Data++;
		strEncode += EncodeTable[(Tmp[1] & 0xFC) >> 2];
		strEncode += EncodeTable[((Tmp[1] & 0x03) << 4) | ((Tmp[2] & 0xF0) >> 4)];
		strEncode += EncodeTable[((Tmp[2] & 0x0F) << 2)];
		strEncode += "=";
	}
	delete[] Data1;
	delete[] Data;
	return strEncode;
}



