#pragma once
#include <stdio.h>
#include <string>
#include <vector>
#include "src/platformDefined.h"

#define MAX_PATH_LEN 256

std::string GetCurPath();

std::string ContextRead(const tChar* filePath);

void printToGbk(const char * data);  // 适配WINDOWS的GBK输出

void printToGbkWithN(const char * data);  // 适配WINDOWS附带换行符的GBK输出

std::string charToString(const char * data);

#if defined(__GNUC__)
//std::vector<std::string> read_dir(const tChar* dir);  //遍历当前文件夹

std::vector<std::string> trave_dir(const tChar* dir); //递归遍历文件夹

#elif defined(_WIN32)
std::vector<std::wstring> read_dir(const tChar* dir);

std::string wstring_to_string(const wchar_t * data);

std::wstring string_to_wstring(const std::string v_str);

std::string UtfToGbk(const std::string v_strUtf);
#endif