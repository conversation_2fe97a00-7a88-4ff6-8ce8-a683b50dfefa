﻿<?xml version="1.0" encoding="utf-8"?>
<ClassDiagram MajorVersion="1" MinorVersion="1">
  <Class Name="CAboutDlg" Collapsed="true">
    <Position X="9" Y="0.5" Width="1.5" />
    <TypeIdentifier>
      <HashCode>AAAAAAAAAAAAAABAABAAAAAAAAAAAAAAAAAAAAAAAAA=</HashCode>
      <FileName>ldfcrTesterDlg.cpp</FileName>
    </TypeIdentifier>
  </Class>
  <Class Name="CDirNode" Collapsed="true">
    <Position X="10.75" Y="0.5" Width="1.5" />
    <TypeIdentifier>
      <HashCode>FAAAxAAAIgAAAAAAABAgAAAKAEAAAAQgEAAAAAAAAAA=</HashCode>
      <FileName>DirNode.h</FileName>
    </TypeIdentifier>
  </Class>
  <Class Name="CInputCheckTextDlg" Collapsed="true">
    <Position X="14.25" Y="0.5" Width="1.5" />
    <TypeIdentifier>
      <HashCode>AAAAAAAEAAAAAAAAABAAAAAAAAAAABAAAAEEIAAAAAA=</HashCode>
      <FileName>InputCheckTextDlg.h</FileName>
    </TypeIdentifier>
  </Class>
  <Class Name="CldfcrTesterApp" Collapsed="true">
    <Position X="9" Y="1.5" Width="1.5" />
    <TypeIdentifier>
      <HashCode>AAAAAAAAAAAAAAAAAAAAAAAAAAAIAAQAAAAAAAAAAAA=</HashCode>
      <FileName>ldfcrTester.h</FileName>
    </TypeIdentifier>
  </Class>
  <Class Name="CldfcrTesterDlg" Collapsed="true">
    <Position X="10.75" Y="1.5" Width="1.5" />
    <TypeIdentifier>
      <HashCode>nQCAAAMAIEBhAhAiBBAQCACACQAgIAUAEwAAQBIQEAI=</HashCode>
      <FileName>ldfcrTesterDlg.h</FileName>
    </TypeIdentifier>
  </Class>
  <Class Name="CSimpleIniTempl&lt;SI_CHAR, SI_STRLESS, SI_CONVERTER&gt;" Collapsed="true">
    <Position X="3.5" Y="2.5" Width="1.5" />
    <TypeIdentifier>
      <HashCode>CBBSsYoBIADbBlAEAP5QEAAAAEFAAAFkBGlgcACABAA=</HashCode>
      <FileName>d:\VSCode\testtest\third-party\simpleini\SimpleIni.h</FileName>
    </TypeIdentifier>
  </Class>
  <Class Name="SI_ConvertA&lt;SI_CHAR&gt;" Collapsed="true">
    <Position X="10.75" Y="2.5" Width="1.5" />
    <TypeIdentifier>
      <HashCode>AAAAAAAIAAAAAAAAAAIABAAAAAAAgAAEAAAgAAIAAAA=</HashCode>
      <FileName>d:\VSCode\testtest\third-party\simpleini\SimpleIni.h</FileName>
    </TypeIdentifier>
  </Class>
  <Class Name="SI_ConvertW&lt;SI_CHAR&gt;" Collapsed="true">
    <Position X="12.5" Y="2.5" Width="1.5" />
    <TypeIdentifier>
      <HashCode>AAAAAAAIAAAAAAAAAAIAAAAAEAAAgAAEAAAgAAIAAAA=</HashCode>
      <FileName>d:\VSCode\testtest\third-party\simpleini\SimpleIni.h</FileName>
    </TypeIdentifier>
  </Class>
  <Class Name="CXFileOpenListView" Collapsed="true">
    <Position X="14.25" Y="1.5" Width="1.5" />
    <TypeIdentifier>
      <HashCode>AQAAAAAQEAIAAAAAAAAAAEAAICAAAQBAAAACAAAQAAA=</HashCode>
      <FileName>ui\XFileOpenListView.h</FileName>
    </TypeIdentifier>
  </Class>
  <Class Name="CXFolderDialog" Collapsed="true">
    <Position X="9" Y="2.5" Width="1.5" />
    <TypeIdentifier>
      <HashCode>AIAlICEEEBEJAAAAAJAIqIABEAwCAQAAQCIIyIAEoAE=</HashCode>
      <FileName>ui\XFolderDialog.h</FileName>
    </TypeIdentifier>
  </Class>
  <Class Name="CHistoryCombo" Collapsed="true">
    <Position X="12.5" Y="0.5" Width="1.5" />
    <TypeIdentifier>
      <HashCode>AAMCECAEAgAAABAAIQAAQCwCgEQQAIAAAAAAAAECAAE=</HashCode>
      <FileName>ui\XHistoryCombo.h</FileName>
    </TypeIdentifier>
  </Class>
  <Struct Name="SI_GenericCase&lt;SI_CHAR&gt;" Collapsed="true">
    <Position X="10.75" Y="3.75" Width="1.5" />
    <TypeIdentifier>
      <HashCode>AAAAAAAAAAAAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAA=</HashCode>
      <FileName>d:\VSCode\testtest\third-party\simpleini\SimpleIni.h</FileName>
    </TypeIdentifier>
  </Struct>
  <Struct Name="SI_GenericNoCase&lt;SI_CHAR&gt;" Collapsed="true">
    <Position X="12.5" Y="3.75" Width="1.5" />
    <TypeIdentifier>
      <HashCode>AAAAAAAAAAAAAAAAEAAAAAAAAAAAAAAIAAAAAAAAAAA=</HashCode>
      <FileName>d:\VSCode\testtest\third-party\simpleini\SimpleIni.h</FileName>
    </TypeIdentifier>
  </Struct>
  <Struct Name="SI_NoCase&lt;SI_CHAR&gt;" Collapsed="true">
    <Position X="14.25" Y="3.75" Width="1.5" />
    <TypeIdentifier>
      <HashCode>AAAAAAAAAAAAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAA=</HashCode>
      <FileName>d:\VSCode\testtest\third-party\simpleini\SimpleIni.h</FileName>
    </TypeIdentifier>
  </Struct>
  <Struct Name="OPENFILENAMEEX_FOLDER" Collapsed="true">
    <Position X="9" Y="3.75" Width="1.5" />
    <TypeIdentifier>
      <HashCode>AAAAAAAAAAAAAAAABAAAAAAAAAAAAAAAIAAAAAAQAAA=</HashCode>
      <FileName>ui\XFolderDialog.h</FileName>
    </TypeIdentifier>
  </Struct>
  <Typedef Name="CSimpleIniA" Collapsed="true">
    <Position X="6.5" Y="2.5" Width="1.5" />
    <TypeIdentifier>
      <HashCode>AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA=</HashCode>
      <FileName>d:\VSCode\testtest\third-party\simpleini\SimpleIni.h</FileName>
    </TypeIdentifier>
  </Typedef>
  <Typedef Name="CSimpleIniCaseA" Collapsed="true">
    <Position X="0.5" Y="2.5" Width="1.5" />
    <TypeIdentifier>
      <HashCode>AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA=</HashCode>
      <FileName>d:\VSCode\testtest\third-party\simpleini\SimpleIni.h</FileName>
    </TypeIdentifier>
  </Typedef>
  <Typedef Name="CSimpleIniW" Collapsed="true">
    <Position X="3.5" Y="4.5" Width="1.5" />
    <TypeIdentifier>
      <HashCode>AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA=</HashCode>
      <FileName>d:\VSCode\testtest\third-party\simpleini\SimpleIni.h</FileName>
    </TypeIdentifier>
  </Typedef>
  <Typedef Name="CSimpleIniCaseW" Collapsed="true">
    <Position X="3.5" Y="0.5" Width="1.5" />
    <TypeIdentifier>
      <HashCode>AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA=</HashCode>
      <FileName>d:\VSCode\testtest\third-party\simpleini\SimpleIni.h</FileName>
    </TypeIdentifier>
  </Typedef>
  <Enum Name="SI_Error" Collapsed="true">
    <Position X="9" Y="5" Width="1.5" />
    <TypeIdentifier>
      <HashCode>CAAAAAAAAAAAAAAQAAAAAAhAACAAAAQAAAAAAAAAAAA=</HashCode>
      <FileName>d:\VSCode\testtest\third-party\simpleini\SimpleIni.h</FileName>
    </TypeIdentifier>
  </Enum>
  <Font Name="Microsoft YaHei UI" Size="9" />
</ClassDiagram>