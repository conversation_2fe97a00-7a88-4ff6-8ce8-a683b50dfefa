#include "stdafx.h"
#include "IniSetting.h"
#include "PublicFunction.h"
#include "../INC/DlpULog.h"
#include "CodeT.h"

extern LOG_HANDLE g_log_handle;

IniSetting IniSetting::instance;

IniSetting& IniSetting::getInstance()
{
	// TODO: 在此处插入 return 语句
	return instance;
}

bool IniSetting::initSetting()
{
	m_iCheckPointType = pCheck;
	m_iniPath = IniPath();
	ini.SetUnicode(true);
	ini.SetMultiKey(false);
	SI_Error ini_rc = ini.LoadFile(m_iniPath.c_str());
	if (ini_rc < 0)
	{
		dlplog_info(g_log_handle, "There is no ini configuration file in the running path, create and initialize it");

		USES_CONVERSION;//声明标识
		CString wcsAppDir;
		wcsAppDir = GetProPath();//获取运行程序路径
		//初始化显示策略
		CString strStrategyFilePath = wcsAppDir + L"\\Strategy.data";
		//初始化检测文件
		CString strFilePath4FCR = wcsAppDir + L"SampleFcr.txt";
		//初始化检测文件夹
		CString strFolderPath4Fcr = wcsAppDir;
		const char* strategyPath = T2A(strStrategyFilePath);
		const char* test_path = T2A(strFilePath4FCR);
		const char* folder_path = T2A(strFolderPath4Fcr);

		//Check
		//SETTING 配置页面 初始化
		ini.SetValue("LDFCR_MODULE", "m_Check_bfilefp", "0");//写入选择的路径
		ini.SetValue("LDFCR_MODULE", "m_Check_bsvm", "0");//写入选择的路径
		ini.SetValue("LDFCR_MODULE", "m_Check_bfiledb", "0");//写入选择的路径
		ini.SetValue("LDFCR_MODULE", "m_Check_bocr_type", "0");//写入选择的路径
		ini.SetValue("LDFCR_MODULE", "m_Check_bocr_embed", "0");//写入选择的路径
		ini.SetValue("LDFCR_MODULE", "m_Check_bocr", "0");//写入选择的路径
		ini.SetValue("FILTER_MODULE", "m_Check_enFM_OCR", "0x00");//写入OCR原生图片选取
		ini.SetValue("FILTER_MODULE", "m_Check_enFM_OCR_IN_CHILD", "0x00");//写入OCR嵌套图片选取
		ini.SetValue("FILTER_MODULE", "m_Check_OCR_TYPE_JSON", "{\"ocrModel\": {\"typeshow\": [\"none\"]}}");//写入OCR内容识别默认json 默认不开启

		ini.SetValue("DETECT_OPTYPE", "Check_optype", "1");//写入操作类型
		ini.SetValue("DETECT_OPTYPE", "Check_time_out", "0");//写入是否阻断选项
		ini.SetValue("DETECT_OPTYPE", "Check_outTimeJug", "0");//写入阻断时间
		ini.SetValue("DETECT_OPTYPE", "Check_circulate", "1");//写入检测次数
		ini.SetValue("DETECT_OPTYPE", "Check_contextCount", "0");//文本前后的文本字符长度
		ini.SetValue("DETECT_OPTYPE", "Check_bcheckALL", "0");//写入嵌套文件中的检测到敏感是否继续检测 
		ini.SetValue("DETECT_OPTYPE", "Check_btag", "0");//写入标签检测选项
		ini.SetValue("DETECT_OPTYPE", "Check_pwdError", "0");//写入密码错误返回选项
		ini.SetValue("DETECT_OPTYPE", "Check_password", "");//写入检测文件密码
		ini.SetValue("DETECT_OPTYPE", "Check_adv_key", "");//写入高级秘钥

		ini.SetValue("Thread", "Check_thread_count", "1"); // 保存初始多线程启用为1
		//外部页面做ini赋值

		ini.SetValue("PATH", "Check_strategy_common", strategyPath); // 保存常规策略文件路径
		ini.SetValue("PATH", "Check_strategy_drip", ""); // 保存零星策略文件路径
		ini.SetValue("PATH", "Check_test_path", test_path); // 保存测试文件路径
		ini.SetValue("PATH", "Check_folder_path", folder_path); //保存测试文件夹路径

		//Encrypt
		//SETTING 配置页面 初始化
		ini.SetValue("LDFCR_MODULE", "m_Encrypt_bfilefp", "0");//写入选择的路径
		ini.SetValue("LDFCR_MODULE", "m_Encrypt_bsvm", "0");//写入选择的路径
		ini.SetValue("LDFCR_MODULE", "m_Encrypt_bfiledb", "0");//写入选择的路径
		ini.SetValue("LDFCR_MODULE", "m_Encrypt_bocr_type", "0");//写入选择的路径
		ini.SetValue("LDFCR_MODULE", "m_Encrypt_bocr_embed", "0");//写入选择的路径
		ini.SetValue("LDFCR_MODULE", "m_Encrypt_bocr", "0");//写入选择的路径
		ini.SetValue("FILTER_MODULE", "m_Encrypt_enFM_OCR", "0x00");//写入OCR原生图片选取
		ini.SetValue("FILTER_MODULE", "m_Encrypt_enFM_OCR_IN_CHILD", "0x00");//写入OCR嵌套图片选取
		ini.SetValue("FILTER_MODULE", "m_Encrypt_OCR_TYPE_JSON", "{\"ocrModel\": {\"typeshow\": [\"none\"]}}");//写入OCR内容识别默认json 默认不开启

		ini.SetValue("DETECT_OPTYPE", "Encrypt_optype", "0");//写入操作类型
		ini.SetValue("DETECT_OPTYPE", "Encrypt_time_out", "0");//写入是否阻断选项
		ini.SetValue("DETECT_OPTYPE", "Encrypt_outTimeJug", "0");//写入阻断时间
		ini.SetValue("DETECT_OPTYPE", "Encrypt_circulate", "1");//写入检测次数
		ini.SetValue("DETECT_OPTYPE", "Encrypt_contextCount", "0");//文本前后的文本字符长度
		ini.SetValue("DETECT_OPTYPE", "Encrypt_bcheckALL", "0");//写入嵌套文件中的检测到敏感是否继续检测 
		ini.SetValue("DETECT_OPTYPE", "Encrypt_btag", "0");//写入标签检测选项
		ini.SetValue("DETECT_OPTYPE", "Encrypt_pwdError", "0");//写入密码错误返回选项
		ini.SetValue("DETECT_OPTYPE", "Encrypt_password", "");//写入检测文件密码
		ini.SetValue("DETECT_OPTYPE", "Encrypt_adv_key", "");//写入高级秘钥

		ini.SetValue("Thread", "Encrypt_thread_count", "1"); // 保存初始多线程启用为1
		//外部页面做ini赋值

		ini.SetValue("PATH", "Encrypt_strategy_common", strategyPath); // 保存常规策略文件路径
		ini.SetValue("PATH", "Encrypt_strategy_drip", ""); // 保存零星策略文件路径
		ini.SetValue("PATH", "Encrypt_test_path", test_path); // 保存测试文件路径
		ini.SetValue("PATH", "Encrypt_folder_path", folder_path); //保存测试文件夹路径

		//EncryptTag
		//SETTING 配置页面 初始化
		ini.SetValue("LDFCR_MODULE", "m_EncryptTag_bfilefp", "0");//写入选择的路径
		ini.SetValue("LDFCR_MODULE", "m_EncryptTag_bsvm", "0");//写入选择的路径
		ini.SetValue("LDFCR_MODULE", "m_EncryptTag_bfiledb", "0");//写入选择的路径
		ini.SetValue("LDFCR_MODULE", "m_EncryptTag_bocr_type", "0");//写入选择的路径
		ini.SetValue("LDFCR_MODULE", "m_EncryptTag_bocr_embed", "0");//写入选择的路径
		ini.SetValue("LDFCR_MODULE", "m_EncryptTag_bocr", "0");//写入选择的路径
		ini.SetValue("FILTER_MODULE", "m_EncryptTag_enFM_OCR", "0x00");//写入OCR原生图片选取
		ini.SetValue("FILTER_MODULE", "m_EncryptTag_enFM_OCR_IN_CHILD", "0x00");//写入OCR嵌套图片选取
		ini.SetValue("FILTER_MODULE", "m_EncryptTag_OCR_TYPE_JSON", "{\"ocrModel\": {\"typeshow\": [\"none\"]}}");//写入OCR内容识别默认json 默认不开启

		ini.SetValue("DETECT_OPTYPE", "EncryptTag_optype", "1");//写入操作类型
		ini.SetValue("DETECT_OPTYPE", "EncryptTag_time_out", "0");//写入是否阻断选项
		ini.SetValue("DETECT_OPTYPE", "EncryptTag_outTimeJug", "0");//写入阻断时间
		ini.SetValue("DETECT_OPTYPE", "EncryptTag_circulate", "1");//写入检测次数
		ini.SetValue("DETECT_OPTYPE", "EncryptTag_contextCount", "0");//文本前后的文本字符长度
		ini.SetValue("DETECT_OPTYPE", "EncryptTag_bcheckALL", "0");//写入嵌套文件中的检测到敏感是否继续检测 
		ini.SetValue("DETECT_OPTYPE", "EncryptTag_btag", "0");//写入标签检测选项
		ini.SetValue("DETECT_OPTYPE", "EncryptTag_pwdError", "0");//写入密码错误返回选项
		ini.SetValue("DETECT_OPTYPE", "EncryptTag_password", "");//写入检测文件密码
		ini.SetValue("DETECT_OPTYPE", "EncryptTag_adv_key", "");//写入高级秘钥

		ini.SetValue("Thread", "EncryptTag_thread_count", "1"); // 保存初始多线程启用为1
		//外部页面做ini赋值

		ini.SetValue("PATH", "EncryptTag_strategy_common", strategyPath); // 保存常规策略文件路径
		ini.SetValue("PATH", "EncryptTag_strategy_drip", ""); // 保存零星策略文件路径
		ini.SetValue("PATH", "EncryptTag_test_path", test_path); // 保存测试文件路径
		ini.SetValue("PATH", "EncryptTag_folder_path", folder_path); //保存测试文件夹路径

		//Global
		//SETTING 配置页面 初始化
		ini.SetValue("LDFCR_MODULE", "m_Global_bfilefp", "0");//写入选择的路径
		ini.SetValue("LDFCR_MODULE", "m_Global_bsvm", "0");//写入选择的路径
		ini.SetValue("LDFCR_MODULE", "m_Global_bfiledb", "0");//写入选择的路径
		ini.SetValue("LDFCR_MODULE", "m_Global_bocr_type", "0");//写入选择的路径
		ini.SetValue("LDFCR_MODULE", "m_Global_bocr_embed", "0");//写入选择的路径
		ini.SetValue("LDFCR_MODULE", "m_Global_bocr", "0");//写入选择的路径
		ini.SetValue("FILTER_MODULE", "m_Global_enFM_OCR", "0x00");//写入OCR原生图片选取
		ini.SetValue("FILTER_MODULE", "m_Global_enFM_OCR_IN_CHILD", "0x00");//写入OCR嵌套图片选取
		ini.SetValue("FILTER_MODULE", "m_Global_OCR_TYPE_JSON", "{\"ocrModel\": {\"typeshow\": [\"none\"]}}");//写入OCR内容识别默认json 默认不开启

		ini.SetValue("DETECT_OPTYPE", "Global_optype", "1");//写入操作类型
		ini.SetValue("DETECT_OPTYPE", "Global_time_out", "0");//写入是否阻断选项
		ini.SetValue("DETECT_OPTYPE", "Global_outTimeJug", "0");//写入阻断时间
		ini.SetValue("DETECT_OPTYPE", "Global_circulate", "1");//写入检测次数
		ini.SetValue("DETECT_OPTYPE", "Global_contextCount", "0");//文本前后的文本字符长度
		ini.SetValue("DETECT_OPTYPE", "Global_bcheckALL", "0");//写入嵌套文件中的检测到敏感是否继续检测 
		ini.SetValue("DETECT_OPTYPE", "Global_btag", "0");//写入标签检测选项
		ini.SetValue("DETECT_OPTYPE", "Global_pwdError", "0");//写入密码错误返回选项
		ini.SetValue("DETECT_OPTYPE", "Global_password", "");//写入检测文件密码
		ini.SetValue("DETECT_OPTYPE", "Global_adv_key", "");//写入高级秘钥

		ini.SetValue("Thread", "Global_thread_count", "1"); // 保存初始多线程启用为1
		//外部页面做ini赋值

		ini.SetValue("PATH", "Global_strategy_common", strategyPath); // 保存常规策略文件路径
		ini.SetValue("PATH", "Global_strategy_drip", ""); // 保存零星策略文件路径
		ini.SetValue("PATH", "Global_test_path", test_path); // 保存测试文件路径
		ini.SetValue("PATH", "Global_folder_path", folder_path); //保存测试文件夹路径

		// 本页面做成员变量赋值
		CString m_strShowLogPath = wcsAppDir + L"\\log\\ldfcr.log";
		const char* logPath = T2A(m_strShowLogPath);
		ini.SetValue("PATH", "log_path", logPath); //保存 日志

		ini.SaveFile(m_iniPath.c_str(), 0);//保存ini文件

	}
	this->m_bfilefp = ini.GetLongValue("LDFCR_MODULE", "m_Check_bfilefp", 0);
	this->m_bsvm = ini.GetLongValue("LDFCR_MODULE", "m_Check_bsvm", 0);
	this->m_bfiledb = ini.GetLongValue("LDFCR_MODULE", "m_Check_bfiledb", 0);
	this->m_bocr_type = ini.GetLongValue("LDFCR_MODULE", "m_Check_bocr_type", 0);
	this->m_bocr_embed = ini.GetLongValue("LDFCR_MODULE", "m_Check_bocr_embed", 0);
	this->m_bocr = ini.GetLongValue("LDFCR_MODULE", "m_Check_bocr", 0);
	this->m_enFM_OCR = ini.GetValue("FILTER_MODULE", "m_Check_enFM_OCR", NULL);
	this->m_enFM_OCR_IN_CHILD = ini.GetValue("FILTER_MODULE", "m_Check_enFM_OCR_IN_CHILD", NULL);
	this->m_OCR_JSON_TYPE = ini.GetValue("FILTER_MODULE", "m_Check_OCR_TYPE_JSON", NULL);

	this->optype = ini.GetLongValue("DETECT_OPTYPE", "Check_optype", 0);
	this->m_btime_out = ini.GetLongValue("DETECT_OPTYPE", "Check_time_out", 0);
	this->m_outTime = ini.GetLongValue("DETECT_OPTYPE", "Check_outTimeJug", 0);
	this->circulate = ini.GetLongValue("DETECT_OPTYPE", "Check_circulate", 0);
	this->contextCount = ini.GetLongValue("DETECT_OPTYPE", "Check_contextCount", 0);
	this->m_bcheckALL = ini.GetLongValue("DETECT_OPTYPE", "Check_bcheckALL", 0);
	this->m_btag = ini.GetLongValue("DETECT_OPTYPE", "Check_btag", 0);
	this->m_pwdError = ini.GetLongValue("DETECT_OPTYPE", "Check_pwdError", 0);
	this->password = ini.GetValue("DETECT_OPTYPE", "Check_password", NULL);
	this->adv_key = ini.GetValue("DETECT_OPTYPE", "Check_adv_key", NULL);

	this->thread_count = ini.GetLongValue("Thread", "Check_thread_count", 0);

	this->strategy_common = ini.GetValue("PATH", "Check_strategy_common", NULL);
	this->strategy_drip = ini.GetValue("PATH", "Check_strategy_drip", NULL);
	this->test_path = ini.GetValue("PATH", "Check_test_path", NULL);
	this->folder_path = ini.GetValue("PATH", "Check_folder_path", NULL);

	this->log_path = ini.GetValue("LOG", "log_path", NULL);
	return true;
}

bool IniSetting::switchSetting(const int type)
{
	m_iCheckPointType = type;
	ini.SetUnicode(true);
	ini.SetMultiKey(false);
	SI_Error ini_rc = ini.LoadFile(m_iniPath.c_str());
	if (m_iCheckPointType == pCheck)
	{
		this->m_bfilefp = ini.GetLongValue("LDFCR_MODULE", "m_Check_bfilefp", 0);
		this->m_bsvm = ini.GetLongValue("LDFCR_MODULE", "m_Check_bsvm", 0);
		this->m_bfiledb = ini.GetLongValue("LDFCR_MODULE", "m_Check_bfiledb", 0);
		this->m_bocr_type = ini.GetLongValue("LDFCR_MODULE", "m_Check_bocr_type", 0);
		this->m_bocr_embed = ini.GetLongValue("LDFCR_MODULE", "m_Check_bocr_embed", 0);
		this->m_bocr = ini.GetLongValue("LDFCR_MODULE", "m_Check_bocr", 0);
		this->m_enFM_OCR = ini.GetValue("FILTER_MODULE", "m_Check_enFM_OCR", NULL);
		this->m_enFM_OCR_IN_CHILD = ini.GetValue("FILTER_MODULE", "m_Check_enFM_OCR_IN_CHILD", NULL);
		this->m_OCR_JSON_TYPE = ini.GetValue("FILTER_MODULE", "m_Check_OCR_TYPE_JSON", NULL);

		this->optype = ini.GetLongValue("DETECT_OPTYPE", "Check_optype", 0);
		this->m_btime_out = ini.GetLongValue("DETECT_OPTYPE", "Check_time_out", 0);
		this->m_outTime = ini.GetLongValue("DETECT_OPTYPE", "Check_outTimeJug", 0);
		this->circulate = ini.GetLongValue("DETECT_OPTYPE", "Check_circulate", 0);
		this->contextCount = ini.GetLongValue("DETECT_OPTYPE", "Check_contextCount", 0);
		this->m_bcheckALL = ini.GetLongValue("DETECT_OPTYPE", "Check_bcheckALL", 0);
		this->m_btag = ini.GetLongValue("DETECT_OPTYPE", "Check_btag", 0);
		this->m_pwdError = ini.GetLongValue("DETECT_OPTYPE", "Check_pwdError", 0);
		this->password = ini.GetValue("DETECT_OPTYPE", "Check_password", NULL);
		this->adv_key = ini.GetValue("DETECT_OPTYPE", "Check_adv_key", NULL);

		this->thread_count = ini.GetLongValue("Thread", "Check_thread_count", 0);

		this->strategy_common = ini.GetValue("PATH", "Check_strategy_common", NULL);
		this->strategy_drip = ini.GetValue("PATH", "Check_strategy_drip", NULL);
		this->test_path = ini.GetValue("PATH", "Check_test_path", NULL);
		this->folder_path = ini.GetValue("PATH", "Check_folder_path", NULL);
	}
	else if (m_iCheckPointType == pEncrypt)
	{
		this->m_bfilefp = ini.GetLongValue("LDFCR_MODULE", "m_Encrypt_bfilefp", 0);
		this->m_bsvm = ini.GetLongValue("LDFCR_MODULE", "m_Encrypt_bsvm", 0);
		this->m_bfiledb = ini.GetLongValue("LDFCR_MODULE", "m_Encrypt_bfiledb", 0);
		this->m_bocr_type = ini.GetLongValue("LDFCR_MODULE", "m_Encrypt_bocr_type", 0);
		this->m_bocr_embed = ini.GetLongValue("LDFCR_MODULE", "m_Encrypt_bocr_embed", 0);
		this->m_bocr = ini.GetLongValue("LDFCR_MODULE", "m_Encrypt_bocr", 0);
		this->m_enFM_OCR = ini.GetValue("FILTER_MODULE", "m_Encrypt_enFM_OCR", NULL);
		this->m_enFM_OCR_IN_CHILD = ini.GetValue("FILTER_MODULE", "m_Encrypt_enFM_OCR_IN_CHILD", NULL);
		this->m_OCR_JSON_TYPE = ini.GetValue("FILTER_MODULE", "m_Encrypt_OCR_TYPE_JSON", NULL);

		this->optype = ini.GetLongValue("DETECT_OPTYPE", "Encrypt_optype", 0);
		this->m_btime_out = ini.GetLongValue("DETECT_OPTYPE", "Encrypt_time_out", 0);
		this->m_outTime = ini.GetLongValue("DETECT_OPTYPE", "Encrypt_outTimeJug", 0);
		this->circulate = ini.GetLongValue("DETECT_OPTYPE", "Encrypt_circulate", 0);
		this->contextCount = ini.GetLongValue("DETECT_OPTYPE", "Encrypt_contextCount", 0);
		this->m_bcheckALL = ini.GetLongValue("DETECT_OPTYPE", "Encrypt_bcheckALL", 0);
		this->m_btag = ini.GetLongValue("DETECT_OPTYPE", "Encrypt_btag", 0);
		this->m_pwdError = ini.GetLongValue("DETECT_OPTYPE", "Encrypt_pwdError", 0);
		this->password = ini.GetValue("DETECT_OPTYPE", "Encrypt_password", NULL);
		this->adv_key = ini.GetValue("DETECT_OPTYPE", "Encrypt_adv_key", NULL);

		this->thread_count = ini.GetLongValue("Thread", "Encrypt_thread_count", 0);

		this->strategy_common = ini.GetValue("PATH", "Encrypt_strategy_common", NULL);
		this->strategy_drip = ini.GetValue("PATH", "Encrypt_strategy_drip", NULL);
		this->test_path = ini.GetValue("PATH", "Encrypt_test_path", NULL);
		this->folder_path = ini.GetValue("PATH", "Encrypt_folder_path", NULL);
	}
	else if (m_iCheckPointType == pEncryptTag)
	{
		this->m_bfilefp = ini.GetLongValue("LDFCR_MODULE", "m_EncryptTag_bfilefp", 0);
		this->m_bsvm = ini.GetLongValue("LDFCR_MODULE", "m_EncryptTag_bsvm", 0);
		this->m_bfiledb = ini.GetLongValue("LDFCR_MODULE", "m_EncryptTag_bfiledb", 0);
		this->m_bocr_type = ini.GetLongValue("LDFCR_MODULE", "m_EncryptTag_bocr_type", 0);
		this->m_bocr_embed = ini.GetLongValue("LDFCR_MODULE", "m_EncryptTag_bocr_embed", 0);
		this->m_bocr = ini.GetLongValue("LDFCR_MODULE", "m_EncryptTag_bocr", 0);
		this->m_enFM_OCR = ini.GetValue("FILTER_MODULE", "m_EncryptTag_enFM_OCR", NULL);
		this->m_enFM_OCR_IN_CHILD = ini.GetValue("FILTER_MODULE", "m_EncryptTag_enFM_OCR_IN_CHILD", NULL);
		this->m_OCR_JSON_TYPE = ini.GetValue("FILTER_MODULE", "m_EncryptTag_OCR_TYPE_JSON", NULL);

		this->optype = ini.GetLongValue("DETECT_OPTYPE", "EncryptTag_optype", 0);
		this->m_btime_out = ini.GetLongValue("DETECT_OPTYPE", "EncryptTag_time_out", 0);
		this->m_outTime = ini.GetLongValue("DETECT_OPTYPE", "EncryptTag_outTimeJug", 0);
		this->circulate = ini.GetLongValue("DETECT_OPTYPE", "EncryptTag_circulate", 0);
		this->contextCount = ini.GetLongValue("DETECT_OPTYPE", "EncryptTag_contextCount", 0);
		this->m_bcheckALL = ini.GetLongValue("DETECT_OPTYPE", "EncryptTag_bcheckALL", 0);
		this->m_btag = ini.GetLongValue("DETECT_OPTYPE", "EncryptTag_btag", 0);
		this->m_pwdError = ini.GetLongValue("DETECT_OPTYPE", "EncryptTag_pwdError", 0);
		this->password = ini.GetValue("DETECT_OPTYPE", "EncryptTag_password", NULL);
		this->adv_key = ini.GetValue("DETECT_OPTYPE", "EncryptTag_adv_key", NULL);

		this->thread_count = ini.GetLongValue("Thread", "EncryptTag_thread_count", 0);

		this->strategy_common = ini.GetValue("PATH", "EncryptTag_strategy_common", NULL);
		this->strategy_drip = ini.GetValue("PATH", "EncryptTag_strategy_drip", NULL);
		this->test_path = ini.GetValue("PATH", "EncryptTag_test_path", NULL);
		this->folder_path = ini.GetValue("PATH", "EncryptTag_folder_path", NULL);
	}
	else if (m_iCheckPointType == Global)
	{
		this->m_bfilefp = ini.GetLongValue("LDFCR_MODULE", "m_Global_bfilefp", 0);
		this->m_bsvm = ini.GetLongValue("LDFCR_MODULE", "m_Global_bsvm", 0);
		this->m_bfiledb = ini.GetLongValue("LDFCR_MODULE", "m_Global_bfiledb", 0);
		this->m_bocr_type = ini.GetLongValue("LDFCR_MODULE", "m_Global_bocr_type", 0);
		this->m_bocr_embed = ini.GetLongValue("LDFCR_MODULE", "m_Global_bocr_embed", 0);
		this->m_bocr = ini.GetLongValue("LDFCR_MODULE", "m_Global_bocr", 0);
		this->m_enFM_OCR = ini.GetValue("FILTER_MODULE", "m_Global_enFM_OCR", NULL);
		this->m_enFM_OCR_IN_CHILD = ini.GetValue("FILTER_MODULE", "m_Global_enFM_OCR_IN_CHILD", NULL);
		this->m_OCR_JSON_TYPE = ini.GetValue("FILTER_MODULE", "m_Global_OCR_TYPE_JSON", NULL);

		this->optype = ini.GetLongValue("DETECT_OPTYPE", "Global_optype", 0);
		this->m_btime_out = ini.GetLongValue("DETECT_OPTYPE", "Global_time_out", 0);
		this->m_outTime = ini.GetLongValue("DETECT_OPTYPE", "Global_outTimeJug", 0);
		this->circulate = ini.GetLongValue("DETECT_OPTYPE", "Global_circulate", 0);
		this->contextCount = ini.GetLongValue("DETECT_OPTYPE", "Global_contextCount", 0);
		this->m_bcheckALL = ini.GetLongValue("DETECT_OPTYPE", "Global_bcheckALL", 0);
		this->m_btag = ini.GetLongValue("DETECT_OPTYPE", "Global_btag", 0);
		this->m_pwdError = ini.GetLongValue("DETECT_OPTYPE", "Global_pwdError", 0);
		this->password = ini.GetValue("DETECT_OPTYPE", "Global_password", NULL);
		this->adv_key = ini.GetValue("DETECT_OPTYPE", "Global_adv_key", NULL);

		this->thread_count = ini.GetLongValue("Thread", "Global_thread_count", 0);

		this->strategy_common = ini.GetValue("PATH", "Global_strategy_common", NULL);
		this->strategy_drip = ini.GetValue("PATH", "Global_strategy_drip", NULL);
		this->test_path = ini.GetValue("PATH", "Global_test_path", NULL);
		this->folder_path = ini.GetValue("PATH", "Global_folder_path", NULL);
	}
	return ini_rc;
}

int IniSetting::recordStr2INI(const char* v_recodeType, const char* v_classification, const char* v_strValue)
{
	ini.LoadFile(m_iniPath.c_str());
	ini.SetValue(v_recodeType, v_classification, v_strValue);//写入选择的路径
	SI_Error rc = ini.SaveFile(m_iniPath.c_str(), 0);//保存ini文件
	return rc;
}

int IniSetting::recordCStr2INI(const char* v_recodeType, const char* v_classification, CString v_runningPath)
{
	//ini文件读取、写入、保存
	string strPath = wstringToString(v_runningPath.GetString());
	ini.LoadFile(m_iniPath.c_str());
	ini.SetValue(v_recodeType, v_classification, strPath.c_str());//写入选择的路径
	SI_Error rc = ini.SaveFile(m_iniPath.c_str(), 0);//保存ini文件
	return rc;
}

int IniSetting::recordDigit2INI(const char* v_recodeType, const char* v_classification, int v_digit)
{
	//ini文件读取、写入、保存

	char m_itc[10];
	sprintf(m_itc, "%d", v_digit); //保存转码类型
	ini.LoadFile(m_iniPath.c_str());
	ini.SetValue(v_recodeType, v_classification, m_itc);//写入选择的路径
	SI_Error rc = ini.SaveFile(m_iniPath.c_str(), 0);//保存ini文件
	return rc;
}

int IniSetting::recordPATH2INI(const char* v_recodeType,const CString& v_Path)
{
	std::string strCheckPath;
	string strPath = wstringToString(v_Path.GetString());
	ini.LoadFile(m_iniPath.c_str());
	ini.SetValue("PATH", v_recodeType, strPath.c_str());//
	SI_Error rc = ini.SaveFile(m_iniPath.c_str(), 0);//保存ini文件
	return rc;
}

int IniSetting::returnCheckPoint()
{
	return this->m_iCheckPointType;
}

bool IniSetting::GetFileFp()
{
	return this->m_bfilefp;
}

bool IniSetting::GetSVM()
{
	return this->m_bsvm;
}

bool IniSetting::GetFileDB()
{
	return this->m_bfiledb;
}

bool IniSetting::GetOCRType()
{
	return this->m_bocr_type;
}

bool IniSetting::GetOCREmbed()
{
	return this->m_bocr_embed;
}

bool IniSetting::GetOCR()
{
	return this->m_bocr;
}

const char* IniSetting::GetOCRenFM()
{
	return this->m_enFM_OCR;
}

const char* IniSetting::GetOCRenFMInChild()
{
	return this->m_enFM_OCR_IN_CHILD;
}

int IniSetting::GetOptype()
{
	return this->optype;
}

bool IniSetting::GetOutTime()
{
	if (this->m_btime_out == 1)
	{
		return true;
	}
	return false;
}

unsigned long long IniSetting::GetOutTimes()
{
	return this->m_outTime;
}

int IniSetting::GetCirCulate()
{
	return this->circulate;
}

int IniSetting::GetContextLength()
{
	return this->contextCount;
}

bool IniSetting::GetCheckAll()
{
	return this->m_bcheckALL;
}

bool IniSetting::GetCheckTag()
{
	return this->m_btag;
}

bool IniSetting::GetPwdStop()
{
	if (m_pwdError == 1)
	{
		return true;
	}
	return false;
}

const char* IniSetting::GetPassWord()
{
	return this->password;
}

int IniSetting::GetThreadCount()
{
	return this->thread_count;
}

const char* IniSetting::GetComStrategyPath()
{
	return this->strategy_common;
}

const char* IniSetting::GetDripStrategyPath()
{
	return this->strategy_drip;
}

const char* IniSetting::GetTestPath()
{
	return this->test_path;
}

const char* IniSetting::GetFolderPath()
{
	return this->folder_path;
}

const char* IniSetting::GetOCRJSONtype()
{
	return this->m_OCR_JSON_TYPE;
}

const char* IniSetting::GetADVKey()
{
	return this->adv_key;
}
