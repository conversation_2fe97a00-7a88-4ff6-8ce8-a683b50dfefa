#include "test_class.h"

int multithread_test(const char *strategy_file, const char *test_file, int num_of_instance, int iterations);

int main(int argc, char **argv)
{
    int threads  = 4;
    int iterations = 10000;
    const char *file = "test/text1.txt";
    if (argc <= 1)
    {
        std::cout << argv[0] << " threads iterations" << std::endl;
        return 0;
    }
    if (argc > 1)
        threads = atoi(argv[1]);
    if (argc > 2)
        iterations = atoi(argv[2]);
    if (argc > 3)
        file = strdup(argv[3]);
    ldfcr_InitStartup();
    //multithread_test("test/data/strategy.data", file, threads, iterations);
    multithread_test("test/tel/s_multithreads.data", file, threads, iterations);
    multithread_test("test/multithread_test/singlekw.data", file, threads, iterations);
    multithread_test("test/multithread_test/pairkw.data", file, threads, iterations);
    multithread_test("test/multithread_test/groupkw.data", file, threads, iterations);
    //multithread_test("test/multithread_test/pairkw.data", "test/multithread_test/data/1", threads, iterations);

    //multithread_test("test/data/strategy.data", "test/text1.txt", 1, 10000);

    ldfcr_StopRelease();
}
