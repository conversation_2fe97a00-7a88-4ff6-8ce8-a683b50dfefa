#pragma once
#include "SimpleIni.h"
#include <string>
#include "afxwin.h"
#include "DragEdit.h"


// Setting_Dialog 对话框

class Setting_Dialog : public CDialog
{
	DECLARE_DYNAMIC(Setting_Dialog)

public:
	Setting_Dialog(CWnd* pParent = NULL);   // 标准构造函数
	virtual ~Setting_Dialog();

// 对话框数据
#ifdef AFX_DESIGN_TIME
	enum { IDD = IDD_SETTING };
#endif

protected:
	virtual void DoDataExchange(CDataExchange* pDX);    // DDX/DDV 支持

	DECLARE_MESSAGE_MAP()
public:
	virtual BOOL OnInitDialog();

private:
	enum ldfcrCheckType
	{
		pCheck = 0,
		pEncrypt,
		pEncryptTag,
		Global
	};
public:
	CSimpleIniA ini;
	afx_msg void OnBnClickedFilefp();
	afx_msg void OnBnClickedSvm();
	afx_msg void OnBnClickedFiledb();
	afx_msg void OnBnClickedOcrEmbed();
	afx_msg void OnBnClickedOcr();
	CEdit eidttim;
	int m_outTimeJug;
	CEdit m_edit_password;
	CString m_password;
	CEdit m_opType;
	CString m_strOptype;
	int iOpType;
	int m_iCount;
	CEdit m_editCount;
	afx_msg void OnBnClickedCheckPwd();
	afx_msg void OnBnClickedUpdate();
	afx_msg void OnBnClickedBtnMiyao();
	CString m_csNormalKey;
	CEdit m_editKey;

	//输出框
	CEdit m_edit;
	afx_msg bool outPutFuc(const char * utf);
	int m_iIndex;  //计数君
	afx_msg void checkPut(int v_index, const char * c);  // 模块统一选择封装
	
	//操作类型
	CComboBox m_OpType;
	int m_intOpType;
	CEdit m_json_type;
	afx_msg void OnBnClickedButton1();
	CString m_cs_json_type;
	afx_msg void OnBnClickedOcrType();
	afx_msg void OnBnClickedTagCheck();
	afx_msg void OnBnClickedCheckAll();
	CEdit m_edit_senContext_count;
	int m_senContext_count;
	afx_msg void OnBnClickedOcrY();
	afx_msg void OnBnClickedOcrQ();
	afx_msg void OnBnClickedTimeOut();
};
