{"respondRule": [{"sendMail": false, "enableCancel": 0, "filters": [{"type": "deviceType", "relation": "either", "object": ["terminal-agent"]}, {"type": "lossType", "relation": "either", "object": ["8", "9", "26", "25", "2", "3", "4", "14", "15", "16", "11", "1", "18", "5", "17", "7", "23", "24", "13", "0", "20", "27", "29"]}], "alarmInfo": {"alarmLimit": 3, "msgFormType": 0, "deleted": 0, "configMethod": 1, "name": "规则响应", "msgFormPosition": 0, "id": 1, "msgFormClose": 30}, "outSendApply": 0, "stopOutgoing": 0, "takeScreenshot": false, "stopOutgoingEx": 0, "createTime": 1689573295000, "name": "规则响应", "id": 5, "warnContent": ""}], "checkRule": [{"createTime": 1689574710000, "fileSuffix": "asm,c,cpp,cxx,cc,h,hpp,hxx,cs,dpr,todo,f,for,f90,go,groovy,html,htm,java,js,m,fig,mat,plx,pl,php,py,py3,pyc,pyo,R,rb,sql,swift,vb,vbp", "SCRModel": -1, "ruleType": "12", "name": "源代码后缀识别", "sensitiveCodeType": "Assembly,C,C++,C-sharp,Delphi,Fortran,Go,Groovy,HTML,Java,JavaScript,MATLAB,Perl,PHP,Python,R,Ruby,SQL,Swift,VB", "id": 6, "type": 12}], "strategy": [{"severity": "1", "respondRule": "5", "createTime": 1689574880407, "checkRule": "6", "name": "源代码识别策略", "dripScan": false, "id": 2, "classification": [{"checkExpr": "6", "name": "源代码识别规则集", "exceptRule": "", "id": 2}]}], "businessType": {"opTypes": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 20], "type": 99}}