
// ldfcrTesterDlg.h : 头文件
//

#pragma once

#include "ldfcr.h"
#include <thread>
#include <vector>
#include <list>
#include "CodeT.h"
#include "afxcmn.h"
#include "afxwin.h"
#include <Windows.h>
#include <io.h>
#include <tchar.h>
#include <map>
#include "DragEdit.h"
#include <mutex>
//#include "ViewDlg.h"
// CldfcrTesterDlg 对话框

LONG WINAPI MyUnhandledExceptionFilter(struct _EXCEPTION_POINTERS *ExceptionInfo);
class CldfcrTesterDlg : public CDialog
{
// 构造
public:
	CldfcrTesterDlg(CWnd* pParent = NULL);	// 标准构造函数

public:
    BOOL dlp_checkSingleFile(const wchar_t* file_path);
	BOOL dlp_checkSingleText(const wchar_t* file_path);//1008
	bool CldfcrTesterDlg::ReadTextContent(const char* file_path, std::string& text);//读取文本内容

private:
    ILDFcr*   m_pILDFcr;
	ILDFcr*   m_pILDTcr;
    BOOL      m_bDoingDirFcr;
	BOOL	  m_bDoingDirTcr;

public:
// 对话框数据
	enum { IDD = IDD_LDFCRTESTER_DIALOG };

	protected:
	virtual void DoDataExchange(CDataExchange* pDX);	// DDX/DDV 支持


// 实现
protected:
	HICON m_hIcon;

	// 生成的消息映射函数
	virtual BOOL OnInitDialog();
	afx_msg void OnSysCommand(UINT nID, LPARAM lParam);
	afx_msg void OnPaint();
	afx_msg HCURSOR OnQueryDragIcon();
	DECLARE_MESSAGE_MAP()
public:
    CString m_strStrategyFilePath; //绑定策略地址控件
    CString m_strStrategy;
	string m_strStrategyMerge;  //2023 8 17 弃用W接口 将宽字节做个转码
    afx_msg void OnBnClickedBtnViewStrategy();
    virtual BOOL DestroyWindow();
    afx_msg void OnBnClickedBtnSelectFile();
    afx_msg void OnBnClickedBtnExecFcrSinglefile();
	afx_msg void OnBnClickedBtnExecTcrSingletext();
    CString m_strFilePath4FCR;
	string m_strFilePath4FCRMerge;  //2023 8 17 弃用W接口
	int m_optype;
    int m_iFcrCount;
	int m_iTcrCount;
    CString m_strSingleFcrTime;
    afx_msg void OnBnClickedBtnSelectFolder();
    CString m_strFolderPath4Fcr;
	
    afx_msg void OnBnClickedBtnExecFcrFolder();
    int m_iFileCount;
    int m_iSensFileCount;
    afx_msg void OnBnClickedTcr();
	afx_msg void OnBnClickedBtnUpdateStrategy();

	afx_msg void OnBnClickedBtnExecTcrFolder();//检测文本


	//响应规则
	CString m_strTimeOutRespRulePath;


	void doMatchTask();
	std::vector<std::shared_ptr<std::thread>> m_v_detector_thd;
	std::list<std::wstring>  filename_list;
	std::wstring fld_path;
	int  file_count_scaned = 0;
	int  file_count_sens = 0;
	CString GetProPath();
	int CountLogLine();

	
private:
	std::shared_ptr<std::thread> m_count_file_thd_sptr;
	
public:
	void initLog();
	
	afx_msg void OnBnClickedBtnTacticsFile2();
	afx_msg void OnLvnItemchangedList2(NMHDR *pNMHDR, LRESULT *pResult);
	
	afx_msg void OnBnClickedLogFlush();
	afx_msg void OnBnClickedChooseLog();
	afx_msg void OnBnClickedLogFlush2();
	afx_msg void OnBnClickedChooseLog2();

	CString m_strShowLogPath;//1019		
	CString m_strShowLogPath2;

	// Edit Input输入检测时间
	CEdit editinput;
	afx_msg void OnEnChangeInputIntercept();
	int m_outTimeJug;
	afx_msg void OnEnChangeEditFcrFilepath();
	int m_intOptype;
	//m_intOptype可控范围
	afx_msg BOOL OnNewDocument();
	afx_msg string GetSoftVersion(const char* exepath);//获取文件版本号
	afx_msg CString returnProName();
	afx_msg string WstringToString(const wstring v_wstr);
	int m_ldfcr_moudle;
	int m_bfilefp;
	int m_bsvm;
	int m_bfiledb;
	int m_bocr_embed;
	int m_bocr;
	int m_filext;

	afx_msg void OnBnClickedFILEFP();
	afx_msg void OnBnClickedSVM();
	afx_msg void OnBnClickedFILEDB();
	afx_msg void OnBnClickedOCR_EMBED();
	afx_msg void OnBnClickedOCR();
	afx_msg void OnBnClickedFILEXT();

	afx_msg string toBase64(const char *src_str);
	CEdit m_edit_password;
	wstring m_current_dir;
	CListCtrl m_list_ctrl_pwd_dict;
	afx_msg void OnBnClickedCheck1();
	CString m_password;
	BOOL m_pwd_error;
	int m_time_input;
	CString m_csNormalKey;
	afx_msg void OnBnClickedSetAdvKeyWord();
	afx_msg char * CStringtochar(CString str);
	afx_msg void OnBnClickedGetDefaultList();
	afx_msg void OnBnClickedSetMoudle();

	int m_InitChosen;  //初始化时获取的模式值 0代表对象模式 1代表全局模式

	enum {
		PointMoudle = 0,
		GlobalMoudle,
	};

	//下拉框
	CComboBox m_ComboSelectIdName;
	//创建map容器
	map<int, string> idNameMap;

	afx_msg void OnEnChangeStrategyFilepath();//默认路径编辑框
	afx_msg void OnCbnSelchangeComboSelectIdName();//获取策略ID和NAME接口

	//提取策略ID和NAME
	void CldfcrTesterDlg::ExtractIdWithName(const CString& filePath, map<int, string>& idNameMap);
	BOOL CldfcrTesterDlg::FindRespRuleId(const string& strLogContent, int ruleId);//查找响应规则id
	
	afx_msg void OnBnClickedBtnSelectTimeoutJson();
	afx_msg void OnBnClickedBtnSelectPasswordJson();
	afx_msg void OnEnChangeEditTimeoutRulepath();
	afx_msg void OnEnChangeEditPasswordRulepath();
	afx_msg void OnEnChangeEditPasswordRulename();
	afx_msg void OnEnChangeEditTimeoutRulesName();

	afx_msg void OnBnClickedButTimeoutTriggerRule();//自定义超时响应规则
	afx_msg void OnBnClickedButPasswordTriggerRule();//自定义带密码响应规则

	CString m_strTimeOutRulesName;//超时规则名
	CString m_strPassWordRuleName;//带密码规则名

	afx_msg void OnEnChangeEdit9();
	afx_msg void OnBnClickedButGetStopType();

	wstring strategy_wUtf8str;//20230807
	
	CDragEdit m_CStrategyEdit;
	CDragEdit m_CFcrEdit;
	CDragEdit m_CPassWordRuleEdit;
	CDragEdit m_CTimeOutRuleEdit;

	CString m_strPassWordRespRulePath;
	CEdit m_CFolderPath4FcrEdit;
	CEdit m_CShowLogPathEdit;
	CEdit m_CShowLogPathEdit2;


	std::list<std::wstring>  m_filename_list; //改为全局
	int m_thread_count;					// 线程数
	std::mutex m_mutex;
	void ldfcrTesterThreadFCR(int thread_index);  //2023 11 2 文件多线程
	void ldfcrTesterThreadTCR(int thread_index);  //2023 11 7 文本多线程

	int m_file_count_scaned;//扫描个数
	int m_file_count_sens;//敏感个数
	CString m_tim_count;
	CString m_thread_num;

};
