#include <vector>
#include <string>
#ifdef __GNUC__
#include <dirent.h>
#endif
//#ifdef WIN32
//#include <strsafe.h>
////#include <vld.h>
//#endif

#include <future>
#include "test_class.h"
#include "rapidjson/rapidjson.h"   //引入json串解析的能力
#include "rapidjson/document.h"
#include "trcrt.h"
#include "DlpULog.h"

using namespace std;

extern int g_log_handle;
//#define _CRT_SECURE_NO_WARNINGS

// 是否打开多线程测试用例
#define MULTITHREAD 1

#if MULTITHREAD == 1

// valgrind 非常耗性能
#ifndef VALGRIND_CHECK
const int loop = 10000;
#else
const int loop = 100;
#endif
#endif

int iSkipTestNum = 0;//跳过的测试用例数

//==============JsonValueAdapter摘除，函数写到这 2023 8 16==================
/*
* type mapping
* 		u	uint32
* 		U 	uint64
* 		i	int
* 		I	int64
* 		b	bool
* 		s	string
*/
bool JsonValueAdapterBool(const char *key, const char *poss_types, rapidjson::Json_Value &_Value, bool &ok, int log)
{
	const char *p = poss_types;
	ok = true;
	while (*p != '\0')
	{
		switch (*p)
		{
		case 'u':
		{
			if (_Value[key].IsUint())
			{
				unsigned int val = _Value[key].GetUint();
				if (val == 1) {
					return true;
				}
				else if (val == 0) {
					return false;
				}
				else {
					ok = false;
					if (log > 0)
						dlplog_error(log, "%s has unknown integer value: \"%u\"", key, val);
					return false;
				}
			}
			break;
		}
		case 'U':
		{
			if (_Value[key].IsUint64())
			{
				unsigned long int val = _Value[key].GetUint64();
				if (val == 1) {
					return true;
				}
				else if (val == 0) {
					return false;
				}
				else {
					ok = false;
					if (log > 0)
						dlplog_error(log, "%s has unknown integer value: \"%lu\"", key, val);
					return false;
				}
			}
			break;
		}
		case 'i':
		{
			if (_Value[key].IsInt())
			{
				int val = _Value[key].GetInt();
				if (val == 1) {
					return true;
				}
				else if (val == 0) {
					return false;
				}
				else {
					ok = false;
					if (log > 0)
						dlplog_error(log, "%s has unknown integer value: \"%d\"", key, val);
					return false;
				}
			}
			break;
		}
		case 'I':
		{
			if (_Value[key].IsInt64())
			{
				int64_t val = _Value[key].GetInt64();
				if (val == 1) {
					return true;
				}
				else if (val == 0) {
					return false;
				}
				else {
					ok = false;
					if (log > 0)
						dlplog_error(log, "%s has unknown integer value: \"%ld\"", key, val);
					return false;
				}
			}
			break;
		}
		case 'b':
		{
			if (_Value[key].IsBool())
			{
				return _Value[key].GetBool();
			}
			break;
		}
		case 's':
		{
			if (_Value[key].IsString())
			{
				const char *val = _Value[key].GetString();
				if (0 == strcmp(val, ("true")))
				{
					return true;
				}
				else if (0 == strcmp(val, ("false")))
				{
					return false;
				}
				else
				{
					ok = false;
					if (log > 0)
						dlplog_error(log, "%s has unknown value: \"%s\"", key, val);
					return false;
				}
			}
			break;
		}
		default:
			break;
		}
		++p;
	}
	ok = false;
	if (log > 0)
		dlplog_error(log, "%s has unknown types", key);
	return false;
}

/*
* type mapping
* 		U 	uint64
* 		s 	string
*/
unsigned long int JsonValueAdapterInt64(const char *key, const char *poss_types, rapidjson::Json_Value &_Value, bool &ok, int log)
{
	const char *p = poss_types;
	ok = true;
	while (*p != '\0')
	{
		switch (*p)
		{
		case 'U':
		{
			if (_Value[key].IsUint64())
			{
				return _Value[key].GetUint64();
			}
			break;
		}
		case 's':
		{
			if (_Value[key].IsString())
			{
				return (unsigned long int)strtoll(_Value[key].GetString(),NULL,10);
			}
			break;
		}
		default:
			break;
		}
		++p;
	}
	ok = false;
	if (log > 0)
		dlplog_error(log, "%s has unknown types", key);
	return 0;
}

/*
* type mapping
* 		U 	uint64
* 		s 	string
*/
std::string JsonValueAdapterString(const char *key, const char *poss_types, rapidjson::Json_Value &_Value, bool &ok, int log)
{
	const char *p = poss_types;
	ok = true;
	while (*p != '\0')
	{
		switch (*p)
		{
		case 'U':
		{
			if (_Value[key].IsUint64())
			{
				uint64_t severity = _Value[key].GetUint64();
				char _SeverityLevel[8];
				sprintf(_SeverityLevel, "%lld", severity);
				return _SeverityLevel;
			}
			break;
		}
		case 's':
		{
			if (_Value[key].IsString())
			{
				return _Value[key].GetString();
			}
			break;
		}
		default:
			break;
		}
		++p;
	}
	ok = false;
	if (log > 0)
		dlplog_error(log, "%s has unknown types", key);
	return "";
}

/*******************************************************************************************/
//									检测结果分析功能函数									//
/*******************************************************************************************/

// 常规检测结果为空
static bool IsResultEmpty(IFCRResult *result) {
	if (!result)
		return true;
	result->moveFirstPart();
	return result->moveNextPart() == NULL;
}

// 零星检测结果为空
static bool IsResultEmpty(IFCRDripResult *result) {
	if (!result)
		return true;
	result->moveFirstIncident();
	return result->moveNextIncident() == NULL;
}

// 检查零星检测策略/规则名是否为空
static bool CheckName(IFCRDripResult *result) 
{
	if (!result)
		return false;
	result->moveFirstIncident();
	ICRIncident *incident = NULL;
	while ((incident = result->moveNextIncident()) != NULL)
	{
		if (strlen(incident->getStrategyName()) <= 0)
		{
			return false;
		}
		incident->moveFirstMatch();
		ICRMatch *match = NULL;
		while ((match = incident->moveNextMatch()) != NULL)
		{
			if (strlen(match->getRuleName()) <= 0)
			{
				return false;
			}
		}
	}
	return true;
}

// 检查常规检测策略/规则名是否为空
static bool CheckName(IFCRResult *result)
{
	if (!result)
		return false;
	result->moveFirstPart();
	ICRPart *part;
	while ((part = result->moveNextPart()) != NULL)
	{
		const char *strategyStr = part->getStrategyMatched();
		rapidjson::Document doc;
		if (doc.Parse<0>(strategyStr).HasParseError())
		{// json串错误或者为空
			return false;
		}

		if (!(doc.HasMember("strategy") || doc["strategy"].IsArray()))
		{
			return false;
		}

		// 解析策略JSON串
		rapidjson::Value& strategyArry = doc["strategy"];
		for (unsigned iStrategy = 0; iStrategy < strategyArry.Size(); iStrategy++)
		{
			rapidjson::Value& strategy = strategyArry[iStrategy];
			if (!strategy.HasMember("name") || !strategy["name"].IsString())
				return false;

			rapidjson::Value& classifyArry = strategy["classification"];
			for (unsigned iClassify = 0; iClassify < classifyArry.Size(); iClassify++)
			{
				rapidjson::Value& classify = classifyArry[iClassify];
				rapidjson::Value& ruleArry = classify["rules"];
				for (unsigned iRule = 0; iRule < ruleArry.Size(); iRule++)
				{
					rapidjson::Value& rule = ruleArry[iRule];
					if (!rule.HasMember("name") || !rule["name"].IsString())
					{
						return false;
					}
				}
			}
		}
	}
	return true;
}

// 零星检测结果数
static int NumResult(IFCRDripResult *result) {
	int num = 0;
	if (!result)
		return 0;
	result->moveFirstIncident();
	while (result->moveNextIncident() != NULL)
		++num;
	return num;
}

// 常规检测结果数
static int NumResult(IFCRResult *result) {
	int num = 0;
	if (!result)
		return 0;
	result->moveFirstPart();
	while (result->moveNextPart() != NULL)
		++num;
	return num;
}

// 检查常规结果是否含有空值
static bool ResultHasContent(IFCRResult *result)
{
	if (!result)
		return false;
	result->moveFirstPart();
	ICRPart* part = NULL;
	while ((part = result->moveNextPart()) != NULL)
	{
		if (strlen(part->getStrategyMatched()) == 0)
			return false;
		if (strlen(part->getTextTrimed()) == 0)
			return false;
	}
	return true;
}

// 检查零星结果是否含有空值
static bool ResultHasContent(IFCRDripResult *result)
{
	if (!result)
		return false;
	result->moveFirstIncident();
	ICRIncident* incident = NULL;
	while ((incident = result->moveNextIncident()) != NULL)
	{
		if (strlen(incident->getStrategyMatched()) == 0)
			return false;
		incident->moveFirstMatch();
		ICRMatch* match = NULL;
		while ((match = incident->moveNextMatch()) != NULL)
		{
			if (strlen(match->getTextTrimed()) == 0)
				return false;
		}
	}
	return true;
}

// 检查响应规则里是否有某个Key，是否和预期值相同
static bool ResponseCheckItem(IFCRResult *result, const char *key, int val)
{
	if (!result)
		return false;
	result->moveFirstPart();
	ICRPart* part = NULL;
	while ((part = result->moveNextPart()) != NULL)
	{
		const char *strategy = part->getStrategyMatched();
		if (strategy == nullptr || *strategy == '\0')
			return false;

		rapidjson::Document doc;
		if (doc.Parse<0>(strategy).HasParseError())
		{// json串错误或者为空
			return false;
		}

		if (!(doc.HasMember("strategy") || doc["strategy"].IsArray()))
		{
			return false;
		}

		// 解析策略JSON串
		rapidjson::Value& strategyArry = doc["strategy"];
		for (unsigned iStrategy = 0; iStrategy < strategyArry.Size(); iStrategy++)
		{
			rapidjson::Value& strategy = strategyArry[iStrategy];
			if (!strategy.HasMember("respond") || !strategy["respond"].IsArray())
			{
				return false;
			}
			rapidjson::Value& respArry = strategy["respond"];
			for (unsigned iRespond = 0; iRespond < respArry.Size(); iRespond++)
			{
				rapidjson::Value& respond = respArry[iRespond];
				if (!respond.HasMember(key) || !respond[key].IsInt() || respond[key] != val)
				{
					return false;
				}
			}
		}
	}
	return true;
}

//判断对应测试用例是否开启自动化测试
BOOL JudgeFileJoinTest(std::string testName)
{
	std::string strSrcIni = "./JoinTestFile.ini";
	std::ifstream configFile(strSrcIni);
	std::string strJoinTestFile = GetCompletePath("/JoinTestFile.ini");

	//配置文件存在
	if (configFile.good())
	{
		dlplog_info(g_log_handle, "[%d] %s", __LINE__, strJoinTestFile.c_str());
	}
	else
	{
		dlplog_info(g_log_handle, "[%d] %s %s", __LINE__, "Does not exist:", strJoinTestFile.c_str());
		return TRUE;
	}
#ifdef WIN32
	char LP[1024];
	// 构造测试用例名称
	GetPrivateProfileStringA("case", testName.c_str(), "NULL", LP, 512, strSrcIni.c_str());
	int testValue2 = GetPrivateProfileIntA("case", testName.c_str(), 0, strSrcIni.c_str());
	int testValue = atoi(LP);

	//关闭测试
	if (testValue2 == 0)
	{
		iSkipTestNum++;
		std::cout << "this test case has been canceled ......" << endl;
		return FALSE;
	}
	else if (testValue == 0)
	{		
		return FALSE;
	}	
	//开启测试
	return TRUE;
#else 
	std::map<std::string, bool> testMap;
	std::string line;
	while (std::getline(configFile, line))
	{
		//解析key和value
		std::istringstream iss(line);
		std::string key, value;
		if (std::getline(iss, key, '=') && std::getline(iss, value))
		{
			testMap[key] = (std::stoi(value) == 1);
		}
	}
	auto it = testMap.find(testName);
	if (it != testMap.end())
	{
		if (it->second)
		{
			return true;
		}
		else
		{
			iSkipTestNum++;
			std::cout << "this test case has been canceled ......" << std::endl;
			return false;
		}
	}
	else
	{
		std::cout << "Test case '" << testName << "' is not found in the configuration file." << std::endl;
		return false;
	}
#endif
}

/***************************************************************************************/
//									具体功能测试用例									//
/***************************************************************************************/

//输出用例测试相关情况
void GetTestinfo()
{
	//输出当前时间
	time_t nowtime;
	time(&nowtime); 
	tm* p = localtime(&nowtime); 
	printf("%04d/%02d/%02d %02d:%02d:%02d\n", p->tm_year + 1900, p->tm_mon + 1, p->tm_mday, p->tm_hour, p->tm_min, p->tm_sec);
	printf("Current OS: %s\n", getOS().c_str());

	dlplog_info(g_log_handle, "[%d] %s %d", __LINE__, "The total number of skipped use cases is ", iSkipTestNum);
	printf("\nThe total number of skipped use cases is %d\n" , iSkipTestNum);
	std::string strJoinTestFile = GetCompletePath("/JoinTestFile.ini");
	printf("The path to the JoinTestFile.ini file is in: %s \n", strJoinTestFile.c_str());
}

#if 1

#ifdef __GNUC__
//linux版本测试
TEST_F(LdfcrFixture, linuxTest) {
	string testName = ::testing::UnitTest::GetInstance()->current_test_info()->name();
	if (!JudgeFileJoinTest(testName))
		return;
	dlplog_info(g_log_handle, "[%d] ===========================================TEST_F linuxTest is started===========================================", __LINE__);

	EXPECT_EQ(ldfcr.init(_T("test/data/nihao.data")), 0);
	EXPECT_TRUE(ldfcr.detect(_T("test/nihao.txt"), nullptr, nullptr));
}
#endif

#ifdef WIN32
//获取被阻断的泄露类型
TEST_F(LdfcrFixture, GetStopOutGoingType)
{
	string testName = ::testing::UnitTest::GetInstance()->current_test_info()->name();
	if (!JudgeFileJoinTest(testName))
		return;

	dlplog_info(g_log_handle, "[%d] ===========================================TEST_F GetStopOutGoingType is started===========================================", __LINE__);
	std::string completePath = GetCompletePath("/test/data/strategy_srv.data");//********
	dlplog_info(g_log_handle, "[%d] %s", __LINE__, completePath.c_str());

	EXPECT_EQ(ldfcr.init(_T("test/data/strategy_srv.data")), 0);
	EXPECT_TRUE(ldfcr.getOLT());
}
#endif

//允许文件外发申请测试
TEST_F(LdfcrFixture, OutSendApply)
{
	string testName = ::testing::UnitTest::GetInstance()->current_test_info()->name();
	if (!JudgeFileJoinTest(testName))
		return;

	dlplog_info(g_log_handle, "[%d] ===========================================TEST_F OutSendApply is started===========================================", __LINE__);
	std::string completePath = GetCompletePath("/test/data/strategy_srv.data");//********
	dlplog_info(g_log_handle, "[%d] %s", __LINE__, completePath.c_str());

	EXPECT_EQ(ldfcr.init(_T("test/data/strategy_srv.data")), 0);
	EXPECT_TRUE(ldfcr.outSendApply());
}

//检测结果json串中输出响应规则中泄露方式  
TEST_F(LdfcrFixture, GetResultLossType)
{
	string testName = ::testing::UnitTest::GetInstance()->current_test_info()->name();
	if (!JudgeFileJoinTest(testName))
		return;

	dlplog_info(g_log_handle, "[%d] ===========================================TEST_F GetResultLossType is started===========================================", __LINE__);
	std::string completePath = GetCompletePath("/test/tel/hahaha.data");//********
	dlplog_info(g_log_handle, "[%d] %s", __LINE__, completePath.c_str());

	if (!operSysJugXP()) // XP版本不支持
	{
		//EXPECT_EQ(ldfcr.init(_T("test/tel/s_image.data")), 0);	   	 20230803
		EXPECT_EQ(ldfcr.init(_T("test/tel/hahaha.data")), 0);

		BOOL sensitive;
		IFCRResult * result = NULL;
		EXPECT_TRUE(sensitive = ldfcr.detect(_T("test/code.bin"), (void**)&result));
		EXPECT_TRUE(ldfcr.getResultLossType(result, sensitive));
		CLEAN_COM(result);
	}
}

//源代码识别                
TEST_F(LdfcrFixture, SCRRec)
{
	string testName = ::testing::UnitTest::GetInstance()->current_test_info()->name();
	if (!JudgeFileJoinTest(testName))
		return;

	dlplog_info(g_log_handle, "[%d] ===========================================TEST_F SCRRec is started===========================================", __LINE__);
	std::string completePath = GetCompletePath("/test/data/strategy_srv.data");//********
	dlplog_info(g_log_handle, "[%d] %s", __LINE__, completePath.c_str());
	
	if (!operSysJugXP()) // XP版本和Linux32位不支持SCR第三方库
	{
		if (!operSysJugLinux32Bit())
		{
			EXPECT_EQ(ldfcr.init(_T("test/data/strategy_srv.data")), 0);
			EXPECT_TRUE(ldfcr.detect(_T("test/20230802.bin")));
			EXPECT_TRUE(ldfcr.detect(_T("test/code.txt")));
			EXPECT_FALSE(ldfcr.detect(_T("test/other0.txt")));
		}	
	}
}

//源代码识别	模式一	SCRModel=0               
TEST_F(LdfcrFixture, SCRModel_1)
{
	string testName = ::testing::UnitTest::GetInstance()->current_test_info()->name();
	if (!JudgeFileJoinTest(testName))
		return;

	dlplog_info(g_log_handle, "[%d] ===========================================TEST_F SCRModel_1 is started===========================================", __LINE__);
	std::string completePath = GetCompletePath("/test/data/SCRModel/SCRModel_1.data");
	dlplog_info(g_log_handle, "[%d] %s", __LINE__, completePath.c_str());
	
	if (!operSysJugXP()) // XP版本和Linux32位不支持SCR第三方库
	{
		if (!operSysJugLinux32Bit())
		{
			EXPECT_EQ(ldfcr.init(_T("/test/data/SCRModel/SCRModel_1.data")), 0);
			EXPECT_TRUE(ldfcr.detect(_T("test/20230802.bin")));
			EXPECT_TRUE(ldfcr.detect(_T("test/code.txt")));
			EXPECT_FALSE(ldfcr.detect(_T("test/other0.txt")));
		}
	}
}

//源代码识别	模式二	SCRMode2=1               
TEST_F(LdfcrFixture, SCRModel_2)
{
	string testName = ::testing::UnitTest::GetInstance()->current_test_info()->name();
	if (!JudgeFileJoinTest(testName))
		return;

	dlplog_info(g_log_handle, "[%d] ===========================================TEST_F SCRModel_2 is started===========================================", __LINE__);
	std::string completePath = GetCompletePath("/test/data/SCRModel/SCRModel_2.data");
	dlplog_info(g_log_handle, "[%d] %s", __LINE__, completePath.c_str());
	
	if (!operSysJugXP()) // XP版本和Linux32位不支持SCR第三方库
	{
		if (!operSysJugLinux32Bit())
		{
			EXPECT_EQ(ldfcr.init(_T("/test/data/SCRModel/SCRModel_2.data")), 0);
			EXPECT_TRUE(ldfcr.detect(_T("test/20230802.bin")));
			EXPECT_TRUE(ldfcr.detect(_T("test/code.txt")));
			EXPECT_FALSE(ldfcr.detect(_T("test/other0.txt")));
		}
	}
}

//源代码识别	模式三	SCRMode3=2               
TEST_F(LdfcrFixture, SCRModel_3)
{
	string testName = ::testing::UnitTest::GetInstance()->current_test_info()->name();
	if (!JudgeFileJoinTest(testName))
		return;

	dlplog_info(g_log_handle, "[%d] ===========================================TEST_F SCRModel_3 is started===========================================", __LINE__);
	std::string completePath = GetCompletePath("/test/data/SCRModel/SCRModel_3.data");
	dlplog_info(g_log_handle, "[%d] %s", __LINE__, completePath.c_str());

	if (!operSysJugXP()) // XP版本和Linux32位不支持SCR第三方库
	{
		if (!operSysJugLinux32Bit())
		{
			EXPECT_EQ(ldfcr.init(_T("/test/data/SCRModel/SCRModel_3.data")), 0);
			EXPECT_TRUE(ldfcr.detect(_T("test/20230802.bin")));
			EXPECT_TRUE(ldfcr.detect(_T("test/code.txt")));
			EXPECT_FALSE(ldfcr.detect(_T("test/other0.txt")));
		}
	}
}

//源代码识别	模式四	SCRMode4=-1               
TEST_F(LdfcrFixture, SCRModel_4)
{
	string testName = ::testing::UnitTest::GetInstance()->current_test_info()->name();
	if (!JudgeFileJoinTest(testName))
		return;

	dlplog_info(g_log_handle, "[%d] ===========================================TEST_F SCRModel_4 is started===========================================", __LINE__);
	std::string completePath = GetCompletePath("/test/data/SCRModel/SCRModel_4.data");
	dlplog_info(g_log_handle, "[%d] %s", __LINE__, completePath.c_str());

	if (!operSysJugXP()) // XP版本和Linux32位不支持SCR第三方库
	{
		if (!operSysJugLinux32Bit())
		{
			EXPECT_EQ(ldfcr.init(_T("/test/data/SCRModel/SCRModel_4.data")), 0);
			EXPECT_TRUE(ldfcr.detect(_T("test/20230802.bin")));
			EXPECT_TRUE(ldfcr.detect(_T("test/code.txt")));
			EXPECT_FALSE(ldfcr.detect(_T("test/other0.txt")));
		}
	}
}

//源代码识别 代码的各种语言——识别文本内容
TEST_F(LdfcrFixture, SCR_MultiLanguege)
{
	string testName = ::testing::UnitTest::GetInstance()->current_test_info()->name();
	if (!JudgeFileJoinTest(testName))
		return;

	dlplog_info(g_log_handle, "[%d] ===========================================TEST_F SCR_MultiLanguege is started===========================================", __LINE__);
	std::string completePath = GetCompletePath("/test/data/strategy_srv.data");
	dlplog_info(g_log_handle, "[%d] %s", __LINE__, completePath.c_str());

	if (!operSysJugXP()) // XP版本和Linux32位不支持SCR第三方库
	{
		if (!operSysJugLinux32Bit())
		{
			EXPECT_EQ(ldfcr.init(_T("/test/data/strategy_srv.data")), 0);
			EXPECT_TRUE(ldfcr.detect(_T("test/DiffLanguegeSCR/Assembly.txt")));
			EXPECT_TRUE(ldfcr.detect(_T("test/DiffLanguegeSCR/C.txt")));
			EXPECT_TRUE(ldfcr.detect(_T("test/DiffLanguegeSCR/C++.txt")));
			EXPECT_TRUE(ldfcr.detect(_T("test/DiffLanguegeSCR/C_sharp.txt")));
			EXPECT_TRUE(ldfcr.detect(_T("test/DiffLanguegeSCR/Delphi.txt")));
			EXPECT_TRUE(ldfcr.detect(_T("test/DiffLanguegeSCR/Fortan.txt")));
			EXPECT_TRUE(ldfcr.detect(_T("test/DiffLanguegeSCR/Go.txt")));
			EXPECT_TRUE(ldfcr.detect(_T("test/DiffLanguegeSCR/HTML.txt")));
			EXPECT_TRUE(ldfcr.detect(_T("test/DiffLanguegeSCR/Java.txt")));
			EXPECT_TRUE(ldfcr.detect(_T("test/DiffLanguegeSCR/JavaScript.txt")));
			EXPECT_TRUE(ldfcr.detect(_T("test/DiffLanguegeSCR/MATLAB.txt")));
			EXPECT_TRUE(ldfcr.detect(_T("test/DiffLanguegeSCR/Perl.txt")));
			EXPECT_TRUE(ldfcr.detect(_T("test/DiffLanguegeSCR/PHP.txt")));
			EXPECT_TRUE(ldfcr.detect(_T("test/DiffLanguegeSCR/Python.txt")));
			EXPECT_TRUE(ldfcr.detect(_T("test/DiffLanguegeSCR/R.txt")));
			EXPECT_TRUE(ldfcr.detect(_T("test/DiffLanguegeSCR/Ruby.txt")));
			EXPECT_TRUE(ldfcr.detect(_T("test/DiffLanguegeSCR/SQL.txt")));
			EXPECT_TRUE(ldfcr.detect(_T("test/DiffLanguegeSCR/Swift.txt")));
			EXPECT_TRUE(ldfcr.detect(_T("test/DiffLanguegeSCR/Kotlin.txt")));
			EXPECT_TRUE(ldfcr.detect(_T("test/DiffLanguegeSCR/Rust.txt")));
			EXPECT_TRUE(ldfcr.detect(_T("test/DiffLanguegeSCR/Scala.txt")));
			EXPECT_TRUE(ldfcr.detect(_T("test/DiffLanguegeSCR/dialog.txt")));
		}		
	}
}

//源代码识别 代码的各种语言——识别文件后缀
TEST_F(LdfcrFixture, SCR_MultiLanguege_Ext)
{
	string testName = ::testing::UnitTest::GetInstance()->current_test_info()->name();
	if (!JudgeFileJoinTest(testName))
		return;

	dlplog_info(g_log_handle, "[%d] ===========================================TEST_F SCR_MultiLanguege_Ext is started===========================================", __LINE__);
	std::string completePath = GetCompletePath("/test/data/strategy_srv.data");
	dlplog_info(g_log_handle, "[%d] %s", __LINE__, completePath.c_str());

	if (!operSysJugXP()) // XP版本和Linux32位不支持SCR第三方库
	{
		if (!operSysJugLinux32Bit())
		{
			EXPECT_EQ(ldfcr.init(_T("/test/data/strategy_srv.data")), 0);
			EXPECT_TRUE(ldfcr.detect(_T("test/DiffLanguegeSCR_Ext/Assembly.asm")));
			EXPECT_TRUE(ldfcr.detect(_T("test/DiffLanguegeSCR_Ext/C.c")));
			EXPECT_TRUE(ldfcr.detect(_T("test/DiffLanguegeSCR_Ext/C++.cpp")));
			EXPECT_TRUE(ldfcr.detect(_T("test/DiffLanguegeSCR_Ext/C_sharp.cxx")));
			EXPECT_TRUE(ldfcr.detect(_T("test/DiffLanguegeSCR_Ext/Delphi.dpr")));
			EXPECT_TRUE(ldfcr.detect(_T("test/DiffLanguegeSCR_Ext/Fortan.f90")));
			EXPECT_TRUE(ldfcr.detect(_T("test/DiffLanguegeSCR_Ext/Go.go")));
			EXPECT_TRUE(ldfcr.detect(_T("test/DiffLanguegeSCR_Ext/HTML.html")));
			EXPECT_TRUE(ldfcr.detect(_T("test/DiffLanguegeSCR_Ext/Java.java")));
			EXPECT_TRUE(ldfcr.detect(_T("test/DiffLanguegeSCR_Ext/JavaScript.js")));
			EXPECT_TRUE(ldfcr.detect(_T("test/DiffLanguegeSCR_Ext/MATLAB.m")));
			EXPECT_TRUE(ldfcr.detect(_T("test/DiffLanguegeSCR_Ext/Perl.pl")));
			EXPECT_TRUE(ldfcr.detect(_T("test/DiffLanguegeSCR_Ext/PHP.php")));
			EXPECT_TRUE(ldfcr.detect(_T("test/DiffLanguegeSCR_Ext/Python.py")));
			EXPECT_TRUE(ldfcr.detect(_T("test/DiffLanguegeSCR_Ext/R.r")));
			EXPECT_TRUE(ldfcr.detect(_T("test/DiffLanguegeSCR_Ext/Ruby.rb")));
			EXPECT_TRUE(ldfcr.detect(_T("test/DiffLanguegeSCR_Ext/SQL.sql")));
			EXPECT_TRUE(ldfcr.detect(_T("test/DiffLanguegeSCR_Ext/Swift.swift")));
			EXPECT_TRUE(ldfcr.detect(_T("test/DiffLanguegeSCR_Ext/Kotlin.kt")));
			EXPECT_TRUE(ldfcr.detect(_T("test/DiffLanguegeSCR_Ext/Rust.rs")));
			EXPECT_TRUE(ldfcr.detect(_T("test/DiffLanguegeSCR_Ext/scala.scala")));
			EXPECT_TRUE(ldfcr.detect(_T("test/DiffLanguegeSCR_Ext/dialog.dialog")));
		}
	}
}

//单位信息的表格被误判为源代码
TEST_F(LdfcrFixture, Excle_SCR)
{
	string testName = ::testing::UnitTest::GetInstance()->current_test_info()->name();
	if (!JudgeFileJoinTest(testName))
		return;

	dlplog_info(g_log_handle, "[%d] ===========================================TEST_F Excle_SCR is started===========================================", __LINE__);
	std::string completePath = GetCompletePath("/test/data/code.data");
	dlplog_info(g_log_handle, "[%d] %s", __LINE__, completePath.c_str());

	if (!operSysJugXP()) // XP版本和Linux32位不支持SCR第三方库
	{
		if (!operSysJugLinux32Bit())
		{
			EXPECT_EQ(ldfcr.init(_T("/test/data/code.data")), 0);
			EXPECT_FALSE(ldfcr.detect(_T("test/单位信息登记(职业健康).xlsx")));
		}
	}
}

// 源代码文本提取200M文件测试——SCRMode4=-1  
TEST_F(LdfcrFixture, SCR_200MB)
{
	string testName = ::testing::UnitTest::GetInstance()->current_test_info()->name();
	if (!JudgeFileJoinTest(testName))
		return;

	dlplog_info(g_log_handle, "[%d] ===========================================TEST_F SCR_200MB is started===========================================", __LINE__);
	std::string completePath = GetCompletePath("/test/data/SCRModel/SCRModel_4.data");
	dlplog_info(g_log_handle, "[%d] %s", __LINE__, completePath.c_str());
	
	if (!operSysJugXP()) // XP版本和Linux32位不支持SCR第三方库
	{
		if (!operSysJugLinux32Bit())
		{
			EXPECT_EQ(ldfcr.init(_T("/test/data/SCRModel/SCRModel_4.data")), 0);

			//创建201mb文件
			EXPECT_EQ(createMBCnFile(_T("test/Cn201mb.txt"),201), 1);
			EXPECT_FALSE(ldfcr.detect(_T("test/Cn201mb.txt")));
			EXPECT_EQ(deleteEmptyFile(_T("test/Cn201mb.txt")), 1);
		}
	}
}

//D3源代码屏蔽网页浏览操作
TEST_F(LdfcrFixture, WebBrowser_SCR)
{
	string testName = ::testing::UnitTest::GetInstance()->current_test_info()->name();
	if (!JudgeFileJoinTest(testName))
		return;

	dlplog_info(g_log_handle, "[%d] ===========================================TEST_F WebBrowser_SCR is started===========================================", __LINE__);
	std::string completePath = GetCompletePath("/test/data/strategy_srv.data");
	dlplog_info(g_log_handle, "[%d] %s", __LINE__, completePath.c_str());

	EXPECT_EQ(ldfcr.init(_T("/test/data/strategy_srv.data")), 0);
	EXPECT_FALSE(ldfcr.detect(_T("test/20230802.bin"), NULL, NULL,14));
	EXPECT_FALSE(ldfcr.detect(_T("test/code.txt"), NULL, NULL,14));
}

// 测试json剪切接口是否准确
TEST_F(LdfcrFixture, JugCutJsonSize)
{
	string testName = ::testing::UnitTest::GetInstance()->current_test_info()->name();
	if (!JudgeFileJoinTest(testName))
		return;
	dlplog_info(g_log_handle, "[%d] ===========================================TEST_F JugCutJsonSize is started===========================================", __LINE__);
	EXPECT_TRUE(ldfcr.JugCutJsonSize(_T("test/json.json"), 256));	//该测试文件长度256
}

//json响应规则中是否存在 ruletype 字段（密码错误阻断后）
TEST_F(LdfcrFixture, existRuletypeInJson)
{
	string testName = ::testing::UnitTest::GetInstance()->current_test_info()->name();
	if (!JudgeFileJoinTest(testName))
		return;

	dlplog_info(g_log_handle, "[%d] ===========================================TEST_F existRuletypeInJson is started===========================================", __LINE__);
	std::string completePath = GetCompletePath("/test/tel/s_image.data");
	dlplog_info(g_log_handle, "[%d] %s", __LINE__, completePath.c_str());

	EXPECT_EQ(ldfcr.init(_T("test/tel/s_image.data")), 0);

	FCRPARAM fcrParam;
	fcrParam.use_all_rule = TRUE;   //启用所有规则
	fcrParam.target_class_code = 3; //达到或超过代码3，即停止
	fcrParam.output_detail = TRUE;  //要求返回时输出策略的细节信息
	fcrParam.devType = 1;
	fcrParam.opType = 0;
	fcrParam.block_on_pwderror = 1;//密码错误阻断

	BOOL sensitive;
	IFCRResult * result = NULL;
	EXPECT_TRUE(sensitive = ldfcr.detect(_T("test/customRulesName_225.rar"), (void**)&result,NULL,0, &fcrParam));
	EXPECT_TRUE(ldfcr.JugExsitRuletypeInJson(result,sensitive));
	CLEAN_COM(result);
}

//自定义响应规则 带密码强制阻断
TEST_F(LdfcrFixture, CustomRespodRulesPwdError)
{
	string testName = ::testing::UnitTest::GetInstance()->current_test_info()->name();
	if (!JudgeFileJoinTest(testName))
		return;

	dlplog_info(g_log_handle, "[%d] ===========================================TEST_F CustomRespodRulesPwdError is started===========================================", __LINE__);
	std::string completePath = GetCompletePath("/test/tel/s_image.data");
	dlplog_info(g_log_handle, "[%d] %s", __LINE__, completePath.c_str());

	EXPECT_EQ(ldfcr.init(_T("test/tel/s_image.data")), 0);

	FCRPARAM fcrParam;
	fcrParam.use_all_rule = TRUE;   //启用所有规则
	fcrParam.target_class_code = 3; //达到或超过代码3，即停止
	fcrParam.output_detail = TRUE;  //要求返回时输出策略的细节信息
	fcrParam.devType = 1;
	fcrParam.opType = 0;
	fcrParam.block_on_pwderror = 1;//密码错误阻断

	Ldfcr::RESPONDPARAM _rspParam;
	_rspParam.m_ErrorCode = LDFCR_PWDERROR;
	_rspParam.m_iRulesId = 225;
	EXPECT_TRUE(ldfcr.checkRespondRulesName(_T("test/customRulesName_225.rar"), &_rspParam,0,&fcrParam));
}

//自定义响应规则 超时强制阻断
TEST_F(LdfcrFixture, CustomRespodRulesOtdError) //注：该用例失败 原因:使用文本检测后 超时中断 敏感结果未返回敏感
{
	string testName = ::testing::UnitTest::GetInstance()->current_test_info()->name();
	if (!JudgeFileJoinTest(testName))
		return;

	dlplog_info(g_log_handle, "[%d] ===========================================TEST_F CustomRespodRulesOtdError is started===========================================", __LINE__);
	std::string completePath = GetCompletePath("/test/tel/s_image.data");
	dlplog_info(g_log_handle, "[%d] %s", __LINE__, completePath.c_str());

	EXPECT_EQ(ldfcr.init(_T("test/tel/s_image.data")), 0);

	FCRPARAM fcrParam;
	fcrParam.use_all_rule = TRUE;   //启用所有规则
	fcrParam.target_class_code = 3; //达到或超过代码3，即停止
	fcrParam.output_detail = TRUE;  //要求返回时输出策略的细节信息
	fcrParam.devType = 1;
	fcrParam.opType = 0;
	fcrParam.block_on_timeout = 1;	//超时阻断
	int m_time_input = 10;// 10毫秒
	fcrParam.timeout = m_time_input;//超时时间

	Ldfcr::RESPONDPARAM _rspParam;
	_rspParam.m_ErrorCode = LDFCR_TIMEOUT;
	_rspParam.m_iRulesId = 224;
	EXPECT_TRUE(ldfcr.checkRespondRulesName(_T("test/5m500.pdf"), &_rspParam, 0, &fcrParam));
}

// test 空策略文档测试，因为没有初始化wstring在赋予指向空的指针时出错，因此改为初始化时赋予空值，赋予空指针时return
TEST_F(LdfcrFixture, strategy_empty) 
{
	string testName = ::testing::UnitTest::GetInstance()->current_test_info()->name();
	if (!JudgeFileJoinTest(testName))
		return;

	dlplog_info(g_log_handle, "[%d] ===========================================TEST_F strategy_empty is started===========================================", __LINE__);
	std::string completePath = GetCompletePath("/test/data/strategy_empty.data");//********
	dlplog_info(g_log_handle, "[%d] %s", __LINE__, completePath.c_str());
	
	EXPECT_EQ(ldfcr.init(_T("test/data/strategy_empty.data")), 0);
	EXPECT_FALSE(ldfcr.detect(_T("test/numbers.txt")));
}

// Image test
TEST_F(LdfcrFixture, ImageTest_moreEasy_OCR) 
{
	string testName = ::testing::UnitTest::GetInstance()->current_test_info()->name();
	if (!JudgeFileJoinTest(testName))
		return;

	dlplog_info(g_log_handle, "[%d] ===========================================TEST_F ImageTest_moreEasy_OCR is started===========================================", __LINE__);
	std::string completePath = GetCompletePath("/test/tel/s_image.data");//********
	dlplog_info(g_log_handle, "[%d] %s", __LINE__, completePath.c_str());

	//if (!operSysJugXP()) // XP版本和Linux32位不支持OCR第三方库
	{
		if (!operSysJugLinux32Bit())
		{
			EXPECT_EQ(ldfcr.init(_T("test/tel/s_image.data")), 0);
			EXPECT_TRUE(ldfcr.detect(_T("test/testOCR.png1"), nullptr, nullptr));//20230803
			//EXPECT_TRUE(ldfcr.detect(_T("test/后缀防篡改.pngg"), nullptr, nullptr));
		}
	}
}

// OCR_EMBED_Test test
TEST_F(LdfcrFixture, OCR_EMBED_Test_OCR) 
{
	string testName = ::testing::UnitTest::GetInstance()->current_test_info()->name();
	if (!JudgeFileJoinTest(testName))
		return;

	dlplog_info(g_log_handle, "[%d] ===========================================TEST_F OCR_EMBED_Test_OCR is started===========================================", __LINE__);
	std::string completePath = GetCompletePath("/test/tel/s_image.data");//********
	dlplog_info(g_log_handle, "[%d] %s", __LINE__, completePath.c_str());

	//if (!operSysJugXP()) // XP版本和Linux32位不支持 嵌入式OCR
	{
		if (!operSysJugLinux32Bit())
		{
			EXPECT_EQ(ldfcr.init(_T("test/tel/s_image.data")), 0);

			ldfcr_ControlModule(LDFCR_MODULE_OCR_EMBED, 0);
			EXPECT_FALSE(ldfcr.detect(_T("test/imageEMBEDOCR.doc"), nullptr, nullptr)); //不加，默认初始开启

			ldfcr_ControlModule(LDFCR_MODULE_OCR_EMBED, 1);
			EXPECT_TRUE(ldfcr.detect(_T("test/imageEMBEDOCR.doc"), nullptr, nullptr));
		}	
	}
}

//图片识别
TEST_F(LdfcrFixture, Image360png_Test_OCR)
{
	string testName = ::testing::UnitTest::GetInstance()->current_test_info()->name();
	if (!JudgeFileJoinTest(testName))
		return;

	dlplog_info(g_log_handle, "[%d] ===========================================TEST_F Image360png_Test_OCR is started===========================================", __LINE__);
	std::string completePath = GetCompletePath("/test/tel/s_ocr_image.data");
	dlplog_info(g_log_handle, "[%d] %s", __LINE__, completePath.c_str());

	//if (!operSysJugXP()) // XP版本和Linux32位不支持OCR第三方库
	{
		if (!operSysJugLinux32Bit())
		{
			EXPECT_EQ(ldfcr.init(_T("test/tel/s_ocr_image.data")), 0);
			EXPECT_TRUE(ldfcr.detect(_T("test/OCR360.png"), nullptr, nullptr));//2024.08.09版本及以上的DlpOCR.dll支持该图片
		}
	}
}

//// 20241206 暂时屏蔽 下个季度再开启
//// OCRtype_swith test		20241101
//TEST_F(LdfcrFixture, OCRtype_swith) {
//	dlplog_info(g_log_handle, "[%d] ===========================================TEST_F OCRtype_swith is started===========================================", __LINE__);
//	std::string completePath = GetCompletePath("/test/data/OCR_Model/IdCard_Model12.data");//********
//	dlplog_info(g_log_handle, "[%d] %s", __LINE__, completePath.c_str());
//
//	EXPECT_EQ(ldfcr.init(_T("/test/data/OCR_Model/IdCard_Model12.data")), 0);
//	IFCRDripResult *dripResult = NULL;
//
//	if (!operSysJugXP()) // XP版本不支持 
//	{
//		//2023 02 13update 原本默认应该是FALSE但是因为检测到程序架构问题 LDFCR_MOUDLE的开关关系的是数据在终端和服务端之间的数据发送开关所以在终端的测试上一样会测试到敏感	
//		//2024 11 01 开关改为LDFCR_MODULE_OCR_TYPE，测试ocr检测开关是否有效
//		ldfcr_ControlModule(LDFCR_MODULE_OCR_TYPE, 0);
//		EXPECT_FALSE(ldfcr.detect(_T("test/OCRtype/IDCard.jpg_"), nullptr, nullptr));
//		ldfcr_ControlModule(LDFCR_MODULE_OCR_TYPE, 1);
//		EXPECT_TRUE(ldfcr.detect(_T("test/OCRtype/IDCard.jpg_"), nullptr, nullptr));
//	}
//}

#ifdef __APPLE__
// 2024 5 22 mac平台下出现pcre因为递归导致的内存溢出导致的程序崩溃问题 
TEST_F(LdfcrFixture, RegexMacRecursionTest) {
	string testName = ::testing::UnitTest::GetInstance()->current_test_info()->name();
	if (!JudgeFileJoinTest(testName))
		return;

	dlplog_info(g_log_handle, "[%d] ===========================================TEST_F RegexMacRecursionTest is started===========================================", __LINE__);
	std::string completePath = GetCompletePath("/test/data/recursionRegex.data");
	dlplog_info(g_log_handle, "[%d] %s", __LINE__, completePath.c_str());

	EXPECT_EQ(ldfcr.init(_T("test/data/recursionRegex.data")), 0);
	EXPECT_TRUE(ldfcr.detect(_T("test/recursionRegexBundle.txt"), nullptr, nullptr));
}
#endif

// 身份证正则表达式lua部分如果因为第一段出现的不是北京区号开头则会因为被返回false
TEST_F(LdfcrFixture, BJRegularLua)
{
	string testName = ::testing::UnitTest::GetInstance()->current_test_info()->name();
	if (!JudgeFileJoinTest(testName))
		return;

	dlplog_info(g_log_handle, "[%d] ===========================================TEST_F BJRegularLua is started===========================================", __LINE__);
	std::string completePath = GetCompletePath("/test/data/BeijingXMRegularLua.data");//********
	dlplog_info(g_log_handle, "[%d] %s", __LINE__, completePath.c_str());

	EXPECT_EQ(ldfcr.init(_T("test/data/BeijingXMRegularLua.data")), 0);
	EXPECT_TRUE(ldfcr.detect(_T("test/BeijingXMTest.txt"), nullptr, nullptr));
}

// 2025 6 30 发现数据标识符高级模式 身份证校验算法 lua 校验位计算存在问题
TEST_F(LdfcrFixture, advSFZRegularLua)
{
	string testName = ::testing::UnitTest::GetInstance()->current_test_info()->name();
	if (!JudgeFileJoinTest(testName))
		return;

	dlplog_info(g_log_handle, "[%d] ===========================================TEST_F BJRegularLua is started===========================================", __LINE__);
	std::string completePath = GetCompletePath("/test/data/adv-sfz.data");//********
	dlplog_info(g_log_handle, "[%d] %s", __LINE__, completePath.c_str());

	EXPECT_EQ(ldfcr.init(_T("test/data/adv-sfz.data")), 0);
	EXPECT_TRUE(ldfcr.detect(_T("test/SFZ/SFZ1.txt"), nullptr, nullptr));
}

//测试 16位或19位（带空格或不带空格）的银行卡号
TEST_F(LdfcrFixture, Regular_and_Empty_BANKCARD)
{
	string testName = ::testing::UnitTest::GetInstance()->current_test_info()->name();
	if (!JudgeFileJoinTest(testName))
		return;

	dlplog_info(g_log_handle, "[%d] ===========================================TEST_F Regular_and_Empty_BANKCARD is started===========================================", __LINE__);
	std::string completePath = GetCompletePath("/test/data/BankCards.data");
	dlplog_info(g_log_handle, "[%d] %s", __LINE__, completePath.c_str());

	EXPECT_EQ(ldfcr.init(_T("test/data/BankCards.data")), 0);
	EXPECT_TRUE(ldfcr.detect(_T("test/BankCards.txt"), nullptr, nullptr));
}

// 案例测试的敏感点与检测后光亮的位置匹配不上
TEST_F(LdfcrFixture, shiftBug)
{
	string testName = ::testing::UnitTest::GetInstance()->current_test_info()->name();
	if (!JudgeFileJoinTest(testName))
		return;

	dlplog_info(g_log_handle, "[%d] ===========================================TEST_F shiftBug is started===========================================", __LINE__);
	std::string completePath = GetCompletePath("/test/tel/s_text.data");//********
	dlplog_info(g_log_handle, "[%d] %s", __LINE__, completePath.c_str());

	EXPECT_EQ(ldfcr.init(_T("test/tel/s_text.data")), 0);

	IFCRResult *result = NULL;
	BOOL sensitive;

	EXPECT_TRUE(sensitive = ldfcr.detect(_T("test/钢铁侠通讯协议.txt"), (void**)&result));
	EXPECT_TRUE(ldfcr.checkKeyWordsInResultText(result, sensitive));
	CLEAN_COM(result);

	EXPECT_TRUE(sensitive = ldfcr.detect(_T("test/钢铁侠能源简介.txt"), (void**)&result));
	EXPECT_TRUE(ldfcr.checkKeyWordsInResultText(result, sensitive));
	CLEAN_COM(result);

	EXPECT_TRUE(sensitive = ldfcr.detect(_T("test/钢铁侠解决方案.txt"), (void**)&result));
	EXPECT_TRUE(ldfcr.checkKeyWordsInResultText(result, sensitive));
	CLEAN_COM(result);

	EXPECT_TRUE(sensitive = ldfcr.detect(_T("test/text123.txt"), (void**)&result));  //注：修改成文本检测后 该用例失败 原因:返回的片段未包含敏感的关键字
	EXPECT_TRUE(ldfcr.checkKeyWordsInResultText(result, sensitive));
	CLEAN_COM(result);

	EXPECT_TRUE(sensitive = ldfcr.detect(_T("test/beijinghuangyingni.txt"), (void**)&result));
	EXPECT_TRUE(ldfcr.checkKeyWordsInResultText(result, sensitive));
	CLEAN_COM(result);
}

//测试高亮位置和敏感匹配的计数点是否能匹配上 
TEST_F(LdfcrFixture, hightLightShiftPos1) 
{
	string testName = ::testing::UnitTest::GetInstance()->current_test_info()->name();
	if (!JudgeFileJoinTest(testName))
		return;

	dlplog_info(g_log_handle, "[%d] ===========================================TEST_F hightLightShiftPos1 is started===========================================", __LINE__);
	std::string completePath = GetCompletePath("/test/tel/s_text.data");//********
	dlplog_info(g_log_handle, "[%d] %s", __LINE__, completePath.c_str());

	EXPECT_EQ(ldfcr.init(_T("test/tel/s_text.data")), 0);

	IFCRResult *result = NULL;
	BOOL sensitive;

	EXPECT_TRUE(sensitive = ldfcr.detect(_T("test/钢铁侠能源简介.txt"), (void**)&result,NULL,0,NULL,Ldfcr::_enTcrOpen::CLOSE));
	EXPECT_TRUE(ldfcr.checkShiftPos(result, sensitive,2675));
	CLEAN_COM(result);
	
	EXPECT_TRUE(sensitive = ldfcr.detect(_T("test/钢铁侠解决方案.txt"), (void**)&result, NULL, 0, NULL, Ldfcr::_enTcrOpen::CLOSE));
	EXPECT_TRUE(ldfcr.checkShiftPos(result, sensitive, 691));
	CLEAN_COM(result);

	EXPECT_TRUE(sensitive = ldfcr.detect(_T("test/beijinghuangyingni.txt"), (void**)&result, NULL, 0, NULL, Ldfcr::_enTcrOpen::CLOSE));
	EXPECT_TRUE(ldfcr.checkShiftPos(result, sensitive, 66));
	CLEAN_COM(result);

	EXPECT_TRUE(sensitive = ldfcr.detect(_T("test/text123.txt"), (void**)&result, NULL, 0, NULL, Ldfcr::_enTcrOpen::CLOSE));
	EXPECT_TRUE(ldfcr.checkShiftPos(result, sensitive, 1419));
	CLEAN_COM(result);

	EXPECT_TRUE(sensitive = ldfcr.detect(_T("test/7.55Mb.txt"), (void**)&result, NULL, 0, NULL, Ldfcr::_enTcrOpen::CLOSE));
	EXPECT_TRUE(ldfcr.checkShiftPos(result, sensitive, 0));
	CLEAN_COM(result);

}

// 高亮测试 checktimes == 2
TEST_F(LdfcrFixture, hightLightShiftPos_TuZhangInDOC_OCR) 
{
	string testName = ::testing::UnitTest::GetInstance()->current_test_info()->name();
	if (!JudgeFileJoinTest(testName))
		return;

	dlplog_info(g_log_handle, "[%d] ===========================================TEST_F hightLightShiftPos_TuZhangInDOC_OCR is started===========================================", __LINE__);
	std::string completePath = GetCompletePath("/test/data/s_zhang.data");//********
	dlplog_info(g_log_handle, "[%d] %s", __LINE__, completePath.c_str());

	//if (!operSysJugXP()) // XP版本和Linux32位不支持OCR第三方库
	{
		if (!operSysJugLinux32Bit())
		{
			EXPECT_EQ(ldfcr.init(_T("test/data/s_zhang.data")), 0);
			//一个是文档一个是嵌套图片，导致程序的检测位置不同无法计算为checktimes = 2 所以无法触发敏感 2024 12 3
			/*EXPECT_TRUE(ldfcr.checkShiftPos2(_T("test/滴滴电子发票（椭圆形印章）.docx"), 31, 77));*/
			//EXPECT_TRUE(ldfcr.checkShiftPos2(_T("test/embedyangmi.docx"), 41, 84)); 屏蔽说明：模型文件优化 2025 4 3
			IFCRResult *result = NULL;
			BOOL sensitive;
			EXPECT_TRUE(sensitive = ldfcr.detect(_T("test/embedyangmi.docx"), (void**)&result));
			EXPECT_TRUE(ldfcr.checkShiftPos2(result, sensitive, 39,81));
			CLEAN_COM(result);
		}		
	}
}

TEST_F(LdfcrFixture, hightLightShiftPos_TuZhangInPNG_OCR)
{
	string testName = ::testing::UnitTest::GetInstance()->current_test_info()->name();
	if (!JudgeFileJoinTest(testName))
		return;

	dlplog_info(g_log_handle, "[%d] ===========================================TEST_F hightLightShiftPos_OCR is started===========================================", __LINE__);
	std::string completePath = GetCompletePath("/test/data/s_tuzhang.data");//********
	dlplog_info(g_log_handle, "[%d] %s", __LINE__, completePath.c_str());

	//if (!operSysJugXP()) // XP版本和Linux32位不支持OCR第三方库
	{
		if (!operSysJugLinux32Bit())
		{
			EXPECT_EQ(ldfcr.init(_T("test/data/s_tuzhang.data")), 0);

			IFCRResult *result = NULL;
			BOOL sensitive;
			EXPECT_TRUE(sensitive = ldfcr.detect(_T("test/tuzhang"), (void**)&result));
			EXPECT_TRUE(ldfcr.checkShiftPos2(result, sensitive, 0, 44));
			CLEAN_COM(result);
		}
	}
}

TEST_F(LdfcrFixture, KEYWORD_STAMP_OCR) 
{
	string testName = ::testing::UnitTest::GetInstance()->current_test_info()->name();
	if (!JudgeFileJoinTest(testName))
		return;

	dlplog_info(g_log_handle, "[%d] ===========================================TEST_F KEYWORD_STAMP_OCR is started===========================================", __LINE__);
	std::string completePath = GetCompletePath("/test/data/s_tuzhang.data");//********
	dlplog_info(g_log_handle, "[%d] %s", __LINE__, completePath.c_str());

	//if (!operSysJugXP()) // XP版本和Linux32位不支持OCR第三方库
	{
		if (!operSysJugLinux32Bit())
		{
			EXPECT_EQ(ldfcr.init(_T("test/data/s_tuzhang.data")), 0);
			EXPECT_TRUE(ldfcr.detect(_T("test/tuzhang")));
		}
	}
}

//OCR类型识别：身份证 [ID Card]
TEST_F(LdfcrFixture, KEYWORD_IDcard_OCR)
{
	string testName = ::testing::UnitTest::GetInstance()->current_test_info()->name();
	if (!JudgeFileJoinTest(testName))
		return;

	dlplog_info(g_log_handle, "[%d] ===========================================TEST_F KEYWORD_IDcard_OCR is started===========================================", __LINE__);
	std::string completePath = GetCompletePath("/test/data/OCR/o_IDcard.data");
	dlplog_info(g_log_handle, "[%d] %s", __LINE__, completePath.c_str());

	//if (!operSysJugXP()) // XP版本和Linux32位不支持OCR第三方库
	{
		if (!operSysJugLinux32Bit())
		{
			EXPECT_EQ(ldfcr.init(_T("/test/data/OCR/o_IDcard.data")), 0);
			EXPECT_TRUE(ldfcr.detect(_T("test/OCRtype/IDCard.jpg_")));
		}
	}
}

//OCR类型识别：银行卡  [Credit Card]
TEST_F(LdfcrFixture, KEYWORD_CreditCard_OCR)
{
	string testName = ::testing::UnitTest::GetInstance()->current_test_info()->name();
	if (!JudgeFileJoinTest(testName))
		return;

	dlplog_info(g_log_handle, "[%d] ===========================================TEST_F KEYWORD_CreditCard_OCR is started===========================================", __LINE__);
	std::string completePath = GetCompletePath("/test/data/OCR/o_CreditCard.data");
	dlplog_info(g_log_handle, "[%d] %s", __LINE__, completePath.c_str());

	//if (!operSysJugXP()) // XP版本和Linux32位不支持OCR第三方库
	{
		if (!operSysJugLinux32Bit())
		{
			EXPECT_EQ(ldfcr.init(_T("/test/data/OCR/o_CreditCard.data")), 0);
			EXPECT_TRUE(ldfcr.detect(_T("test/OCRtype/CreditCard.jpg_")));
		}
	}
}

// OCR类型识别：营业执照  [Business License]
TEST_F(LdfcrFixture, KEYWORD_BusiLicense_OCR)
{
	string testName = ::testing::UnitTest::GetInstance()->current_test_info()->name();
	if (!JudgeFileJoinTest(testName))
		return;

	dlplog_info(g_log_handle, "[%d] ===========================================TEST_F KEYWORD_BusiLicense_OCR is started===========================================", __LINE__);
	std::string completePath = GetCompletePath("/test/data/OCR/o_BusiLicense.data");
	dlplog_info(g_log_handle, "[%d] %s", __LINE__, completePath.c_str());

	//if (!operSysJugXP()) // XP版本和Linux32位不支持OCR第三方库
	{
		if (!operSysJugLinux32Bit())
		{
			EXPECT_EQ(ldfcr.init(_T("/test/data/OCR/o_BusiLicense.data")), 0);
			EXPECT_TRUE(ldfcr.detect(_T("test/OCRtype/BusinessLicense.jpg_")));
		}
	}
}

// OCR模式分类：印章  [Stamp] <model=11>
TEST_F(LdfcrFixture, STAMP_Model_OCR)
{
	string testName = ::testing::UnitTest::GetInstance()->current_test_info()->name();
	if (!JudgeFileJoinTest(testName))
		return;

	dlplog_info(g_log_handle, "[%d] ===========================================TEST_F STAMP_Model_OCR is started===========================================", __LINE__);
	std::string completePath = GetCompletePath("/test/data/OCR_Model/Stamp_Model11.data");
	dlplog_info(g_log_handle, "[%d] %s", __LINE__, completePath.c_str());

	//if (!operSysJugXP()) // XP版本和Linux32位不支持OCR第三方库
	{
		if (!operSysJugLinux32Bit())
		{
			EXPECT_EQ(ldfcr.init(_T("/test/data/OCR_Model/Stamp_Model11.data")), 0);
			EXPECT_TRUE(ldfcr.detect(_T("test/滴滴电子发票（椭圆形印章）.docx")));
			EXPECT_FALSE(ldfcr.detect(_T("test/OCRtype/IDCard.jpg_")));//身份证上有国徽
			EXPECT_FALSE(ldfcr.detect(_T("test/OCRtype/CreditCard.jpg_")));
			EXPECT_TRUE(ldfcr.detect(_T("test/OCRtype/BusinessLicense.jpg_")));//营业执照有印章
		}
	}
}

// OCR模式分类：身份证  [IdCard] <model=12>
TEST_F(LdfcrFixture, IdCard_Model_OCR)
{
	string testName = ::testing::UnitTest::GetInstance()->current_test_info()->name();
	if (!JudgeFileJoinTest(testName))
		return;

	dlplog_info(g_log_handle, "[%d] ===========================================TEST_F IdCard_Model_OCR is started===========================================", __LINE__);
	std::string completePath = GetCompletePath("/test/data/OCR_Model/IdCard_Model12.data");
	dlplog_info(g_log_handle, "[%d] %s", __LINE__, completePath.c_str());

	//if (!operSysJugXP()) // XP版本和Linux32位不支持OCR第三方库
	{
		if (!operSysJugLinux32Bit())
		{
			EXPECT_EQ(ldfcr.init(_T("/test/data/OCR_Model/IdCard_Model12.data")), 0);
			EXPECT_TRUE(ldfcr.detect(_T("test/OCRtype/IDCard.jpg_")));
			EXPECT_FALSE(ldfcr.detect(_T("test/滴滴电子发票（椭圆形印章）.docx")));
			EXPECT_FALSE(ldfcr.detect(_T("test/OCRtype/CreditCard.jpg_")));
			EXPECT_FALSE(ldfcr.detect(_T("test/OCRtype/BusinessLicense.jpg_")));
		}
	}
}

// OCR模式分类：银行卡  [Credit Card] <model=13>
TEST_F(LdfcrFixture, CreditCard_Model_OCR)
{
	string testName = ::testing::UnitTest::GetInstance()->current_test_info()->name();
	if (!JudgeFileJoinTest(testName))
		return;

	dlplog_info(g_log_handle, "[%d] ===========================================TEST_F CreditCard_Model_OCR is started===========================================", __LINE__);
	std::string completePath = GetCompletePath("/test/data/OCR_Model/CreditCard_Model13.data");
	dlplog_info(g_log_handle, "[%d] %s", __LINE__, completePath.c_str());

	//if (!operSysJugXP()) // XP版本和Linux32位不支持OCR第三方库
	{
		if (!operSysJugLinux32Bit())
		{
			EXPECT_EQ(ldfcr.init(_T("/test/data/OCR_Model/CreditCard_Model13.data")), 0);
			EXPECT_TRUE(ldfcr.detect(_T("test/OCRtype/CreditCard.jpg_")));
			EXPECT_FALSE(ldfcr.detect(_T("test/OCRtype/IDCard.jpg_")));
			EXPECT_FALSE(ldfcr.detect(_T("test/滴滴电子发票（椭圆形印章）.docx")));
			EXPECT_FALSE(ldfcr.detect(_T("test/OCRtype/BusinessLicense.jpg_")));
		}
	}
}

// OCR模式分类：营业执照  [Business License] <model=14>
TEST_F(LdfcrFixture, Business_License_Model_OCR)
{
	string testName = ::testing::UnitTest::GetInstance()->current_test_info()->name();
	if (!JudgeFileJoinTest(testName))
		return;

	dlplog_info(g_log_handle, "[%d] ===========================================TEST_F Business_License_Model_OCR is started===========================================", __LINE__);
	std::string completePath = GetCompletePath("/test/data/OCR_Model/BusiLicense_Model14.data");
	dlplog_info(g_log_handle, "[%d] %s", __LINE__, completePath.c_str());

	//if (!operSysJugXP()) // XP版本和Linux32位不支持OCR第三方库
	{
		if (!operSysJugLinux32Bit())
		{
			EXPECT_EQ(ldfcr.init(_T("/test/data/OCR_Model/BusiLicense_Model14.data")), 0);
			EXPECT_TRUE(ldfcr.detect(_T("test/OCRtype/BusinessLicense.jpg_")));
			EXPECT_FALSE(ldfcr.detect(_T("test/OCRtype/CrediCard.jpg_")));
			EXPECT_FALSE(ldfcr.detect(_T("test/OCRtype/IDCard.jpg_")));
			EXPECT_FALSE(ldfcr.detect(_T("test/滴滴电子发票（椭圆形印章）.docx")));
		}
	}
}

// OCR长图识别
TEST_F(LdfcrFixture, LongImage_Stamp_OCR)
{
	string testName = ::testing::UnitTest::GetInstance()->current_test_info()->name();
	if (!JudgeFileJoinTest(testName))
		return;

	dlplog_info(g_log_handle, "[%d] ===========================================TEST_F LongImage_Stamp_OCR is started===========================================", __LINE__);
	std::string completePath = GetCompletePath("/test/data/s_stamp_one.data");
	dlplog_info(g_log_handle, "[%d] %s", __LINE__, completePath.c_str());

	if (!operSysJugLinux32Bit())
	{
		EXPECT_EQ(ldfcr.init(_T("test/data/s_stamp_one.data")), 0);
		EXPECT_TRUE(ldfcr.detect(_T("test/LongImageStamp.jpg")));
	}
}

// svg类型图片识别
TEST_F(LdfcrFixture, SVG_ImageType_OCR)
{
	string testName = ::testing::UnitTest::GetInstance()->current_test_info()->name();
	if (!JudgeFileJoinTest(testName))
		return;

	dlplog_info(g_log_handle, "[%d] ===========================================TEST_F SVG_ImageType_OCR is started===========================================", __LINE__);
	std::string completePath = GetCompletePath("/test/data/SuffixRecognition/Imagefiles.data");
	dlplog_info(g_log_handle, "[%d] %s", __LINE__, completePath.c_str());

	if (!operSysJugLinux32Bit())
	{
		EXPECT_EQ(ldfcr.init(_T("/test/data/SuffixRecognition/Imagefiles.data")), 0);
		EXPECT_TRUE(ldfcr.detect(_T("test/MoreEasy.svg")));
	}
}

//高亮关键字
TEST_F(LdfcrFixture, KEYWORD_hightLightShiftPos)
{
	string testName = ::testing::UnitTest::GetInstance()->current_test_info()->name();
	if (!JudgeFileJoinTest(testName))
		return;

	dlplog_info(g_log_handle, "[%d] ===========================================TEST_F KEYWORD_hightLightShiftPos is started===========================================", __LINE__);
	std::string completePath = GetCompletePath("/test/data/s_tuzhang.data");
	dlplog_info(g_log_handle, "[%d] %s", __LINE__, completePath.c_str());

	EXPECT_EQ(ldfcr.init(_T("test/data/mutiple.data")), 0);

	IFCRResult *result = NULL;
	BOOL sensitive;
	EXPECT_TRUE(sensitive = ldfcr.detect(_T("test/mutiple_bom.txt"), (void**)&result,NULL,0,NULL,Ldfcr::_enTcrOpen::CLOSE));
	EXPECT_TRUE(ldfcr.checkShiftPos2(result, sensitive, 588, 559));
	CLEAN_COM(result);
}

// 多敏感文本测试
TEST_F(LdfcrFixture, hightTripleLightPos) 
{
	string testName = ::testing::UnitTest::GetInstance()->current_test_info()->name();
	if (!JudgeFileJoinTest(testName))
		return;

	dlplog_info(g_log_handle, "[%d] ===========================================TEST_F hightTripleLightPos is started===========================================", __LINE__);
	std::string completePath = GetCompletePath("/test/tel/s_triple.data");//********
	dlplog_info(g_log_handle, "[%d] %s", __LINE__, completePath.c_str());

	EXPECT_EQ(ldfcr.init(_T("test/tel/s_triple.data")), 0);

	IFCRResult *result = NULL;
	BOOL sensitive;
	EXPECT_TRUE(sensitive = ldfcr.detect(_T("test/钢铁侠通讯协议.txt"), (void**)&result, NULL, 0, NULL, Ldfcr::_enTcrOpen::CLOSE));
	EXPECT_TRUE(ldfcr.checkTripleShiftPos(result, sensitive, 565, 1492, 208));
	CLEAN_COM(result);
	//EXPECT_TRUE(ldfcr.checkTripleShiftPos(_T("test/钢铁侠通讯协议.txt"), 565, 1492, 208)); //多策略状态首先匹配到的是test，原本的报错匹配的是 哈哈哈 565 1492 208
}

// 身份证多匹配测试
TEST_F(LdfcrFixture, SFZCchekTest) 
{
	string testName = ::testing::UnitTest::GetInstance()->current_test_info()->name();
	if (!JudgeFileJoinTest(testName))
		return;

	dlplog_info(g_log_handle, "[%d] ===========================================TEST_F SFZCchekTest is started===========================================", __LINE__);
	std::string completePath = GetCompletePath("/test/data/SFZ.data");//********
	dlplog_info(g_log_handle, "[%d] %s", __LINE__, completePath.c_str());

	EXPECT_EQ(ldfcr.init(_T("test/data/SFZ.data")), 0);
	EXPECT_TRUE(ldfcr.detect(_T("test/SFZ.txt"))); //多策略状态首先匹配到的是test，原本的报错匹配的是 哈哈哈
}

// 身份证精准匹配测试 2024 5 15
TEST_F(LdfcrFixture, SFZAccurateChekTest) 
{
	string testName = ::testing::UnitTest::GetInstance()->current_test_info()->name();
	if (!JudgeFileJoinTest(testName))
		return;

	dlplog_info(g_log_handle, "[%d] ===========================================TEST_F SFZAccurateChekTest is started===========================================", __LINE__);
	std::string completePath = GetCompletePath("/test/data/SFZAccurate.data");//2024515
	dlplog_info(g_log_handle, "[%d] %s", __LINE__, completePath.c_str());

	// 第一个文件为原SFZ号 犯罪嫌疑人 吴志强 350524198806015610 福建省晋江市
	// 第二个文件为校验码失败文件 第三个文件为时间检验失败文件 第四个文件为归属地不在bin表文件
	EXPECT_EQ(ldfcr.init(_T("test/data/SFZAccurate.data")), 0);
	EXPECT_TRUE(ldfcr.detect(_T("test/SFZ/SFZ1.txt")));
	EXPECT_FALSE(ldfcr.detect(_T("test/SFZ/SFZ2.txt")));
	EXPECT_FALSE(ldfcr.detect(_T("test/SFZ/SFZ3.txt")));
	EXPECT_FALSE(ldfcr.detect(_T("test/SFZ/SFZ4.txt")));
}

// 去重匹配测试 2024 11 1
TEST_F(LdfcrFixture, uniqueMatchCheckTest)
{
	string testName = ::testing::UnitTest::GetInstance()->current_test_info()->name();
	if (!JudgeFileJoinTest(testName))
		return;

	dlplog_info(g_log_handle, "[%d] ===========================================TEST_F uniqueMatchCheckTest is started===========================================", __LINE__);

	// 1 匹配次数为1 开启去重 分别匹配两次同个文件 -> 都true 
	// 2 匹配次数为2 开启去重 同个文件中出现同一个银行卡两次 -> false 分别匹配两次出现同一个银行卡的文件 -> false 不相关导致匹配次数不够 毕竟不是零星或者计数那种 匹配一个文件存在两个不同的银行卡 -> true
	//			   	 关闭去重 同个文件中出现同一个银行卡两次 -> true 
	// 2 匹配次数为3 开启去重 同个文件中出现同一个银行卡三次 -> false 同个文件中出现一个银行卡一次 一个银行卡两次 -> false 同个文件中出现不同的三个银行卡 -> true 
	//			   	 关闭去重 同个文件中出现同一个银行卡三次 -> true  同个文件中出现一个银行卡一次 一个银行卡两次 -> true
	// 1
	std::string completePath = GetCompletePath("/test/data/uniqueMatchTimes1.data");//2024111
	dlplog_info(g_log_handle, "[%d] %s", __LINE__, completePath.c_str());
	EXPECT_EQ(ldfcr.init(_T("test/uniqueMatch/uniqueMatchTimes1.data")), 0);
	EXPECT_TRUE(ldfcr.detect(_T("test/uniqueMatch/uniqueMatchSameTimes1.txt")));
	EXPECT_TRUE(ldfcr.detect(_T("test/uniqueMatch/uniqueMatchSameTimes1.txt")));

	// 2
	completePath = GetCompletePath("/test/data/uniqueMatchTimes2.data");//2024111
	dlplog_info(g_log_handle, "[%d] %s", __LINE__, completePath.c_str());
	EXPECT_EQ(ldfcr.init(_T("test/uniqueMatch/uniqueMatchTimes2.data")), 0);
	EXPECT_FALSE(ldfcr.detect(_T("test/uniqueMatch/uniqueMatchSameTimes2.txt")));
	EXPECT_FALSE(ldfcr.detect(_T("test/uniqueMatch/uniqueMatchSameTimes1.txt")));
	EXPECT_FALSE(ldfcr.detect(_T("test/uniqueMatch/uniqueMatchSameTimes1.txt")));
	EXPECT_TRUE(ldfcr.detect(_T("test/uniqueMatch/uniqueMatchDiffTimes2.txt")));

	completePath = GetCompletePath("/test/data/uniqueCloseMatchTimes2.data");//2024111
	dlplog_info(g_log_handle, "[%d] %s", __LINE__, completePath.c_str());
	EXPECT_EQ(ldfcr.init(_T("test/uniqueMatch/uniqueCloseMatchTimes2.data")), 0);
	EXPECT_TRUE(ldfcr.detect(_T("test/uniqueMatch/uniqueMatchSameTimes2.txt")));


	// 3
	EXPECT_EQ(ldfcr.init(_T("test/uniqueMatch/uniqueMatchTimes3.data")), 0);
	completePath = GetCompletePath("/test/data/uniqueMatchTimes3.data");//2024111
	dlplog_info(g_log_handle, "[%d] %s", __LINE__, completePath.c_str());
	EXPECT_FALSE(ldfcr.detect(_T("test/uniqueMatch/uniqueMatchSameTimes3.txt")));
	EXPECT_FALSE(ldfcr.detect(_T("test/uniqueMatch/uniqueMatchSameTimes12.txt")));
	EXPECT_TRUE(ldfcr.detect(_T("test/uniqueMatch/uniqueMatchDiffTimes3.txt")));

	completePath = GetCompletePath("/test/data/uniqueCloseMatchTimes3.data");//2024111
	dlplog_info(g_log_handle, "[%d] %s", __LINE__, completePath.c_str());
	EXPECT_EQ(ldfcr.init(_T("test/uniqueMatch/uniqueCloseMatchTimes3.data")), 0);
	EXPECT_TRUE(ldfcr.detect(_T("test/uniqueMatch/uniqueMatchSameTimes3.txt")));
	EXPECT_TRUE(ldfcr.detect(_T("test/uniqueMatch/uniqueMatchSameTimes12.txt")));
}

//银行卡号正则表达式精准匹配功能测试 2024 3 11
TEST_F(LdfcrFixture, BankAccurateChekTest) 
{
	string testName = ::testing::UnitTest::GetInstance()->current_test_info()->name();
	if (!JudgeFileJoinTest(testName))
		return;

	dlplog_info(g_log_handle, "[%d] ===========================================TEST_F BankAccurateChekTest is started===========================================", __LINE__);
	std::string completePath = GetCompletePath("/test/data/BankCardAccurate.data");
	dlplog_info(g_log_handle, "[%d] %s", __LINE__, completePath.c_str());

	// 根据规则第一个文件有一条可匹配其他全是错误项仍应认为为可匹配文件
	//二 三作为可匹配规则文件 后面分别是 因为不符合正则表达式出错(a) 不符合校验位出错(b) 不符合bin表出错(c)
	EXPECT_EQ(ldfcr.init(_T("test/data/BankCardAccurate.data")), 0);
	EXPECT_TRUE(ldfcr.detect(_T("test/BCards/BCCards.txt"), nullptr, nullptr));
	EXPECT_TRUE(ldfcr.detect(_T("test/BCards/BCCards1.txt"), nullptr, nullptr));
	EXPECT_TRUE(ldfcr.detect(_T("test/BCards/BCCards2.txt"), nullptr, nullptr));
	EXPECT_FALSE(ldfcr.detect(_T("test/BCards/BCCards3.txt"), nullptr, nullptr));//不符合正则表达式（正确银行卡号前或后有数字）
	EXPECT_FALSE(ldfcr.detect(_T("test/BCards/BCCards4.txt"), nullptr, nullptr));//bin对，luhn校验码错
	EXPECT_FALSE(ldfcr.detect(_T("test/BCards/BCCards5.txt"), nullptr, nullptr));//bin错，luhn效验码对
}

// 2024 7 30 多精准匹配存在导致的匹配失效问题
TEST_F(LdfcrFixture, MultiAccurateChekTest) 
{
	string testName = ::testing::UnitTest::GetInstance()->current_test_info()->name();
	if (!JudgeFileJoinTest(testName))
		return;

	dlplog_info(g_log_handle, "[%d] ===========================================TEST_F MultiAccurateChekTest is started===========================================", __LINE__);
	std::string completePath = GetCompletePath("/test/data/MultiAccurate.data");
	dlplog_info(g_log_handle, "[%d] %s", __LINE__, completePath.c_str());

	EXPECT_EQ(ldfcr.init(_T("test/data/MultiAccurate.data")), 0);
	EXPECT_TRUE(ldfcr.detect(_T("test/MultiAccurateTest.txt"), nullptr, nullptr));
}

// 6703 零星检测 checktimes ！= 1 时，零星检测词组被普通检测模式的checktimes限制了触发次数，理应每次检测自动加1但是因为checktimes没有达成条件因此没有增加匹配次数
TEST_F(LdfcrFixture, countThres_CheckTimes_Tset) 
{
	string testName = ::testing::UnitTest::GetInstance()->current_test_info()->name();
	if (!JudgeFileJoinTest(testName))
		return;

	dlplog_info(g_log_handle, "[%d] ===========================================TEST_F countThres_CheckTimes_Tset is started===========================================", __LINE__);
	std::string completePath = GetCompletePath("/test/data/strategy.data_chushibiao");//********
	dlplog_info(g_log_handle, "[%d] %s", __LINE__, completePath.c_str());

	EXPECT_EQ(ldfcr.init(_T("test/data/strategy.data_chushibiao")), 0);
	for (auto i = 1; i <= 100; i++)
	{
		if (i % 5 == 0)
		{
			EXPECT_TRUE(ldfcr.detect(_T("test/CSB.txt"), nullptr, nullptr));
		}
		else
			EXPECT_FALSE(ldfcr.detect(_T("test/CSB.txt"), nullptr, nullptr));
	}
}

#ifdef WIN32
// 加密文本应返回 -4 超过500MB应返回-3 2023 4 4 更新 返回-7
TEST_F(LdfcrFixture, encryption_Check) 
{
	string testName = ::testing::UnitTest::GetInstance()->current_test_info()->name();
	if (!JudgeFileJoinTest(testName))
		return;

	dlplog_info(g_log_handle, "[%d] ===========================================TEST_F encryption_Check is started===========================================", __LINE__);
	std::string completePath = GetCompletePath("/test/data/strategy.data");//********
	dlplog_info(g_log_handle, "[%d] %s", __LINE__, completePath.c_str());

	EXPECT_EQ(ldfcr.init(_T("test/data/strategy.data")), 0);
	IFCRResult *result = NULL;
	EXPECT_TRUE(ldfcr.detect(_T("test/encryptionTest.rar"),(void**)&result));
	EXPECT_TRUE(ldfcr.checkErCode(result, -7));
	CLEAN_COM(result);
	//int i = ldfcr.checkErCode(_T("test/76973"), nullptr, 0);
}

// 加密文本应返回 -4 超过500MB应返回-3
TEST_F(LdfcrFixture, overSize500MB)
{
	string testName = ::testing::UnitTest::GetInstance()->current_test_info()->name();
	if (!JudgeFileJoinTest(testName))
		return;

	dlplog_info(g_log_handle, "[%d] ===========================================TEST_F overSize500MB is started===========================================", __LINE__);
	std::string completePath = GetCompletePath("/test/data/strategy.data");//********
	dlplog_info(g_log_handle, "[%d] %s", __LINE__, completePath.c_str());

	EXPECT_EQ(ldfcr.init(_T("test/data/strategy.data")), 0);
	//创建501mb文件
	EXPECT_EQ(createMBEmptyFile(_T("test/empty501mb.txt"),501), 1);
	IFCRResult *result = NULL;
	EXPECT_FALSE(ldfcr.detect(_T("test/empty501mb.txt"), (void**)&result, NULL, 0,NULL, Ldfcr::_enTcrOpen::CLOSE));
	EXPECT_TRUE(ldfcr.checkErCode(result, -3));
	CLEAN_COM(result);
	//程序结束清空
	EXPECT_EQ(deleteEmptyFile(_T("test/empty501mb.txt")), 1);
}

// 2023 4 14 新增文件属性 >500MB文件触发超时中断，但是文件属性应该敏感匹配上 
TEST_F(LdfcrFixture, overSize500MB_FileProperties) 
{
	string testName = ::testing::UnitTest::GetInstance()->current_test_info()->name();
	if (!JudgeFileJoinTest(testName))
		return;

	dlplog_info(g_log_handle, "[%d] ===========================================TEST_F overSize500MB_FileProperties is started===========================================", __LINE__);
	std::string completePath = GetCompletePath("/test/data/File_PropertiesME501MB.data");//********
	dlplog_info(g_log_handle, "[%d] %s", __LINE__, completePath.c_str());

	//Sleep(15000);
	EXPECT_EQ(ldfcr.init(_T("test/data/File_PropertiesME501MB.data")), 0);
	//创建501mb文件
	EXPECT_EQ(createMBEmptyFile(_T("test/501MB.docx"),501), 1); //文件后缀虽然是docx但是这种创建方式还是txt文件
	EXPECT_TRUE(ldfcr.detect(_T("test/501MB.docx"), nullptr, nullptr));
	//程序结束清空
	EXPECT_EQ(deleteEmptyFile(_T("test/501MB.docx")), 1);
	//std::cout << i << std::endl;
}
#endif

// 2023 4 13 4月12日文本识别出现异常
TEST_F(LdfcrFixture, PDFmod_time) 
{
	string testName = ::testing::UnitTest::GetInstance()->current_test_info()->name();
	if (!JudgeFileJoinTest(testName))
		return;

	dlplog_info(g_log_handle, "[%d] ===========================================TEST_F PDFmod_time is started===========================================", __LINE__);
	std::string completePath = GetCompletePath("/test/data/mod_time.data");//********
	dlplog_info(g_log_handle, "[%d] %s", __LINE__, completePath.c_str());

	EXPECT_EQ(ldfcr.init(_T("test/data/mod_time.data")), 0);
	EXPECT_TRUE(ldfcr.detect(_T("test/PDF_mod_time.pdf"), nullptr, nullptr));
}

//2023 4 18 文本识别敏感测试
TEST_F(LdfcrFixture, TxtFilterTest) 
{
	string testName = ::testing::UnitTest::GetInstance()->current_test_info()->name();
	if (!JudgeFileJoinTest(testName))
		return;

	dlplog_info(g_log_handle, "[%d] ===========================================TEST_F TxtFilterTest is started===========================================", __LINE__);
	std::string completePath = GetCompletePath("/test/data/OCR_TxtFilterTest.data");//********
	dlplog_info(g_log_handle, "[%d] %s", __LINE__, completePath.c_str());

	EXPECT_EQ(ldfcr.init(_T("test/data/OCR_TxtFilterTest.data")), 0);
	EXPECT_FALSE(ldfcr.detect(_T("test/俄雇佣兵将领罕见公开呼吁普京停火.SLDPRT"), nullptr, nullptr)); //OLE文本敏感测试 OLE复合文档  2023 8 14 SLDPRT类型无法提取
	EXPECT_TRUE(ldfcr.detect(_T("test/俄雇佣兵将领罕见公开呼吁普京停火.xml"), nullptr, nullptr)); //XML文本敏感测试
	EXPECT_TRUE(ldfcr.detect(_T("test/俄雇佣兵将领罕见公开呼吁普京停火.rtf"), nullptr, nullptr)); //RTF文本敏感测试
	EXPECT_TRUE(ldfcr.detect(_T("test/俄雇佣兵将领罕见公开呼吁普京停火.com"), nullptr, nullptr)); //ComOle文本敏感测试
	EXPECT_FALSE(ldfcr.detect(_T("test/俄雇佣兵将领罕见公开呼吁普京停火.dwg"), nullptr, nullptr)); //dwg文本敏感测试   2023 8 14 dwg类型无法提取
	EXPECT_TRUE(ldfcr.detect(_T("test/俄雇佣兵将领罕见公开呼吁普京停火.rar"), nullptr, nullptr)); //压缩文本敏感测试
	EXPECT_TRUE(ldfcr.detect(_T("test/俄雇佣兵将领罕见公开呼吁普京停火.pdf"), nullptr, nullptr)); //pdf文本敏感测试
}

//======================================================================================2023 4 18 文件属性测试============================================================================================

// 2023 4 14 新增文件属性作者测试 作者来自创建者并不是小说作者，不懂可以用文本提取工具测试 txt PDFLIB生成 无作者 PDBLIB应该可以设置作者
TEST_F(LdfcrFixture, AuthorRegularTest) 
{
	string testName = ::testing::UnitTest::GetInstance()->current_test_info()->name();
	if (!JudgeFileJoinTest(testName))
		return;

	dlplog_info(g_log_handle, "[%d] ===========================================TEST_F AuthorRegularTest is started===========================================", __LINE__);
	std::string completePath = GetCompletePath("/test/data/author.data");//********
	dlplog_info(g_log_handle, "[%d] %s", __LINE__, completePath.c_str());

	EXPECT_EQ(ldfcr.init(_T("test/data/author.data")), 0);
	EXPECT_TRUE(ldfcr.detect(_T("test/俄雇佣兵将领罕见公开呼吁普京停火.doc"), nullptr, nullptr));
}

//DOC
TEST_F(LdfcrFixture, DOC_File_Attribute) 
{
	string testName = ::testing::UnitTest::GetInstance()->current_test_info()->name();
	if (!JudgeFileJoinTest(testName))
		return;

	dlplog_info(g_log_handle, "[%d] ===========================================TEST_F DOC_File_Attribute is started===========================================", __LINE__);
	std::string completePath = GetCompletePath("/test/data/file attribute/DOCFile_Attribute.data");//********
	dlplog_info(g_log_handle, "[%d] %s", __LINE__, completePath.c_str());

	EXPECT_EQ(ldfcr.init(_T("test/data/file attribute/DOCFile_Attribute.data")), 0);
	EXPECT_TRUE(ldfcr.detect(_T("test/俄雇佣兵将领罕见公开呼吁普京停火.doc"), nullptr, nullptr)); //DOC文本敏感测试 
}

//OLE
TEST_F(LdfcrFixture, OLE_File_Attribute) 
{
	string testName = ::testing::UnitTest::GetInstance()->current_test_info()->name();
	if (!JudgeFileJoinTest(testName))
		return;

	dlplog_info(g_log_handle, "[%d] ===========================================TEST_F OLE_File_Attribute is started===========================================", __LINE__);
	std::string completePath = GetCompletePath("/test/data/file attribute/OLEFile_Attribute.data");//********
	dlplog_info(g_log_handle, "[%d] %s", __LINE__, completePath.c_str());

	EXPECT_EQ(ldfcr.init(_T("test/data/file attribute/OLEFile_Attribute.data")), 0);
	EXPECT_TRUE(ldfcr.detect(_T("test/俄雇佣兵将领罕见公开呼吁普京停火.SLDPRT"), nullptr, nullptr)); //OLE文本敏感测试 OLE复合文档
}

//RTF
TEST_F(LdfcrFixture, RTF_File_Attribute) 
{
	string testName = ::testing::UnitTest::GetInstance()->current_test_info()->name();
	if (!JudgeFileJoinTest(testName))
		return;

	dlplog_info(g_log_handle, "[%d] ===========================================TEST_F RTF_File_Attribute is started===========================================", __LINE__);
	std::string completePath = GetCompletePath("/test/data/file attribute/RTFFile_Attribute.data");//********
	dlplog_info(g_log_handle, "[%d] %s", __LINE__, completePath.c_str());

	EXPECT_EQ(ldfcr.init(_T("test/data/file attribute/RTFFile_Attribute.data")), 0);
	EXPECT_TRUE(ldfcr.detect(_T("test/俄雇佣兵将领罕见公开呼吁普京停火.rtf"), nullptr, nullptr)); //RTF文本敏感测试 
}

//DWG
TEST_F(LdfcrFixture, CAD_File_Attribute)
{
	string testName = ::testing::UnitTest::GetInstance()->current_test_info()->name();
	if (!JudgeFileJoinTest(testName))
		return;

	dlplog_info(g_log_handle, "[%d] ===========================================TEST_F CAD_File_Attribute is started===========================================", __LINE__);
	std::string completePath = GetCompletePath("/test/data/file attribute/CADFile_Attribute.data");//********
	dlplog_info(g_log_handle, "[%d] %s", __LINE__, completePath.c_str());

	EXPECT_EQ(ldfcr.init(_T("test/data/file attribute/CADFile_Attribute.data")), 0);
	EXPECT_TRUE(ldfcr.detect(_T("test/俄雇佣兵将领罕见公开呼吁普京停火.dwg"), nullptr, nullptr)); //CAD文本敏感测试 
}

//RAR
TEST_F(LdfcrFixture, RAR_File_Attribute) 
{
	string testName = ::testing::UnitTest::GetInstance()->current_test_info()->name();
	if (!JudgeFileJoinTest(testName))
		return;

	dlplog_info(g_log_handle, "[%d] ===========================================TEST_F RAR_File_Attribute is started===========================================", __LINE__);
	std::string completePath = GetCompletePath("/test/data/file attribute/RARFile_Attribute.data");//********
	dlplog_info(g_log_handle, "[%d] %s", __LINE__, completePath.c_str());

	EXPECT_EQ(ldfcr.init(_T("test/data/file attribute/RARFile_Attribute.data")), 0);
	EXPECT_TRUE(ldfcr.detect(_T("test/俄雇佣兵将领罕见公开呼吁普京停火.rar"), nullptr, nullptr)); //RAR文本敏感测试
}

//XML
TEST_F(LdfcrFixture, XML_File_Attribute)
{
	string testName = ::testing::UnitTest::GetInstance()->current_test_info()->name();
	if (!JudgeFileJoinTest(testName))
		return;

	dlplog_info(g_log_handle, "[%d] ===========================================TEST_F XML_File_Attribute is started===========================================", __LINE__);
	std::string completePath = GetCompletePath("/test/data/file attribute/XMLFile_Attribute.data");//********
	dlplog_info(g_log_handle, "[%d] %s", __LINE__, completePath.c_str());

	EXPECT_EQ(ldfcr.init(_T("test/data/file attribute/XMLFile_Attribute.data")), 0);
	EXPECT_TRUE(ldfcr.detect(_T("test/俄雇佣兵将领罕见公开呼吁普京停火.xml"), nullptr, nullptr)); //XML文本敏感测试
}

//PDF
TEST_F(LdfcrFixture, PDF_File_Attribute) 
{
	string testName = ::testing::UnitTest::GetInstance()->current_test_info()->name();
	if (!JudgeFileJoinTest(testName))
		return;

	dlplog_info(g_log_handle, "[%d] ===========================================TEST_F PDF_File_Attribute is started===========================================", __LINE__);
	std::string completePath = GetCompletePath("/test/data/file attribute/PDFFile_Attribute.data");//********
	dlplog_info(g_log_handle, "[%d] %s", __LINE__, completePath.c_str());

	EXPECT_EQ(ldfcr.init(_T("test/data/file attribute/PDFFile_Attribute.data")), 0);
	EXPECT_TRUE(ldfcr.detect(_T("test/俄雇佣兵将领罕见公开呼吁普京停火.pdf"), nullptr, nullptr)); //PDF文本敏感测试
}

//======================================================================================2023 11 20 文件属性测试============================================================================================

//文件属性 多文件作者测试
TEST_F(LdfcrFixture, Multi_AuthorRegularTest)
{
	string testName = ::testing::UnitTest::GetInstance()->current_test_info()->name();
	if (!JudgeFileJoinTest(testName))
		return;

	dlplog_info(g_log_handle, "[%d] ===========================================TEST_F Multi_AuthorRegularTest is started===========================================", __LINE__);
	std::string completePath = GetCompletePath("/test/data/Multi_author.data");
	dlplog_info(g_log_handle, "[%d] %s", __LINE__, completePath.c_str());

	EXPECT_EQ(ldfcr.init(_T("test/data/Multi_author.data")), 0);
	EXPECT_TRUE(ldfcr.detect(_T("test/Multi_Author.doc"), nullptr, nullptr));

}

//文件属性 文件大小 -> 大于5k，小于100k
TEST_F(LdfcrFixture, FileSizeTest)
{
	string testName = ::testing::UnitTest::GetInstance()->current_test_info()->name();
	if (!JudgeFileJoinTest(testName))
		return;

	dlplog_info(g_log_handle, "[%d] ===========================================TEST_F FileSizeTest is started===========================================", __LINE__);
	std::string completePath = GetCompletePath("/test/data/filesize.data");
	dlplog_info(g_log_handle, "[%d] %s", __LINE__, completePath.c_str());

	EXPECT_EQ(ldfcr.init(_T("test/data/filesize.data")), 0);
	EXPECT_TRUE(ldfcr.detect(_T("test/6k.txt"), nullptr, nullptr,0,NULL,Ldfcr::_enTcrOpen::CLOSE));
	EXPECT_FALSE(ldfcr.detect(_T("test/4k.txt"), nullptr, nullptr, 0, NULL, Ldfcr::_enTcrOpen::CLOSE));
}

//屏蔽说明：测试能跑过，但因文件创建时间和修改时间每次版本更新换代，导致测试文件hahaha.txt时间发生改变，故暂时屏蔽起来；

////文件属性 文件创建时间：2023 1 1-2023 12 31    
//TEST_F(LdfcrFixture, FileCreateTimeTest)
//{
//	dlplog_info(g_log_handle, "[%d] ===========================================TEST_F FileCreateTimeTest is started===========================================", __LINE__);
//	std::string completePath = GetCompletePath("/test/data/2023yeartime.data");
//	dlplog_info(g_log_handle, "[%d] %s", __LINE__, completePath.c_str());
//
//	EXPECT_EQ(ldfcr.init(_T("test/data/2023yeartime.data")), 0);
//	EXPECT_TRUE(ldfcr.detect(_T("test/hahaha.txt"), nullptr, nullptr));
//
//}
//
////文件属性 文件修改时间：2023 1 1-2023 12 31
//TEST_F(LdfcrFixture, FileChangeTimeTest)
//{
//	dlplog_info(g_log_handle, "[%d] ===========================================TEST_F FileChangeTimeTest is started===========================================", __LINE__);
//	std::string completePath = GetCompletePath("/test/data/2023FileChangeTime.data");
//	dlplog_info(g_log_handle, "[%d] %s", __LINE__, completePath.c_str());
//
//	EXPECT_EQ(ldfcr.init(_T("test/data/2023FileChangeTime.data")), 0);
//	EXPECT_TRUE(ldfcr.detect(_T("test/hahaha.txt"), nullptr, nullptr));
//
//}

//文件属性 文件例外条件：doc/docx
TEST_F(LdfcrFixture, ExceptFileTest)
{
	string testName = ::testing::UnitTest::GetInstance()->current_test_info()->name();
	if (!JudgeFileJoinTest(testName))
		return;

	dlplog_info(g_log_handle, "[%d] ===========================================TEST_F ExceptFileTest is started===========================================", __LINE__);
	std::string completePath = GetCompletePath("/test/data/exceptfile.data");
	dlplog_info(g_log_handle, "[%d] %s", __LINE__, completePath.c_str());

	EXPECT_EQ(ldfcr.init(_T("test/data/exceptfile.data")), 0);
	EXPECT_TRUE(ldfcr.detect(_T("test/text.txt"), nullptr, nullptr, 0, NULL, Ldfcr::_enTcrOpen::CLOSE));
	EXPECT_FALSE(ldfcr.detect(_T("test/imageEMBEDOCR.doc"), nullptr, nullptr));
}

//文件属性 例外文件名：hahaha.txt
TEST_F(LdfcrFixture, ExceptFileNameTest)
{
	string testName = ::testing::UnitTest::GetInstance()->current_test_info()->name();
	if (!JudgeFileJoinTest(testName))
		return;

	dlplog_info(g_log_handle, "[%d] ===========================================TEST_F ExceptFileNameTest is started===========================================", __LINE__);
	std::string completePath = GetCompletePath("/test/data/ExceptFileName.data");
	dlplog_info(g_log_handle, "[%d] %s", __LINE__, completePath.c_str());

	EXPECT_EQ(ldfcr.init(_T("test/data/ExceptFileName.data")), 0);
	EXPECT_TRUE(ldfcr.detect(_T("test/text.txt"), nullptr, nullptr, 0, NULL, Ldfcr::_enTcrOpen::CLOSE));
	EXPECT_FALSE(ldfcr.detect(_T("test/hahaha.txt"), nullptr, nullptr));
}

//文件属性 空文件测试
TEST_F(LdfcrFixture, EmptyFileTest)
{
	string testName = ::testing::UnitTest::GetInstance()->current_test_info()->name();
	if (!JudgeFileJoinTest(testName))
		return;

	dlplog_info(g_log_handle, "[%d] ===========================================TEST_F EmptyFileTest is started===========================================", __LINE__);
	std::string completePath = GetCompletePath("/test/data/EmptyFile.data");
	dlplog_info(g_log_handle, "[%d] %s", __LINE__, completePath.c_str());

	EXPECT_EQ(ldfcr.init(_T("test/data/EmptyFile.data")), 0);
	EXPECT_TRUE(ldfcr.detect(_T("test/501MB.docx"), nullptr, nullptr));
	
}

//文件属性 密码保护&后缀测试	doc
TEST_F(LdfcrFixture, PasswordProtectTest_appointFileEXT_doc)
{
	string testName = ::testing::UnitTest::GetInstance()->current_test_info()->name();
	if (!JudgeFileJoinTest(testName))
		return;

	dlplog_info(g_log_handle, "[%d] ===========================================TEST_F PasswordProtectTest_appointFileEXT_doc is started===========================================", __LINE__);
	std::string completePath = GetCompletePath("/test/data/PasswordProtectEXT.data");
	dlplog_info(g_log_handle, "[%d] %s", __LINE__, completePath.c_str());

	if (!operSysJugLinux32Bit())
	{
		EXPECT_EQ(ldfcr.init(_T("test/data/PasswordProtectEXT.data")), 0);
		EXPECT_TRUE(ldfcr.detect(_T("test/EncryptFiles/office2019.doc"), nullptr, nullptr));
	}
}

//文件属性 密码保护&后缀测试	docx
TEST_F(LdfcrFixture, PasswordProtectTest_appointFileEXT_docx)
{
	string testName = ::testing::UnitTest::GetInstance()->current_test_info()->name();
	if (!JudgeFileJoinTest(testName))
		return;

	dlplog_info(g_log_handle, "[%d] ===========================================TEST_F PasswordProtectTest_appointFileEXT_docx is started===========================================", __LINE__);
	std::string completePath = GetCompletePath("/test/data/PasswordProtectEXT.data");
	dlplog_info(g_log_handle, "[%d] %s", __LINE__, completePath.c_str());

	if (!operSysJugLinux32Bit())
	{
		EXPECT_EQ(ldfcr.init(_T("test/data/PasswordProtectEXT.data")), 0);
		EXPECT_TRUE(ldfcr.detect(_T("test/EncryptFiles/office2019.docx"), nullptr, nullptr));
	}
}

//文件属性 密码保护&后缀测试	ppt
TEST_F(LdfcrFixture, PasswordProtectTest_appointFileEXT_ppt)
{
	string testName = ::testing::UnitTest::GetInstance()->current_test_info()->name();
	if (!JudgeFileJoinTest(testName))
		return;

	dlplog_info(g_log_handle, "[%d] ===========================================TEST_F PasswordProtectTest_appointFileEXT_ppt is started===========================================", __LINE__);
	std::string completePath = GetCompletePath("/test/data/PasswordProtectEXT.data");
	dlplog_info(g_log_handle, "[%d] %s", __LINE__, completePath.c_str());

	if (!operSysJugLinux32Bit())
	{
		EXPECT_EQ(ldfcr.init(_T("test/data/PasswordProtectEXT.data")), 0);
		EXPECT_TRUE(ldfcr.detect(_T("test/EncryptFiles/office2019.ppt"), nullptr, nullptr));
	}
}

//文件属性 密码保护&后缀测试	pptx
TEST_F(LdfcrFixture, PasswordProtectTest_appointFileEXT_pptx)
{
	string testName = ::testing::UnitTest::GetInstance()->current_test_info()->name();
	if (!JudgeFileJoinTest(testName))
		return;

	dlplog_info(g_log_handle, "[%d] ===========================================TEST_F PasswordProtectTest_appointFileEXT_pptx is started===========================================", __LINE__);
	std::string completePath = GetCompletePath("/test/data/PasswordProtectEXT.data");
	dlplog_info(g_log_handle, "[%d] %s", __LINE__, completePath.c_str());

	if (!operSysJugLinux32Bit())
	{
		EXPECT_EQ(ldfcr.init(_T("test/data/PasswordProtectEXT.data")), 0);
		EXPECT_TRUE(ldfcr.detect(_T("test/EncryptFiles/office2019.pptx"), nullptr, nullptr));
	}
}

//文件属性 密码保护&后缀测试	xls
TEST_F(LdfcrFixture, PasswordProtectTest_appointFileEXT_xls)
{
	string testName = ::testing::UnitTest::GetInstance()->current_test_info()->name();
	if (!JudgeFileJoinTest(testName))
		return;

	dlplog_info(g_log_handle, "[%d] ===========================================TEST_F PasswordProtectTest_appointFileEXT_xls is started===========================================", __LINE__);
	std::string completePath = GetCompletePath("/test/data/PasswordProtectEXT.data");
	dlplog_info(g_log_handle, "[%d] %s", __LINE__, completePath.c_str());

	if (!operSysJugLinux32Bit())
	{
		EXPECT_EQ(ldfcr.init(_T("test/data/PasswordProtectEXT.data")), 0);
		EXPECT_TRUE(ldfcr.detect(_T("test/EncryptFiles/office2019.xls"), nullptr, nullptr));
	}
}

//文件属性 密码保护&后缀测试	xlsx
TEST_F(LdfcrFixture, PasswordProtectTest_appointFileEXT_xlsx)
{
	string testName = ::testing::UnitTest::GetInstance()->current_test_info()->name();
	if (!JudgeFileJoinTest(testName))
		return;

	dlplog_info(g_log_handle, "[%d] ===========================================TEST_F PasswordProtectTest_appointFileEXT_xlsx is started===========================================", __LINE__);
	std::string completePath = GetCompletePath("/test/data/PasswordProtectEXT.data");
	dlplog_info(g_log_handle, "[%d] %s", __LINE__, completePath.c_str());

	if (!operSysJugLinux32Bit())
	{
		EXPECT_EQ(ldfcr.init(_T("test/data/PasswordProtectEXT.data")), 0);
		EXPECT_TRUE(ldfcr.detect(_T("test/EncryptFiles/office2019.xlsx"), nullptr, nullptr));
	}
}

//文件属性 密码保护&后缀测试	pdf
TEST_F(LdfcrFixture, PasswordProtectTest_appointFileEXT_pdf)
{
	string testName = ::testing::UnitTest::GetInstance()->current_test_info()->name();
	if (!JudgeFileJoinTest(testName))
		return;

	dlplog_info(g_log_handle, "[%d] ===========================================TEST_F PasswordProtectTest_appointFileEXT_pdf is started===========================================", __LINE__);
	std::string completePath = GetCompletePath("/test/data/PasswordProtectEXT.data");
	dlplog_info(g_log_handle, "[%d] %s", __LINE__, completePath.c_str());

	EXPECT_EQ(ldfcr.init(_T("test/data/PasswordProtectEXT.data")), 0);
	EXPECT_TRUE(ldfcr.detect(_T("test/EncryptFiles/role 口令加密test123..pdf"), nullptr, nullptr));
}

//文件属性 密码保护&后缀测试	rar
TEST_F(LdfcrFixture, PasswordProtectTest_appointFileEXT_rar)
{
	string testName = ::testing::UnitTest::GetInstance()->current_test_info()->name();
	if (!JudgeFileJoinTest(testName))
		return;

	dlplog_info(g_log_handle, "[%d] ===========================================TEST_F PasswordProtectTest_appointFileEXT_rar is started===========================================", __LINE__);
	std::string completePath = GetCompletePath("/test/data/PasswordProtectEXT.data");
	dlplog_info(g_log_handle, "[%d] %s", __LINE__, completePath.c_str());

	EXPECT_EQ(ldfcr.init(_T("test/data/PasswordProtectEXT.data")), 0);
	EXPECT_TRUE(ldfcr.detect(_T("test/EncryptFiles/新建 文本文档.rar"), nullptr, nullptr));
}

//文件属性 密码保护测试	doc
TEST_F(LdfcrFixture, PasswordProtectTest_doc)
{
	string testName = ::testing::UnitTest::GetInstance()->current_test_info()->name();
	if (!JudgeFileJoinTest(testName))
		return;

	dlplog_info(g_log_handle, "[%d] ===========================================TEST_F PasswordProtectTest_doc is started===========================================", __LINE__);
	std::string completePath = GetCompletePath("/test/data/PasswordProtect.data");
	dlplog_info(g_log_handle, "[%d] %s", __LINE__, completePath.c_str());

	if (!operSysJugLinux32Bit())
	{
		EXPECT_EQ(ldfcr.init(_T("test/data/PasswordProtect.data")), 0);
		EXPECT_TRUE(ldfcr.detect(_T("test/EncryptFiles/office2019.doc"), nullptr, nullptr));
	}
}

//文件属性 密码保护测试	docx
TEST_F(LdfcrFixture, PasswordProtectTest_docx)
{
	string testName = ::testing::UnitTest::GetInstance()->current_test_info()->name();
	if (!JudgeFileJoinTest(testName))
		return;

	dlplog_info(g_log_handle, "[%d] ===========================================TEST_F PasswordProtectTest_docx is started===========================================", __LINE__);
	std::string completePath = GetCompletePath("/test/data/PasswordProtect.data");
	dlplog_info(g_log_handle, "[%d] %s", __LINE__, completePath.c_str());

	if (!operSysJugLinux32Bit())
	{
		EXPECT_EQ(ldfcr.init(_T("test/data/PasswordProtect.data")), 0);
		EXPECT_TRUE(ldfcr.detect(_T("test/EncryptFiles/office2019.docx"), nullptr, nullptr));
	}
}

//文件属性 密码保护测试	ppt
TEST_F(LdfcrFixture, PasswordProtectTest_ppt)
{
	string testName = ::testing::UnitTest::GetInstance()->current_test_info()->name();
	if (!JudgeFileJoinTest(testName))
		return;

	dlplog_info(g_log_handle, "[%d] ===========================================TEST_F PasswordProtectTest_ppt is started===========================================", __LINE__);
	std::string completePath = GetCompletePath("/test/data/PasswordProtect.data");
	dlplog_info(g_log_handle, "[%d] %s", __LINE__, completePath.c_str());

	if (!operSysJugLinux32Bit())
	{
		EXPECT_EQ(ldfcr.init(_T("test/data/PasswordProtect.data")), 0);
		EXPECT_TRUE(ldfcr.detect(_T("test/EncryptFiles/office2019.ppt"), nullptr, nullptr));
	}
}

//文件属性 密码保护测试	pptx
TEST_F(LdfcrFixture, PasswordProtectTest_pptx)
{
	string testName = ::testing::UnitTest::GetInstance()->current_test_info()->name();
	if (!JudgeFileJoinTest(testName))
		return;

	dlplog_info(g_log_handle, "[%d] ===========================================TEST_F PasswordProtectTest_pptx is started===========================================", __LINE__);
	std::string completePath = GetCompletePath("/test/data/PasswordProtect.data");
	dlplog_info(g_log_handle, "[%d] %s", __LINE__, completePath.c_str());

	if (!operSysJugLinux32Bit())
	{
		EXPECT_EQ(ldfcr.init(_T("test/data/PasswordProtect.data")), 0);
		EXPECT_TRUE(ldfcr.detect(_T("test/EncryptFiles/office2019.pptx"), nullptr, nullptr));
	}
}

//文件属性 密码保护测试	xls
TEST_F(LdfcrFixture, PasswordProtectTest_xls)
{
	string testName = ::testing::UnitTest::GetInstance()->current_test_info()->name();
	if (!JudgeFileJoinTest(testName))
		return;

	dlplog_info(g_log_handle, "[%d] ===========================================TEST_F PasswordProtectTest_xls is started===========================================", __LINE__);
	std::string completePath = GetCompletePath("/test/data/PasswordProtect.data");
	dlplog_info(g_log_handle, "[%d] %s", __LINE__, completePath.c_str());

	if (!operSysJugLinux32Bit())
	{
		EXPECT_EQ(ldfcr.init(_T("test/data/PasswordProtect.data")), 0);
		EXPECT_TRUE(ldfcr.detect(_T("test/EncryptFiles/office2019.xls"), nullptr, nullptr));
	}
}

//文件属性 密码保护测试	xlsx
TEST_F(LdfcrFixture, PasswordProtectTest_xlsx)
{
	string testName = ::testing::UnitTest::GetInstance()->current_test_info()->name();
	if (!JudgeFileJoinTest(testName))
		return;

	dlplog_info(g_log_handle, "[%d] ===========================================TEST_F PasswordProtectTest_xlsx is started===========================================", __LINE__);
	std::string completePath = GetCompletePath("/test/data/PasswordProtect.data");
	dlplog_info(g_log_handle, "[%d] %s", __LINE__, completePath.c_str());

	if (!operSysJugLinux32Bit())
	{
		EXPECT_EQ(ldfcr.init(_T("test/data/PasswordProtect.data")), 0);
		EXPECT_TRUE(ldfcr.detect(_T("test/EncryptFiles/office2019.xlsx"), nullptr, nullptr));
	}
}

//文件属性 密码保护测试	pdf
TEST_F(LdfcrFixture, PasswordProtectTest_pdf)
{
	string testName = ::testing::UnitTest::GetInstance()->current_test_info()->name();
	if (!JudgeFileJoinTest(testName))
		return;

	dlplog_info(g_log_handle, "[%d] ===========================================TEST_F PasswordProtectTest_pdf is started===========================================", __LINE__);
	std::string completePath = GetCompletePath("/test/data/PasswordProtect.data");
	dlplog_info(g_log_handle, "[%d] %s", __LINE__, completePath.c_str());

	EXPECT_EQ(ldfcr.init(_T("test/data/PasswordProtect.data")), 0);
	EXPECT_TRUE(ldfcr.detect(_T("test/EncryptFiles/role 口令加密test123..pdf"), nullptr, nullptr));
}

//文件属性 密码保护测试	rar
TEST_F(LdfcrFixture, PasswordProtectTest_rar)
{
	string testName = ::testing::UnitTest::GetInstance()->current_test_info()->name();
	if (!JudgeFileJoinTest(testName))
		return;

	dlplog_info(g_log_handle, "[%d] ===========================================TEST_F PasswordProtectTest_rar is started===========================================", __LINE__);
	std::string completePath = GetCompletePath("/test/data/PasswordProtect.data");
	dlplog_info(g_log_handle, "[%d] %s", __LINE__, completePath.c_str());
	
	EXPECT_EQ(ldfcr.init(_T("test/data/PasswordProtect.data")), 0);
	EXPECT_TRUE(ldfcr.detect(_T("test/EncryptFiles/新建 文本文档.rar"), nullptr, nullptr));
}

//==========================================================================文件属性-开始测试是否依赖后缀识别====================================================================================

//文件属性1 后缀识别 办公文件
TEST_F(LdfcrFixture, SuffixRecognition_Officefiles)
{
	string testName = ::testing::UnitTest::GetInstance()->current_test_info()->name();
	if (!JudgeFileJoinTest(testName))
		return;

	dlplog_info(g_log_handle, "[%d] ===========================================TEST_F SuffixRecognition_Officefiles is started===========================================", __LINE__);
	std::string completePath = GetCompletePath("/test/data/SuffixRecognition/Officefiles.data");
	dlplog_info(g_log_handle, "[%d] %s", __LINE__, completePath.c_str());

	EXPECT_EQ(ldfcr.init(_T("test/data/SuffixRecognition/Officefiles.data")), 0);
	EXPECT_TRUE(ldfcr.detect(_T("test/Officefiles/Officefiles.doc.errorExt"), nullptr, nullptr));
	EXPECT_TRUE(ldfcr.detect(_T("test/Officefiles/Officefiles.docm.errorExt"), nullptr, nullptr));
	EXPECT_TRUE(ldfcr.detect(_T("test/Officefiles/Officefiles.docx.errorExt"), nullptr, nullptr));
	EXPECT_TRUE(ldfcr.detect(_T("test/Officefiles/Officefiles.dotm.errorExt"), nullptr, nullptr));
	EXPECT_TRUE(ldfcr.detect(_T("test/Officefiles/Officefiles.dotx.errorExt"), nullptr, nullptr));
	EXPECT_TRUE(ldfcr.detect(_T("test/Officefiles/Officefiles.dps.errorExt"), nullptr, nullptr));
	EXPECT_TRUE(ldfcr.detect(_T("test/Officefiles/Officefiles.epub.errorExt"), nullptr, nullptr));
	EXPECT_TRUE(ldfcr.detect(_T("test/Officefiles/Officefiles.et.errorExt"), nullptr, nullptr));
	EXPECT_TRUE(ldfcr.detect(_T("test/Officefiles/Officefiles.odf.errorExt"), nullptr, nullptr));
	EXPECT_TRUE(ldfcr.detect(_T("test/Officefiles/Officefiles.odg.errorExt"), nullptr, nullptr));
	EXPECT_TRUE(ldfcr.detect(_T("test/Officefiles/Officefiles.odp.errorExt"), nullptr, nullptr));
	EXPECT_TRUE(ldfcr.detect(_T("test/Officefiles/Officefiles.ods.errorExt"), nullptr, nullptr));
	EXPECT_TRUE(ldfcr.detect(_T("test/Officefiles/Officefiles.odt.errorExt"), nullptr, nullptr));
	EXPECT_TRUE(ldfcr.detect(_T("test/Officefiles/Officefiles.ofd.errorExt"), nullptr, nullptr));
	EXPECT_TRUE(ldfcr.detect(_T("test/Officefiles/Officefiles.pdf.errorExt"), nullptr, nullptr));
	EXPECT_TRUE(ldfcr.detect(_T("test/Officefiles/Officefiles.potm.errorExt"), nullptr, nullptr));
	EXPECT_TRUE(ldfcr.detect(_T("test/Officefiles/Officefiles.potx.errorExt"), nullptr, nullptr));
	EXPECT_TRUE(ldfcr.detect(_T("test/Officefiles/Officefiles.ppsm.errorExt"), nullptr, nullptr));
	EXPECT_TRUE(ldfcr.detect(_T("test/Officefiles/Officefiles.ppsx.errorExt"), nullptr, nullptr));
	EXPECT_TRUE(ldfcr.detect(_T("test/Officefiles/Officefiles.ppt.errorExt"), nullptr, nullptr));
	EXPECT_TRUE(ldfcr.detect(_T("test/Officefiles/Officefiles.pptm.errorExt"), nullptr, nullptr));
	EXPECT_TRUE(ldfcr.detect(_T("test/Officefiles/Officefiles.pptx.errorExt"), nullptr, nullptr));
	EXPECT_TRUE(ldfcr.detect(_T("test/Officefiles/Officefiles.rtf.errorExt"), nullptr, nullptr));
	EXPECT_TRUE(ldfcr.detect(_T("test/Officefiles/Officefiles.vsd.errorExt"), nullptr, nullptr));
	EXPECT_TRUE(ldfcr.detect(_T("test/Officefiles/Officefiles.vsdx.errorExt"), nullptr, nullptr));	
	EXPECT_TRUE(ldfcr.detect(_T("test/Officefiles/Officefiles.wps.errorExt"), nullptr, nullptr));
	EXPECT_TRUE(ldfcr.detect(_T("test/Officefiles/Officefiles.xlam"), nullptr, nullptr));//依赖后缀识别
	EXPECT_TRUE(ldfcr.detect(_T("test/Officefiles/Officefiles.xls.errorExt"), nullptr, nullptr));
	EXPECT_TRUE(ldfcr.detect(_T("test/Officefiles/Officefiles.xlsb"), nullptr, nullptr));//依赖后缀识别
	EXPECT_TRUE(ldfcr.detect(_T("test/Officefiles/Officefiles.xlsm.errorExt"), nullptr, nullptr));
	EXPECT_TRUE(ldfcr.detect(_T("test/Officefiles/Officefiles.xlsx.errorExt"), nullptr, nullptr));
	EXPECT_TRUE(ldfcr.detect(_T("test/Officefiles/Officefiles.xltm.errorExt"), nullptr, nullptr));
	EXPECT_TRUE(ldfcr.detect(_T("test/Officefiles/Officefiles.xltx.errorExt"), nullptr, nullptr));
	EXPECT_TRUE(ldfcr.detect(_T("test/Officefiles/Officefiles.xps.errorExt"), nullptr, nullptr));
	EXPECT_TRUE(ldfcr.detect(_T("test/Officefiles/Officefiles.vssx"), nullptr, nullptr));//依赖后缀识别
}

//文件属性2 后缀识别 压缩文件(1)——7z
TEST_F(LdfcrFixture, SuffixRecognition_Compressfiles_7z)
{
	string testName = ::testing::UnitTest::GetInstance()->current_test_info()->name();
	if (!JudgeFileJoinTest(testName))
		return;

	dlplog_info(g_log_handle, "[%d] ===========================================TEST_F SuffixRecognition_Compressfiles_7z is started===========================================", __LINE__);
	std::string completePath = GetCompletePath("/test/data/SuffixRecognition/Compressfiles.data");
	dlplog_info(g_log_handle, "[%d] %s", __LINE__, completePath.c_str());

	EXPECT_EQ(ldfcr.init(_T("test/data/SuffixRecognition/Compressfiles.data")), 0);
	EXPECT_TRUE(ldfcr.detect(_T("test/Compressfiles/Compressfiles.7z.errorExt"), nullptr, nullptr));
	//EXPECT_TRUE(ldfcr.detect(_T("test/Compressfiles/Compressfiles.tbz2.errorExt"), nullptr, nullptr));//暂无此格式
	//EXPECT_TRUE(ldfcr.detect(_T("test/Compressfiles/Compressfiles.gzip.errorExt"), nullptr, nullptr));//暂无此格式
}

//文件属性2 后缀识别 压缩文件(2)——bz2
TEST_F(LdfcrFixture, SuffixRecognition_Compressfiles_bz2)
{
	string testName = ::testing::UnitTest::GetInstance()->current_test_info()->name();
	if (!JudgeFileJoinTest(testName))
		return;

	dlplog_info(g_log_handle, "[%d] ===========================================TEST_F SuffixRecognition_Compressfiles_bz2 is started===========================================", __LINE__);
	std::string completePath = GetCompletePath("/test/data/SuffixRecognition/Compressfiles.data");
	dlplog_info(g_log_handle, "[%d] %s", __LINE__, completePath.c_str());
	EXPECT_EQ(ldfcr.init(_T("test/data/SuffixRecognition/Compressfiles.data")), 0);
	EXPECT_TRUE(ldfcr.detect(_T("test/Compressfiles/Compressfiles.bz2.errorExt"), nullptr, nullptr));
}

#ifdef WIN32
//文件属性2 后缀识别 压缩文件(3)——exe (非windowsOS不支持该格式防篡改)
TEST_F(LdfcrFixture, SuffixRecognition_Compressfiles_exe)
{
	string testName = ::testing::UnitTest::GetInstance()->current_test_info()->name();
	if (!JudgeFileJoinTest(testName))
		return;

	dlplog_info(g_log_handle, "[%d] ===========================================TEST_F SuffixRecognition_Compressfiles_exe is started===========================================", __LINE__);
	std::string completePath = GetCompletePath("/test/data/SuffixRecognition/Compressfiles.data");
	dlplog_info(g_log_handle, "[%d] %s", __LINE__, completePath.c_str());
	EXPECT_EQ(ldfcr.init(_T("test/data/SuffixRecognition/Compressfiles.data")), 0);
	EXPECT_TRUE(ldfcr.detect(_T("test/Compressfiles/Compressfiles.exe.errorExt"), nullptr, nullptr));
}
#endif

//文件属性2 后缀识别 压缩文件(4)——iso
TEST_F(LdfcrFixture, SuffixRecognition_Compressfiles_iso)
{
	string testName = ::testing::UnitTest::GetInstance()->current_test_info()->name();
	if (!JudgeFileJoinTest(testName))
		return;

	dlplog_info(g_log_handle, "[%d] ===========================================TEST_F SuffixRecognition_Compressfiles_iso is started===========================================", __LINE__);
	std::string completePath = GetCompletePath("/test/data/SuffixRecognition/Compressfiles.data");
	dlplog_info(g_log_handle, "[%d] %s", __LINE__, completePath.c_str());
	EXPECT_EQ(ldfcr.init(_T("test/data/SuffixRecognition/Compressfiles.data")), 0);
	EXPECT_TRUE(ldfcr.detect(_T("test/Compressfiles/Compressfiles.iso"), nullptr, nullptr));//依赖后缀识别
}

//文件属性2 后缀识别 压缩文件(5)——rar
TEST_F(LdfcrFixture, SuffixRecognition_Compressfiles_rar)
{
	string testName = ::testing::UnitTest::GetInstance()->current_test_info()->name();
	if (!JudgeFileJoinTest(testName))
		return;

	dlplog_info(g_log_handle, "[%d] ===========================================TEST_F SuffixRecognition_Compressfiles_rar is started===========================================", __LINE__);
	std::string completePath = GetCompletePath("/test/data/SuffixRecognition/Compressfiles.data");
	dlplog_info(g_log_handle, "[%d] %s", __LINE__, completePath.c_str());
	EXPECT_EQ(ldfcr.init(_T("test/data/SuffixRecognition/Compressfiles.data")), 0);
	EXPECT_TRUE(ldfcr.detect(_T("test/Compressfiles/Compressfiles.rar.errorExt"), nullptr, nullptr));
}

//文件属性2 后缀识别 压缩文件(6)——tar
TEST_F(LdfcrFixture, SuffixRecognition_Compressfiles_tar)
{
	string testName = ::testing::UnitTest::GetInstance()->current_test_info()->name();
	if (!JudgeFileJoinTest(testName))
		return;

	dlplog_info(g_log_handle, "[%d] ===========================================TEST_F SuffixRecognition_Compressfiles_tar is started===========================================", __LINE__);
	std::string completePath = GetCompletePath("/test/data/SuffixRecognition/Compressfiles.data");
	dlplog_info(g_log_handle, "[%d] %s", __LINE__, completePath.c_str());
	EXPECT_EQ(ldfcr.init(_T("test/data/SuffixRecognition/Compressfiles.data")), 0);
	EXPECT_TRUE(ldfcr.detect(_T("test/Compressfiles/Compressfiles.tar.errorExt"), nullptr, nullptr));
}

//文件属性2 后缀识别 压缩文件(7)——wim
TEST_F(LdfcrFixture, SuffixRecognition_Compressfiles_wim)
{
	string testName = ::testing::UnitTest::GetInstance()->current_test_info()->name();
	if (!JudgeFileJoinTest(testName))
		return;

	dlplog_info(g_log_handle, "[%d] ===========================================TEST_F SuffixRecognition_Compressfiles_wim is started===========================================", __LINE__);
	std::string completePath = GetCompletePath("/test/data/SuffixRecognition/Compressfiles.data");
	dlplog_info(g_log_handle, "[%d] %s", __LINE__, completePath.c_str());
	EXPECT_EQ(ldfcr.init(_T("test/data/SuffixRecognition/Compressfiles.data")), 0);
	EXPECT_TRUE(ldfcr.detect(_T("test/Compressfiles/Compressfiles.wim.errorExt"), nullptr, nullptr));
}

//文件属性2 后缀识别 压缩文件(8)——xz
TEST_F(LdfcrFixture, SuffixRecognition_Compressfiles_xz)
{
	string testName = ::testing::UnitTest::GetInstance()->current_test_info()->name();
	if (!JudgeFileJoinTest(testName))
		return;

	dlplog_info(g_log_handle, "[%d] ===========================================TEST_F SuffixRecognition_Compressfiles_xz is started===========================================", __LINE__);
	std::string completePath = GetCompletePath("/test/data/SuffixRecognition/Compressfiles.data");
	dlplog_info(g_log_handle, "[%d] %s", __LINE__, completePath.c_str());
	EXPECT_EQ(ldfcr.init(_T("test/data/SuffixRecognition/Compressfiles.data")), 0);
	EXPECT_TRUE(ldfcr.detect(_T("test/Compressfiles/Compressfiles.xz.errorExt"), nullptr, nullptr));
}

//文件属性2 后缀识别 压缩文件(9)——bz
TEST_F(LdfcrFixture, SuffixRecognition_Compressfiles_bz)
{
	string testName = ::testing::UnitTest::GetInstance()->current_test_info()->name();
	if (!JudgeFileJoinTest(testName))
		return;

	dlplog_info(g_log_handle, "[%d] ===========================================TEST_F SuffixRecognition_Compressfiles_bz is started===========================================", __LINE__);
	std::string completePath = GetCompletePath("/test/data/SuffixRecognition/Compressfiles.data");
	dlplog_info(g_log_handle, "[%d] %s", __LINE__, completePath.c_str());
	EXPECT_EQ(ldfcr.init(_T("test/data/SuffixRecognition/Compressfiles.data")), 0);
	EXPECT_TRUE(ldfcr.detect(_T("test/Compressfiles/Compressfiles.bz"), nullptr, nullptr));//改后缀->格式也变errorExt
}

//文件属性2 后缀识别 压缩文件(10)——cab
TEST_F(LdfcrFixture, SuffixRecognition_Compressfiles_cab)
{
	string testName = ::testing::UnitTest::GetInstance()->current_test_info()->name();
	if (!JudgeFileJoinTest(testName))
		return;

	dlplog_info(g_log_handle, "[%d] ===========================================TEST_F SuffixRecognition_Compressfiles_cab is started===========================================", __LINE__);
	std::string completePath = GetCompletePath("/test/data/SuffixRecognition/Compressfiles.data");
	dlplog_info(g_log_handle, "[%d] %s", __LINE__, completePath.c_str());
	EXPECT_EQ(ldfcr.init(_T("test/data/SuffixRecognition/Compressfiles.data")), 0);
	EXPECT_TRUE(ldfcr.detect(_T("test/Compressfiles/Compressfiles.cab"), nullptr, nullptr));//改后缀->格式也变errorExt
}

//文件属性2 后缀识别 压缩文件(11)——dmg
TEST_F(LdfcrFixture, SuffixRecognition_Compressfiles_dmg)
{
	string testName = ::testing::UnitTest::GetInstance()->current_test_info()->name();
	if (!JudgeFileJoinTest(testName))
		return;

	dlplog_info(g_log_handle, "[%d] ===========================================TEST_F SuffixRecognition_Compressfiles_dmg is started===========================================", __LINE__);
	std::string completePath = GetCompletePath("/test/data/SuffixRecognition/Compressfiles.data");
	dlplog_info(g_log_handle, "[%d] %s", __LINE__, completePath.c_str());
	EXPECT_EQ(ldfcr.init(_T("test/data/SuffixRecognition/Compressfiles.data")), 0);
	EXPECT_TRUE(ldfcr.detect(_T("test/Compressfiles/Compressfiles.dmg"), nullptr, nullptr));//改后缀->格式也变errorExt
}

//文件属性2 后缀识别 压缩文件(12)——tb2
TEST_F(LdfcrFixture, SuffixRecognition_Compressfiles_tb2)
{
	string testName = ::testing::UnitTest::GetInstance()->current_test_info()->name();
	if (!JudgeFileJoinTest(testName))
		return;

	dlplog_info(g_log_handle, "[%d] ===========================================TEST_F SuffixRecognition_Compressfiles_tb2 is started===========================================", __LINE__);
	std::string completePath = GetCompletePath("/test/data/SuffixRecognition/Compressfiles.data");
	dlplog_info(g_log_handle, "[%d] %s", __LINE__, completePath.c_str());
	EXPECT_EQ(ldfcr.init(_T("test/data/SuffixRecognition/Compressfiles.data")), 0);
	EXPECT_TRUE(ldfcr.detect(_T("test/Compressfiles/Compressfiles.tb2"), nullptr, nullptr));//改后缀->格式也变errorExt
}

//文件属性2 后缀识别 压缩文件(13)——lzma
TEST_F(LdfcrFixture, SuffixRecognition_Compressfiles_lzma)
{
	string testName = ::testing::UnitTest::GetInstance()->current_test_info()->name();
	if (!JudgeFileJoinTest(testName))
		return;

	dlplog_info(g_log_handle, "[%d] ===========================================TEST_F SuffixRecognition_Compressfiles_lzma is started===========================================", __LINE__);
	std::string completePath = GetCompletePath("/test/data/SuffixRecognition/Compressfiles.data");
	dlplog_info(g_log_handle, "[%d] %s", __LINE__, completePath.c_str());
	EXPECT_EQ(ldfcr.init(_T("test/data/SuffixRecognition/Compressfiles.data")), 0);
	EXPECT_TRUE(ldfcr.detect(_T("test/Compressfiles/Compressfiles.lzma.errorExt"), nullptr, nullptr));
}

//文件属性3 后缀识别 纯文本文件
TEST_F(LdfcrFixture, SuffixRecognition_PlainTextfiles)
{
	string testName = ::testing::UnitTest::GetInstance()->current_test_info()->name();
	if (!JudgeFileJoinTest(testName))
		return;

	dlplog_info(g_log_handle, "[%d] ===========================================TEST_F SuffixRecognition_PlainTextfiles is started===========================================", __LINE__);
	std::string completePath = GetCompletePath("/test/data/SuffixRecognition/PlainTextfiles.data");
	dlplog_info(g_log_handle, "[%d] %s", __LINE__, completePath.c_str());

	EXPECT_EQ(ldfcr.init(_T("test/data/SuffixRecognition/PlainTextfiles.data")), 0);
	EXPECT_TRUE(ldfcr.detect(_T("test/PlainTextfiles/PlainTextfiles.csv"), nullptr, nullptr));//改后缀->格式也变errorExt
	EXPECT_TRUE(ldfcr.detect(_T("test/PlainTextfiles/PlainTextfiles.html"), nullptr, nullptr));//改后缀->格式也变errorExt
	EXPECT_TRUE(ldfcr.detect(_T("test/PlainTextfiles/PlainTextfiles.txt"), nullptr, nullptr, 0, NULL, Ldfcr::_enTcrOpen::CLOSE));//改后缀->格式也变errorExt
	EXPECT_TRUE(ldfcr.detect(_T("test/PlainTextfiles/PlainTextfiles.xml"), nullptr, nullptr));//改后缀->格式也变errorExt
}

//文件属性4 后缀识别 图片文件
TEST_F(LdfcrFixture, SuffixRecognition_Imagefiles)
{
	string testName = ::testing::UnitTest::GetInstance()->current_test_info()->name();
	if (!JudgeFileJoinTest(testName))
		return;

	dlplog_info(g_log_handle, "[%d] ===========================================TEST_F SuffixRecognition_Imagefiles is started===========================================", __LINE__);
	std::string completePath = GetCompletePath("/test/data/SuffixRecognition/Imagefiles.data");
	dlplog_info(g_log_handle, "[%d] %s", __LINE__, completePath.c_str());

	EXPECT_EQ(ldfcr.init(_T("test/data/SuffixRecognition/Imagefiles.data")), 0);
	EXPECT_TRUE(ldfcr.detect(_T("test/Imagefiles/Imagefiles.eps"), nullptr, nullptr));//改后缀->格式也变errorExt	
	EXPECT_TRUE(ldfcr.detect(_T("test/Imagefiles/Imagefiles.mif"), nullptr, nullptr));//改后缀->格式也变errorExt
	EXPECT_TRUE(ldfcr.detect(_T("test/Imagefiles/Imagefiles.miff"), nullptr, nullptr));//改后缀->格式也变errorExt
	EXPECT_TRUE(ldfcr.detect(_T("test/Imagefiles/Imagefiles.pdd"), nullptr, nullptr));//改后缀->格式也变errorExt
	EXPECT_TRUE(ldfcr.detect(_T("test/Imagefiles/Imagefiles.psb"), nullptr, nullptr));//改后缀->格式也变errorExt
	EXPECT_TRUE(ldfcr.detect(_T("test/Imagefiles/Imagefiles.svg"), nullptr, nullptr));//改后缀->格式也变errorExt
	EXPECT_TRUE(ldfcr.detect(_T("test/Imagefiles/Imagefiles.tiff"), nullptr, nullptr));//改后缀->格式也变errorExt
	EXPECT_TRUE(ldfcr.detect(_T("test/Imagefiles/Imagefiles.wmf.errorExt"), nullptr, nullptr));//不依赖后缀识别
	EXPECT_TRUE(ldfcr.detect(_T("test/Imagefiles/Imagefiles.gif.errorExt"), nullptr, nullptr));
	EXPECT_TRUE(ldfcr.detect(_T("test/Imagefiles/Imagefiles.ico.errorExt"), nullptr, nullptr));
	EXPECT_TRUE(ldfcr.detect(_T("test/Imagefiles/Imagefiles.jpg.errorExt"), nullptr, nullptr));
	EXPECT_TRUE(ldfcr.detect(_T("test/Imagefiles/Imagefiles.png.errorExt"), nullptr, nullptr));
	EXPECT_TRUE(ldfcr.detect(_T("test/Imagefiles/Imagefiles.psd.errorExt"), nullptr, nullptr));
	EXPECT_TRUE(ldfcr.detect(_T("test/Imagefiles/Imagefiles.tif.errorExt"), nullptr, nullptr));
	EXPECT_TRUE(ldfcr.detect(_T("test/Imagefiles/Imagefiles.bmp.errorExt"), nullptr, nullptr));
	EXPECT_TRUE(ldfcr.detect(_T("test/Imagefiles/Imagefiles.jpeg"), nullptr, nullptr));
	//EXPECT_TRUE(ldfcr.detect(_T("test/Imagefiles/Imagefiles.webp"), nullptr, nullptr));//有无修改后缀都不敏感
}

//文件属性5 后缀识别 AutoCAD文件
TEST_F(LdfcrFixture, SuffixRecognition_AutoCADfiles)
{
	string testName = ::testing::UnitTest::GetInstance()->current_test_info()->name();
	if (!JudgeFileJoinTest(testName))
		return;

	dlplog_info(g_log_handle, "[%d] ===========================================TEST_F SuffixRecognition_AutoCADfiles is started===========================================", __LINE__);
	std::string completePath = GetCompletePath("/test/data/SuffixRecognition/AutoCADfiles.data");
	dlplog_info(g_log_handle, "[%d] %s", __LINE__, completePath.c_str());

	EXPECT_EQ(ldfcr.init(_T("test/data/SuffixRecognition/AutoCADfiles.data")), 0);
	EXPECT_TRUE(ldfcr.detect(_T("test/AutoCADfiles/AutoCADfiles.dwg.errorExt"), nullptr, nullptr));
	EXPECT_TRUE(ldfcr.detect(_T("test/AutoCADfiles/AutoCADfiles.dxf.errorExt"), nullptr, nullptr));
}

//文件属性6 后缀识别 音频文件
TEST_F(LdfcrFixture, SuffixRecognition_Audiofiles)
{
	string testName = ::testing::UnitTest::GetInstance()->current_test_info()->name();
	if (!JudgeFileJoinTest(testName))
		return;

	dlplog_info(g_log_handle, "[%d] ===========================================TEST_F SuffixRecognition_Audiofiles is started===========================================", __LINE__);
	std::string completePath = GetCompletePath("/test/data/SuffixRecognition/Audiofiles.data");
	dlplog_info(g_log_handle, "[%d] %s", __LINE__, completePath.c_str());

	EXPECT_EQ(ldfcr.init(_T("test/data/SuffixRecognition/Audiofiles.data")), 0);
	EXPECT_TRUE(ldfcr.detect(_T("test/Audiofiles/Audiofiles.crx.errorExt"), nullptr, nullptr));
	EXPECT_TRUE(ldfcr.detect(_T("test/Audiofiles/Audiofiles.flac"), nullptr, nullptr));//改后缀->格式也变errorExt
	EXPECT_TRUE(ldfcr.detect(_T("test/Audiofiles/Audiofiles.m4a.errorExt"), nullptr, nullptr));
	EXPECT_TRUE(ldfcr.detect(_T("test/Audiofiles/Audiofiles.mov.errorExt"), nullptr, nullptr));
	EXPECT_TRUE(ldfcr.detect(_T("test/Audiofiles/Audiofiles.mp3.errorExt"), nullptr, nullptr));
	EXPECT_TRUE(ldfcr.detect(_T("test/Audiofiles/Audiofiles.swf.errorExt"), nullptr, nullptr));
	EXPECT_TRUE(ldfcr.detect(_T("test/Audiofiles/Audiofiles.wmv.errorExt"), nullptr, nullptr));
	EXPECT_TRUE(ldfcr.detect(_T("test/Audiofiles/Audiofiles.ogg.errorExt"), nullptr, nullptr));
	EXPECT_TRUE(ldfcr.detect(_T("test/Audiofiles/Audiofiles.amr"), nullptr, nullptr));
	//EXPECT_TRUE(ldfcr.detect(_T("test/Audiofiles/Audiofiles.cda.errorExt"), nullptr, nullptr));//暂无此格式
	//EXPECT_TRUE(ldfcr.detect(_T("test/Audiofiles/Audiofiles.mid.errorExt"), nullptr, nullptr));//暂无此格式
	//EXPECT_TRUE(ldfcr.detect(_T("test/Audiofiles/Audiofiles.ra.errorExt"), nullptr, nullptr));//暂无此格式
	//EXPECT_TRUE(ldfcr.detect(_T("test/Audiofiles/Audiofiles.qcp.errorExt"), nullptr, nullptr));//暂无此格式
	//EXPECT_TRUE(ldfcr.detect(_T("test/Audiofiles/Audiofiles.rmi.errorExt"), nullptr, nullptr));//暂无此格式
}

//文件属性7 后缀识别 视频文件
TEST_F(LdfcrFixture, SuffixRecognition_Videofiles)
{
	string testName = ::testing::UnitTest::GetInstance()->current_test_info()->name();
	if (!JudgeFileJoinTest(testName))
		return;

	dlplog_info(g_log_handle, "[%d] ===========================================TEST_F SuffixRecognition_Videofiles is started===========================================", __LINE__);
	std::string completePath = GetCompletePath("/test/data/SuffixRecognition/Videofiles.data");
	dlplog_info(g_log_handle, "[%d] %s", __LINE__, completePath.c_str());

	EXPECT_EQ(ldfcr.init(_T("test/data/SuffixRecognition/Videofiles.data")), 0);
	EXPECT_TRUE(ldfcr.detect(_T("test/Videofiles/Videofiles.avi.errorExt"), nullptr, nullptr));
	EXPECT_TRUE(ldfcr.detect(_T("test/Videofiles/Videofiles.flv.errorExt"), nullptr, nullptr));
	EXPECT_TRUE(ldfcr.detect(_T("test/Videofiles/Videofiles.m4v.errorExt"), nullptr, nullptr));
	EXPECT_TRUE(ldfcr.detect(_T("test/Videofiles/Videofiles.mkv.errorExt"), nullptr, nullptr));
	EXPECT_TRUE(ldfcr.detect(_T("test/Videofiles/Videofiles.mp4.errorExt"), nullptr, nullptr));
	EXPECT_TRUE(ldfcr.detect(_T("test/Videofiles/Videofiles.3gp.errorExt"), nullptr, nullptr));
	EXPECT_TRUE(ldfcr.detect(_T("test/Videofiles/Videofiles.rmvb"), nullptr, nullptr));
	//EXPECT_TRUE(ldfcr.detect(_T("test/Videofiles/Videofiles.wav"), nullptr, nullptr));//有无修改后缀都不敏感
	//EXPECT_TRUE(ldfcr.detect(_T("test/Videofiles/Videofiles.asf.errorExt"), nullptr, nullptr));//暂无此格式
}

//文件属性8 后缀识别 数据库文件
TEST_F(LdfcrFixture, SuffixRecognition_Databasefiles)
{
	string testName = ::testing::UnitTest::GetInstance()->current_test_info()->name();
	if (!JudgeFileJoinTest(testName))
		return;

	dlplog_info(g_log_handle, "[%d] ===========================================TEST_F SuffixRecognition_Databasefiles is started===========================================", __LINE__);
	std::string completePath = GetCompletePath("/test/data/SuffixRecognition/Databasefiles.data");
	dlplog_info(g_log_handle, "[%d] %s", __LINE__, completePath.c_str());

	EXPECT_EQ(ldfcr.init(_T("test/data/SuffixRecognition/Databasefiles.data")), 0);
	EXPECT_TRUE(ldfcr.detect(_T("test/Databasefiles/Databasefiles.accdb.errorExt"), nullptr, nullptr));
	EXPECT_TRUE(ldfcr.detect(_T("test/Databasefiles/Databasefiles.db"), nullptr, nullptr));//改后缀->格式也变errorExt
	EXPECT_TRUE(ldfcr.detect(_T("test/Databasefiles/Databasefiles.mdb.errorExt"), nullptr, nullptr));
}

//文件属性9 后缀识别 流程图、思维导图文件
TEST_F(LdfcrFixture, SuffixRecognition_Flow_mindmaps)
{
	string testName = ::testing::UnitTest::GetInstance()->current_test_info()->name();
	if (!JudgeFileJoinTest(testName))
		return;

	dlplog_info(g_log_handle, "[%d] ===========================================TEST_F SuffixRecognition_Flow_mindmaps is started===========================================", __LINE__);
	std::string completePath = GetCompletePath("/test/data/SuffixRecognition/Flow_mindmaps.data");
	dlplog_info(g_log_handle, "[%d] %s", __LINE__, completePath.c_str());

	EXPECT_EQ(ldfcr.init(_T("test/data/SuffixRecognition/Flow_mindmaps.data")), 0);
	EXPECT_TRUE(ldfcr.detect(_T("test/Flow_mindmaps/Flow_mindmaps.eddx.errorExt"), nullptr, nullptr));
	EXPECT_TRUE(ldfcr.detect(_T("test/Flow_mindmaps/Flow_mindmaps.emmx.errorExt"), nullptr, nullptr));
	EXPECT_TRUE(ldfcr.detect(_T("test/Flow_mindmaps/Flow_mindmaps.mmap.errorExt"), nullptr, nullptr));
	EXPECT_TRUE(ldfcr.detect(_T("test/Flow_mindmaps/Flow_mindmaps.mmat.errorExt"), nullptr, nullptr));
	EXPECT_TRUE(ldfcr.detect(_T("test/Flow_mindmaps/Flow_mindmaps.xmind.errorExt"), nullptr, nullptr));
	EXPECT_TRUE(ldfcr.detect(_T("test/Flow_mindmaps/Flow_mindmaps.xpi.errorExt"), nullptr, nullptr));
	EXPECT_TRUE(ldfcr.detect(_T("test/Flow_mindmaps/Flow_mindmaps.zxm.errorExt"), nullptr, nullptr));
	//EXPECT_TRUE(ldfcr.detect(_T("test/Flow_mindmaps/Flow_mindmaps.kmz.errorExt"), nullptr, nullptr));//暂无此格式
	//EXPECT_TRUE(ldfcr.detect(_T("test/Flow_mindmaps/Flow_mindmaps.usdz.errorExt"), nullptr, nullptr));//暂无此格式
}

//文件属性10 后缀识别 其他文件
TEST_F(LdfcrFixture, SuffixRecognition_Otherfiles)
{
	string testName = ::testing::UnitTest::GetInstance()->current_test_info()->name();
	if (!JudgeFileJoinTest(testName))
		return;

	dlplog_info(g_log_handle, "[%d] ===========================================TEST_F SuffixRecognition_Otherfiles is started===========================================", __LINE__);
	std::string completePath = GetCompletePath("/test/data/SuffixRecognition/Otherfiles.data");
	dlplog_info(g_log_handle, "[%d] %s", __LINE__, completePath.c_str());

	EXPECT_EQ(ldfcr.init(_T("test/data/SuffixRecognition/Otherfiles.data")), 0);
	EXPECT_TRUE(ldfcr.detect(_T("test/Otherfiles/Otherfiles.3mf.errorExt"), nullptr, nullptr));
	EXPECT_TRUE(ldfcr.detect(_T("test/Otherfiles/Otherfiles.accdt"), nullptr, nullptr));//改后缀->格式也变errorExt
	EXPECT_TRUE(ldfcr.detect(_T("test/Otherfiles/Otherfiles.cdf-ms.errorExt"), nullptr, nullptr));
	EXPECT_TRUE(ldfcr.detect(_T("test/Otherfiles/Otherfiles.chm.errorExt"), nullptr, nullptr));
	EXPECT_TRUE(ldfcr.detect(_T("test/Otherfiles/Otherfiles.dmp.errorExt"), nullptr, nullptr));
	EXPECT_TRUE(ldfcr.detect(_T("test/Otherfiles/Otherfiles.lib"), nullptr, nullptr));//改后缀->格式也变errorExt
	EXPECT_TRUE(ldfcr.detect(_T("test/Otherfiles/Otherfiles.lnk.errorExt"), nullptr, nullptr));
	EXPECT_TRUE(ldfcr.detect(_T("test/Otherfiles/Otherfiles.msix.errorExt"), nullptr, nullptr));
	EXPECT_TRUE(ldfcr.detect(_T("test/Otherfiles/Otherfiles.nvram.errorExt"), nullptr, nullptr));
	EXPECT_TRUE(ldfcr.detect(_T("test/Otherfiles/Otherfiles.obj"), nullptr, nullptr));//改后缀->格式也变errorExt
	EXPECT_TRUE(ldfcr.detect(_T("test/Otherfiles/Otherfiles.pdb.errorExt"), nullptr, nullptr));
	EXPECT_TRUE(ldfcr.detect(_T("test/Otherfiles/Otherfiles.thmx.errorExt"), nullptr, nullptr));
	EXPECT_TRUE(ldfcr.detect(_T("test/Otherfiles/Otherfiles.vmdk"), nullptr, nullptr));//改后缀->格式也变errorExt
	EXPECT_TRUE(ldfcr.detect(_T("test/Otherfiles/Otherfiles.vmsn"), nullptr, nullptr));//改后缀->格式也变errorExt
	//EXPECT_TRUE(ldfcr.detect(_T("test/Otherfiles/Otherfiles.vmem.errorExt"), nullptr, nullptr));//文件太大
}

//==========================================================================文件属性-结束测试是否依赖后缀识别====================================================================================

// 支持文本提取文件格式 58种
TEST_F(LdfcrFixture, KEYWORD_TextExtractFormats)
{
	string testName = ::testing::UnitTest::GetInstance()->current_test_info()->name();
	if (!JudgeFileJoinTest(testName))
		return;

	dlplog_info(g_log_handle, "[%d] ===========================================TEST_F KEYWORD_TextExtractFormats is started===========================================", __LINE__);
	std::string completePath = GetCompletePath("/test/data/MultiKeyword.data");
	dlplog_info(g_log_handle, "[%d] %s", __LINE__, completePath.c_str());

	EXPECT_EQ(ldfcr.init(_T("test/data/MultiKeyword.data")), 0);
	EXPECT_TRUE(ldfcr.detect(_T("test/SupportTextExtractList/officeFile/hahaha.doc"), nullptr, nullptr));
	EXPECT_TRUE(ldfcr.detect(_T("test/SupportTextExtractList/officeFile/hahaha.docx"), nullptr, nullptr));
	EXPECT_TRUE(ldfcr.detect(_T("test/SupportTextExtractList/officeFile/hahaha.epub"), nullptr, nullptr));
	EXPECT_TRUE(ldfcr.detect(_T("test/SupportTextExtractList/officeFile/hahaha.et"), nullptr, nullptr));
	EXPECT_TRUE(ldfcr.detect(_T("test/SupportTextExtractList/officeFile/hahaha.odf"), nullptr, nullptr));
	EXPECT_TRUE(ldfcr.detect(_T("test/SupportTextExtractList/officeFile/hahaha.odg"), nullptr, nullptr));
	EXPECT_TRUE(ldfcr.detect(_T("test/SupportTextExtractList/officeFile/hahaha.odp"), nullptr, nullptr));
	EXPECT_TRUE(ldfcr.detect(_T("test/SupportTextExtractList/officeFile/hahaha.ods"), nullptr, nullptr));
	EXPECT_TRUE(ldfcr.detect(_T("test/SupportTextExtractList/officeFile/hahaha.odt"), nullptr, nullptr));
	EXPECT_TRUE(ldfcr.detect(_T("test/SupportTextExtractList/officeFile/hahaha.pdf"), nullptr, nullptr));
	EXPECT_TRUE(ldfcr.detect(_T("test/SupportTextExtractList/officeFile/hahaha.ppt"), nullptr, nullptr));
	EXPECT_TRUE(ldfcr.detect(_T("test/SupportTextExtractList/officeFile/hahaha.pptx"), nullptr, nullptr));
	EXPECT_TRUE(ldfcr.detect(_T("test/SupportTextExtractList/officeFile/hahaha.rtf"), nullptr, nullptr));
	EXPECT_TRUE(ldfcr.detect(_T("test/SupportTextExtractList/officeFile/hahaha.vsd"), nullptr, nullptr));
	EXPECT_TRUE(ldfcr.detect(_T("test/SupportTextExtractList/officeFile/hahaha.wps"), nullptr, nullptr));
	EXPECT_TRUE(ldfcr.detect(_T("test/SupportTextExtractList/officeFile/hahaha.xls"), nullptr, nullptr));
	EXPECT_TRUE(ldfcr.detect(_T("test/SupportTextExtractList/officeFile/hahaha.xlsx"), nullptr, nullptr));
	EXPECT_TRUE(ldfcr.detect(_T("test/SupportTextExtractList/officeFile/hahaha.xps"), nullptr, nullptr));
	EXPECT_TRUE(ldfcr.detect(_T("test/SupportTextExtractList/officeFile/moreEasy.dps"), nullptr, nullptr));
	EXPECT_TRUE(ldfcr.detect(_T("test/SupportTextExtractList/officeFile/hahaha.vsdx"), nullptr, nullptr));
	EXPECT_TRUE(ldfcr.detect(_T("test/SupportTextExtractList/officeFile/hahaha.vss"), nullptr, nullptr));
	EXPECT_TRUE(ldfcr.detect(_T("test/SupportTextExtractList/officeFile/hahaha.vst"), nullptr, nullptr));
	EXPECT_TRUE(ldfcr.detect(_T("test/SupportTextExtractList/officeFile/hahaha.ofd"), nullptr, nullptr));
	EXPECT_TRUE(ldfcr.detect(_T("test/SupportTextExtractList/compressFile/ARGOLA.ARJ"), nullptr, nullptr));
	EXPECT_TRUE(ldfcr.detect(_T("test/SupportTextExtractList/compressFile/and.Z"), nullptr, nullptr));
	EXPECT_TRUE(ldfcr.detect(_T("test/SupportTextExtractList/compressFile/and.chm"), nullptr, nullptr));
	EXPECT_TRUE(ldfcr.detect(_T("test/SupportTextExtractList/compressFile/name.cab"), nullptr, nullptr));
	EXPECT_TRUE(ldfcr.detect(_T("test/SupportTextExtractList/compressFile/hahaha.7z"), nullptr, nullptr));
	EXPECT_TRUE(ldfcr.detect(_T("test/SupportTextExtractList/compressFile/hahaha.bz"), nullptr, nullptr));
	EXPECT_TRUE(ldfcr.detect(_T("test/SupportTextExtractList/compressFile/hahaha.bz2"), nullptr, nullptr));
	EXPECT_TRUE(ldfcr.detect(_T("test/SupportTextExtractList/compressFile/hahaha.dmg"), nullptr, nullptr));
	EXPECT_TRUE(ldfcr.detect(_T("test/SupportTextExtractList/compressFile/hahaha.tar.gz"), nullptr, nullptr));
	EXPECT_TRUE(ldfcr.detect(_T("test/SupportTextExtractList/compressFile/hahaha.iso"), nullptr, nullptr));
	EXPECT_TRUE(ldfcr.detect(_T("test/SupportTextExtractList/compressFile/hahaha.lha"), nullptr, nullptr));
	EXPECT_TRUE(ldfcr.detect(_T("test/SupportTextExtractList/compressFile/hahaha.lzh"), nullptr, nullptr));
	EXPECT_TRUE(ldfcr.detect(_T("test/SupportTextExtractList/compressFile/hahaha.lzma"), nullptr, nullptr));
	EXPECT_TRUE(ldfcr.detect(_T("test/SupportTextExtractList/compressFile/hahaha.rar"), nullptr, nullptr));
	EXPECT_TRUE(ldfcr.detect(_T("test/SupportTextExtractList/compressFile/hahaha.tar"), nullptr, nullptr));
	EXPECT_TRUE(ldfcr.detect(_T("test/SupportTextExtractList/compressFile/hahaha.tb2"), nullptr, nullptr));
	EXPECT_TRUE(ldfcr.detect(_T("test/SupportTextExtractList/compressFile/hahaha.tbz2"), nullptr, nullptr));
	EXPECT_TRUE(ldfcr.detect(_T("test/SupportTextExtractList/compressFile/hahaha.tgz"), nullptr, nullptr));
	EXPECT_TRUE(ldfcr.detect(_T("test/SupportTextExtractList/compressFile/hahaha.wim"), nullptr, nullptr));
	EXPECT_TRUE(ldfcr.detect(_T("test/SupportTextExtractList/compressFile/hahaha.xz"), nullptr, nullptr));
	EXPECT_TRUE(ldfcr.detect(_T("test/SupportTextExtractList/compressFile/hahaha.zip"), nullptr, nullptr));
	EXPECT_TRUE(ldfcr.detect(_T("test/SupportTextExtractList/compressFile/hahaha.xar"), nullptr, nullptr));
	EXPECT_TRUE(ldfcr.detect(_T("test/SupportTextExtractList/Flow_mindmaps/hahaha.emmx"), nullptr, nullptr));
	EXPECT_TRUE(ldfcr.detect(_T("test/SupportTextExtractList/Flow_mindmaps/hahaha.mmap"), nullptr, nullptr));
	EXPECT_TRUE(ldfcr.detect(_T("test/SupportTextExtractList/Flow_mindmaps/hahaha.mmat"), nullptr, nullptr));
	EXPECT_TRUE(ldfcr.detect(_T("test/SupportTextExtractList/Flow_mindmaps/hahaha.xmind"), nullptr, nullptr));
	EXPECT_TRUE(ldfcr.detect(_T("test/SupportTextExtractList/Flow_mindmaps/hahaha.zxm"), nullptr, nullptr));
	EXPECT_TRUE(ldfcr.detect(_T("test/SupportTextExtractList/Flow_mindmaps/moreEasy.eddx"), nullptr, nullptr));
	EXPECT_TRUE(ldfcr.detect(_T("test/SupportTextExtractList/graphPaperFile/hahaha.dwg"), nullptr, nullptr));
	EXPECT_TRUE(ldfcr.detect(_T("test/SupportTextExtractList/graphPaperFile/左右.3dxml"), nullptr, nullptr));
	EXPECT_TRUE(ldfcr.detect(_T("test/SupportTextExtractList/graphPaperFile/3.dxf"), nullptr, nullptr));
	EXPECT_TRUE(ldfcr.detect(_T("test/SupportTextExtractList/textFile/hahaha.csv"), nullptr, nullptr));
	EXPECT_TRUE(ldfcr.detect(_T("test/SupportTextExtractList/textFile/hahaha.html"), nullptr, nullptr));
	EXPECT_TRUE(ldfcr.detect(_T("test/SupportTextExtractList/textFile/hahaha.txt"), nullptr, nullptr));
	EXPECT_TRUE(ldfcr.detect(_T("test/SupportTextExtractList/textFile/hahaha.xml"), nullptr, nullptr));
}


#ifdef WIN32

//全盘扫描和自检策略，进行数据库指纹算法检测（全局）
TEST_F(LdfcrFixture, DBFP_GlobalScan_Selfcheck)
{
	string testName = ::testing::UnitTest::GetInstance()->current_test_info()->name();
	if (!JudgeFileJoinTest(testName))
		return;

	dlplog_info(g_log_handle, "[%d] ===========================================TEST_F DBFP_GlobalScan_Selfcheck is started===========================================", __LINE__);
	std::string completePath = GetCompletePath("/test/data/DBFP_type19_globalScan.data");
	dlplog_info(g_log_handle, "[%d] %s", __LINE__, completePath.c_str());

	//更新策略
	EXPECT_EQ(ldfcr.g_init(_T("test/data/DBFP_type19_globalScan.data")), 0);//全盘扫描 数据库指纹
	EXPECT_EQ(ldfcr.g_init(_T("test/data/DBFP_type28_selfcheck.data")), 0);//自检策略 数据库指纹

	EXPECT_TRUE(ldfcr.g_detect(_T("test/DataFileEmpty.csv"), NULL, NULL, 19));
	EXPECT_FALSE(ldfcr.g_detect(_T("test/DataFile.txt"), NULL, NULL, 19));

	EXPECT_FALSE(ldfcr.g_detect(_T("test/DataFileEmpty.csv"), NULL, NULL, 28));
	EXPECT_TRUE(ldfcr.g_detect(_T("test/DataFile.txt"), NULL, NULL, 28));
}

//全盘扫描，进行关键字算法检测（全局）
TEST_F(LdfcrFixture, KEYWORD_GlobalScan)
{
	string testName = ::testing::UnitTest::GetInstance()->current_test_info()->name();
	if (!JudgeFileJoinTest(testName))
		return;

	dlplog_info(g_log_handle, "[%d] ===========================================TEST_F KEYWORD_GlobalScan is started===========================================", __LINE__);
	std::string completePath = GetCompletePath("/test/data/KEYWORD_type19_globalScan.data");
	dlplog_info(g_log_handle, "[%d] %s", __LINE__, completePath.c_str());

	EXPECT_EQ(ldfcr.g_init(_T("test/data/KEYWORD_type19_globalScan.data")), 0);//全盘扫描 关键字
	EXPECT_EQ(ldfcr.g_init(_T("test/data/strategy_srv.data")), 0);//普通策略

	EXPECT_TRUE(ldfcr.g_detect(_T("test/hahaha.txt"), nullptr, nullptr, 19));
	EXPECT_FALSE(ldfcr.g_detect(_T("test/hahaha.txt"), nullptr, nullptr, 28));

}
#endif

//关键字   验证错误值 LDFCR_OK = 0
TEST_F(LdfcrFixture, KEYWORD_ErrCode)
{
	string testName = ::testing::UnitTest::GetInstance()->current_test_info()->name();
	if (!JudgeFileJoinTest(testName))
		return;

	dlplog_info(g_log_handle, "[%d] ===========================================TEST_F KEYWORD_ErrCode is started===========================================", __LINE__);
	std::string completePath = GetCompletePath("/test/data/hahaha.data");
	dlplog_info(g_log_handle, "[%d] %s", __LINE__, completePath.c_str());

	EXPECT_EQ(ldfcr.init(_T("test/data/hahaha.data")), 0);
	IFCRResult *result = NULL;
	BOOL sensitive;
	EXPECT_TRUE(sensitive = ldfcr.detect(_T("test/hahaha.txt"),(void**)&result));
	EXPECT_TRUE(ldfcr.get_error_code(result, sensitive, 0)); // 关键字匹配成功 errcode应返回0
	CLEAN_COM(result);
}

// 韩文路径下识别 JPG格式图片  
TEST_F(LdfcrFixture, KEYWORD_KoreanPath_JPG_OCR)
{
	string testName = ::testing::UnitTest::GetInstance()->current_test_info()->name();
	if (!JudgeFileJoinTest(testName))
		return;

	dlplog_info(g_log_handle, "[%d] ===========================================TEST_F KEYWORD_KoreanPath_JPG_OCR is started===========================================", __LINE__);
	std::string completePath = GetCompletePath("/test/tel/s_image.data");
	dlplog_info(g_log_handle, "[%d] %s", __LINE__, completePath.c_str());

	EXPECT_EQ(ldfcr.init(_T("test/tel/s_image.data")), 0);
	//以下图片中“更容易”字样触发敏感
	EXPECT_TRUE(ldfcr.detect(_T("test/사과쥬스/1.jpg"), nullptr, nullptr));

}

// 韩文路径下识别 PNG格式图片  
TEST_F(LdfcrFixture, KEYWORD_KoreanPath_PNG_OCR)
{
	string testName = ::testing::UnitTest::GetInstance()->current_test_info()->name();
	if (!JudgeFileJoinTest(testName))
		return;

	dlplog_info(g_log_handle, "[%d] ===========================================TEST_F KEYWORD_KoreanPath_PNG_OCR is started===========================================", __LINE__);
	std::string completePath = GetCompletePath("/test/tel/s_image.data");
	dlplog_info(g_log_handle, "[%d] %s", __LINE__, completePath.c_str());

	EXPECT_EQ(ldfcr.init(_T("test/tel/s_image.data")), 0);
	//以下图片中“更容易”字样触发敏感
	EXPECT_TRUE(ldfcr.detect(_T("test/사과쥬스/1.png"), nullptr, nullptr));

}

// 韩文路径下识别 DOC格式 嵌套图片  
TEST_F(LdfcrFixture, KEYWORD_KoreanPath_DOC_OCR)
{
	string testName = ::testing::UnitTest::GetInstance()->current_test_info()->name();
	if (!JudgeFileJoinTest(testName))
		return;

	dlplog_info(g_log_handle, "[%d] ===========================================TEST_F KEYWORD_KoreanPath_DOC_OCR is started===========================================", __LINE__);
	std::string completePath = GetCompletePath("/test/tel/s_image.data");
	dlplog_info(g_log_handle, "[%d] %s", __LINE__, completePath.c_str());

	EXPECT_EQ(ldfcr.init(_T("test/tel/s_image.data")), 0);
	//以下图片中“更容易”字样触发敏感
	EXPECT_TRUE(ldfcr.detect(_T("test/사과쥬스/1.doc"), nullptr, nullptr));

}

// 韩文路径下识别 DOCX格式 嵌套图片  
TEST_F(LdfcrFixture, KEYWORD_KoreanPath_DOCX_OCR)
{
	string testName = ::testing::UnitTest::GetInstance()->current_test_info()->name();
	if (!JudgeFileJoinTest(testName))
		return;

	dlplog_info(g_log_handle, "[%d] ===========================================TEST_F KEYWORD_KoreanPath_DOCX_OCR is started===========================================", __LINE__);
	std::string completePath = GetCompletePath("/test/tel/s_image.data");
	dlplog_info(g_log_handle, "[%d] %s", __LINE__, completePath.c_str());

	EXPECT_EQ(ldfcr.init(_T("test/tel/s_image.data")), 0);
	//以下图片中“更容易”字样触发敏感
	EXPECT_TRUE(ldfcr.detect(_T("test/사과쥬스/1.docx"), nullptr, nullptr));

}

// 韩文路径下识别 PDF格式 嵌套图片  
TEST_F(LdfcrFixture, KEYWORD_KoreanPath_PDF_OCR)
{
	string testName = ::testing::UnitTest::GetInstance()->current_test_info()->name();
	if (!JudgeFileJoinTest(testName))
		return;

	dlplog_info(g_log_handle, "[%d] ===========================================TEST_F KEYWORD_KoreanPath_PDF_OCR is started===========================================", __LINE__);

	if (operSysJugXP()) // XP版本支持
	{
		// D1版本在xp系统下暂不支持韩文路径下“pdf文件内嵌ocr”的识别
		std::string completePath1 = GetCompletePath("/test/tel/o_15png.data");
		dlplog_info(g_log_handle, "[%d] %s", __LINE__, completePath1.c_str());
		EXPECT_EQ(ldfcr.init(_T("test/tel/o_15png.data")), 0);		
		//以下图片中“15png”字样触发敏感
		EXPECT_TRUE(ldfcr.detect(_T("test/사과쥬스/1.pdf"), nullptr, nullptr));
	}
	else
	{
		// D1版本在win10系统支持韩文路径下pdf文件内嵌ocr的识别
		std::string completePath = GetCompletePath("/test/tel/s_image.data");
		dlplog_info(g_log_handle, "[%d] %s", __LINE__, completePath.c_str());
		EXPECT_EQ(ldfcr.init(_T("test/tel/s_image.data")), 0);
		//以下图片中“更容易”字样触发敏感
		EXPECT_TRUE(ldfcr.detect(_T("test/사과쥬스/1.docx"), nullptr, nullptr));
	}
}

// 韩文路径下检测 关键字算法
TEST_F(LdfcrFixture, KEYWORD_KoreanPath)
{
	string testName = ::testing::UnitTest::GetInstance()->current_test_info()->name();
	if (!JudgeFileJoinTest(testName))
		return;

	dlplog_info(g_log_handle, "[%d] ===========================================TEST_F KEYWORD_KoreanPath is started===========================================", __LINE__);
	std::string completePath = GetCompletePath("/test/data/hahaha.data");
	dlplog_info(g_log_handle, "[%d] %s", __LINE__, completePath.c_str());

	EXPECT_EQ(ldfcr.init(_T("test/data/hahaha.data")), 0);
	EXPECT_TRUE(ldfcr.detect(_T("test/사과쥬스/hahaha.txt"), nullptr, nullptr));
}

// 验证文本提取docx类型文件无异常
TEST_F(LdfcrFixture, KEYWORD_Contract_docx)
{
	string testName = ::testing::UnitTest::GetInstance()->current_test_info()->name();
	if (!JudgeFileJoinTest(testName))
		return;

	dlplog_info(g_log_handle, "[%d] ===========================================TEST_F KEYWORD_Contract_docx is started===========================================", __LINE__);
	std::string completePath = GetCompletePath("/test/data/ContractKeyword.data");
	dlplog_info(g_log_handle, "[%d] %s", __LINE__, completePath.c_str());
	EXPECT_EQ(ldfcr.init(_T("test/data/ContractKeyword.data")), 0);
	IFCRResult *result = NULL;
	EXPECT_TRUE(ldfcr.detect(_T("test/FileFilterAbility/合同docx19.docx"), (void**)&result, NULL, 0, NULL, Ldfcr::_enTcrOpen::CLOSE));
	EXPECT_TRUE(ldfcr.getFileExt(result, "docx")); //DOCX文件类型
	CLEAN_COM(result);
}

// 标签策略  验证返回json中是否含有taginfo字段
TEST_F(LdfcrFixture, isExist_TagInfo)
{
	string testName = ::testing::UnitTest::GetInstance()->current_test_info()->name();
	if (!JudgeFileJoinTest(testName))
		return;

	dlplog_info(g_log_handle, "[%d] ===========================================TEST_F isExist_TagInfo is started===========================================", __LINE__);
	std::string completePath = GetCompletePath("/test/data/RegularAndTagKW.data");
	dlplog_info(g_log_handle, "[%d] %s", __LINE__, completePath.c_str());

	EXPECT_EQ(ldfcr.init(_T("test/data/RegularAndTagKW.data")), 0);
	IFCRResult *result = NULL;
	BOOL sensitive;
	EXPECT_TRUE(sensitive = ldfcr.detect(_T("test/hahaha_tag.txt"),(void**)&result));
	EXPECT_TRUE(ldfcr.checkTagInfoExists(result, sensitive));
	CLEAN_COM(result);
}

// 标签策略  检测结果是否只返回标签策略相关内容 
// 1 普通策略勾选与不勾选 -> 勾选 1  不勾选 -> 正常返回json 并且 getStrategyMatchedType == 1
// 2 普通策略与标签策略并存 -> 勾选 getStrategyMatchedType == 2 不勾选 getStrategyMatchedType == 3
// onlyReturnTagRespond 第一个int 1 勾选 第二个int 1 普通策略  2 标签策略 3 都存在类型
TEST_F(LdfcrFixture, onlyReturnTagRespond)
{
	string testName = ::testing::UnitTest::GetInstance()->current_test_info()->name();
	if (!JudgeFileJoinTest(testName))
		return;

	dlplog_info(g_log_handle, "[%d] ===========================================TEST_F onlyReturnTagRespond is started===========================================", __LINE__);
	// 普通测试案例 nihao
	std::string completePath = GetCompletePath("/test/data/nihao.data");
	dlplog_info(g_log_handle, "[%d] %s", __LINE__, completePath.c_str());

	EXPECT_EQ(ldfcr.init(_T("test/data/nihao.data")), 0);
	EXPECT_TRUE(ldfcr.onlyReturnTagRespond(_T("test/nihao.txt"),0,1));
	EXPECT_FALSE(ldfcr.onlyReturnTagRespond(_T("test/nihao.txt"),1,1));

	// 存在普通测试案例和标签测试案例
	completePath = GetCompletePath("/test/data/strategy_tagwithnormal.data");
	dlplog_info(g_log_handle, "[%d] %s", __LINE__, completePath.c_str());

	EXPECT_EQ(ldfcr.init(_T("test/data/strategy_tagwithnormal.data")), 0);
	EXPECT_TRUE(ldfcr.onlyReturnTagRespond(_T("test/tagwithnormalTest1.txt"),0,3));
	EXPECT_TRUE(ldfcr.onlyReturnTagRespond(_T("test/tagwithnormalTest1.txt"),1,2));
}

#ifdef WIN32
// 标签策略 传入普通策略与标签策略进行合并，返回合并的策略 ldfcr_MergeStrategyW
//TEST_F(LdfcrFixture, ReturnMergeStrategyW)
//{
//	string testName = ::testing::UnitTest::GetInstance()->current_test_info()->name();
//	if (!JudgeFileJoinTest(testName))
//		return;
//
//	dlplog_info(g_log_handle, "[%d] ===========================================TEST_F ReturnMergeStrategy is started===========================================", __LINE__);
//	std::string completePath1 = GetCompletePath("/test/data/hahaha.data");
//	std::string completePath2 = GetCompletePath("/test/data/TagStrategy.data");
//	dlplog_info(g_log_handle, "[%d] %s", __LINE__, completePath1.c_str());
//	dlplog_info(g_log_handle, "[%d] %s", __LINE__, completePath2.c_str());
//	std::wstring wstrRegularDataPath = StringToWstring(completePath1);
//	std::wstring wstrTagDataPath = StringToWstring(completePath2);
//
//	EXPECT_EQ(ldfcr.init(_T("test/data/hahaha.data")), 0); // 策略id=11
//	EXPECT_EQ(ldfcr.init(_T("test/data/TagStrategy.data")), 0);// 策略id=12
//	EXPECT_TRUE(ldfcr.GetMergeStrategyW(wstrRegularDataPath.c_str(), wstrTagDataPath.c_str(), 11, 12));
//}

// 标签策略 传入普通策略与标签策略进行合并，返回合并的策略 ldfcr_MergeStrategy
TEST_F(LdfcrFixture, ReturnMergeStrategy)
{
	string testName = ::testing::UnitTest::GetInstance()->current_test_info()->name();
	if (!JudgeFileJoinTest(testName))
		return;

	dlplog_info(g_log_handle, "[%d] ===========================================TEST_F ReturnMergeStrategy is started===========================================", __LINE__);
	std::string completePath1 = GetCompletePath("/test/data/hahaha.data");
	std::string completePath2 = GetCompletePath("/test/data/TagStrategy.data");
	dlplog_info(g_log_handle, "[%d] %s", __LINE__, completePath1.c_str());
	dlplog_info(g_log_handle, "[%d] %s", __LINE__, completePath2.c_str());

	EXPECT_EQ(ldfcr.init(_T("test/data/hahaha.data")), 0); // 策略id=11
	EXPECT_EQ(ldfcr.init(_T("test/data/TagStrategy.data")), 0);// 策略id=12
	EXPECT_TRUE(ldfcr.GetMergeStrategy(_T("test/data/hahaha.data"), _T("test/data/TagStrategy.data"), 11, 12));
}
#endif

//标签策略 获取匹配策略中的策略类型，第一位为普通策略，第二位为标签策略。 即1为匹配到普通策略，2为匹配到标签策略，3为都匹配到。
TEST_F(LdfcrFixture, getStrategyMatchedType)
{
	string testName = ::testing::UnitTest::GetInstance()->current_test_info()->name();
	if (!JudgeFileJoinTest(testName))
		return;

	dlplog_info(g_log_handle, "[%d] ===========================================TEST_F getStrategyMatchedType is started===========================================", __LINE__);
	std::string completePath = GetCompletePath("/test/data/RegularAndTagKW.data");
	dlplog_info(g_log_handle, "[%d] %s", __LINE__, completePath.c_str());

	EXPECT_EQ(ldfcr.init(_T("test/data/RegularAndTagKW2.data")), 0);// 包含普通策略和标签策略
	EXPECT_TRUE(ldfcr.getStrategyMatchedType(_T("test/moreEasy.txt"), 1));// 匹配普通策略
	EXPECT_TRUE(ldfcr.getStrategyMatchedType(_T("test/hahaha_tag.txt"), 2));// 匹配标签策略
	EXPECT_TRUE(ldfcr.getStrategyMatchedType(_T("test/data/RegularAndTagKW2.data"), 3));// 普通和标签策略都匹配

}

// 获取文件类型 getFileExt_doc
TEST_F(LdfcrFixture, getFileExt_doc)
{
	string testName = ::testing::UnitTest::GetInstance()->current_test_info()->name();
	if (!JudgeFileJoinTest(testName))
		return;

	dlplog_info(g_log_handle, "[%d] ===========================================TEST_F getFileExt_doc is started===========================================", __LINE__);
	std::string completePath = GetCompletePath("/test/data/FileExt7.data");
	dlplog_info(g_log_handle, "[%d] %s", __LINE__, completePath.c_str());

	EXPECT_EQ(ldfcr.init(_T("test/data/FileExt7.data")), 0);
	IFCRResult *result = NULL;
	EXPECT_TRUE(ldfcr.detect(_T("test/FileExt/FileExt.doc"),(void**)&result,NULL,0,NULL,Ldfcr::_enTcrOpen::CLOSE)); //DOC文件类型
	EXPECT_TRUE(ldfcr.getFileExt(result, "doc")); //DOC文件类型
	CLEAN_COM(result);
}

// 获取文件类型 getFileExt_SLDPRT
TEST_F(LdfcrFixture, getFileExt_SLDPRT)
{
	string testName = ::testing::UnitTest::GetInstance()->current_test_info()->name();
	if (!JudgeFileJoinTest(testName))
		return;

	dlplog_info(g_log_handle, "[%d] ===========================================TEST_F getFileExt_SLDPRT is started===========================================", __LINE__);
	std::string completePath = GetCompletePath("/test/data/FileExt7.data");
	dlplog_info(g_log_handle, "[%d] %s", __LINE__, completePath.c_str());
	EXPECT_EQ(ldfcr.init(_T("test/data/FileExt7.data")), 0);
	IFCRResult *result = NULL;
	EXPECT_TRUE(ldfcr.detect(_T("test/FileExt/FileExt.SLDPRT"), (void**)&result, NULL, 0, NULL, Ldfcr::_enTcrOpen::CLOSE)); 
	EXPECT_TRUE(ldfcr.getFileExt(result, "sldprt")); //OLE文件类型 OLE复合文档
	CLEAN_COM(result);
}

// 获取文件类型 getFileExt_rtf
TEST_F(LdfcrFixture, getFileExt_rtf)
{
	string testName = ::testing::UnitTest::GetInstance()->current_test_info()->name();
	if (!JudgeFileJoinTest(testName))
		return;

	dlplog_info(g_log_handle, "[%d] ===========================================TEST_F getFileExt_rtf is started===========================================", __LINE__);
	std::string completePath = GetCompletePath("/test/data/FileExt7.data");
	dlplog_info(g_log_handle, "[%d] %s", __LINE__, completePath.c_str());
	EXPECT_EQ(ldfcr.init(_T("test/data/FileExt7.data")), 0);
	IFCRResult *result = NULL;
	EXPECT_TRUE(ldfcr.detect(_T("test/FileExt/FileExt.rtf"), (void**)&result, NULL, 0, NULL, Ldfcr::_enTcrOpen::CLOSE));
	EXPECT_TRUE(ldfcr.getFileExt(result, "rtf")); //RTF文件类型
	CLEAN_COM(result);
}

// 获取文件类型 getFileExt_dwg
TEST_F(LdfcrFixture, getFileExt_dwg)
{
	string testName = ::testing::UnitTest::GetInstance()->current_test_info()->name();
	if (!JudgeFileJoinTest(testName))
		return;

	dlplog_info(g_log_handle, "[%d] ===========================================TEST_F getFileExt_dwg is started===========================================", __LINE__);
	std::string completePath = GetCompletePath("/test/data/FileExt7.data");
	dlplog_info(g_log_handle, "[%d] %s", __LINE__, completePath.c_str());
	EXPECT_EQ(ldfcr.init(_T("test/data/FileExt7.data")), 0);
	IFCRResult *result = NULL;
	EXPECT_TRUE(ldfcr.detect(_T("test/FileExt/FileExt.dwg"), (void**)&result, NULL, 0, NULL, Ldfcr::_enTcrOpen::CLOSE));
	EXPECT_TRUE(ldfcr.getFileExt(result, "dwg")); //DWG文件类型
	CLEAN_COM(result);
}

// 获取文件类型 getFileExt_rar
TEST_F(LdfcrFixture, getFileExt_rar)
{
	string testName = ::testing::UnitTest::GetInstance()->current_test_info()->name();
	if (!JudgeFileJoinTest(testName))
		return;

	dlplog_info(g_log_handle, "[%d] ===========================================TEST_F getFileExt_rar is started===========================================", __LINE__);
	std::string completePath = GetCompletePath("/test/data/FileExt7.data");
	dlplog_info(g_log_handle, "[%d] %s", __LINE__, completePath.c_str());
	EXPECT_EQ(ldfcr.init(_T("test/data/FileExt7.data")), 0);
	IFCRResult *result = NULL;
	EXPECT_TRUE(ldfcr.detect(_T("test/FileExt/FileExt.rar"), (void**)&result, NULL, 0, NULL, Ldfcr::_enTcrOpen::CLOSE));
	EXPECT_TRUE(ldfcr.getFileExt(result, "rar")); //RAR文件类型
	CLEAN_COM(result);
}

// 获取文件类型 getFileExt_xml
TEST_F(LdfcrFixture, getFileExt_xml)
{
	string testName = ::testing::UnitTest::GetInstance()->current_test_info()->name();
	if (!JudgeFileJoinTest(testName))
		return;

	dlplog_info(g_log_handle, "[%d] ===========================================TEST_F getFileExt_xml is started===========================================", __LINE__);
	std::string completePath = GetCompletePath("/test/data/FileExt7.data");
	dlplog_info(g_log_handle, "[%d] %s", __LINE__, completePath.c_str());
	EXPECT_EQ(ldfcr.init(_T("test/data/FileExt7.data")), 0);
	IFCRResult *result = NULL;
	EXPECT_TRUE(ldfcr.detect(_T("test/FileExt/FileExt.xml"), (void**)&result, NULL, 0, NULL, Ldfcr::_enTcrOpen::CLOSE));
	EXPECT_TRUE(ldfcr.getFileExt(result, "xml")); //XML文件类型
	CLEAN_COM(result);
}

// 获取文件类型 getFileExt_pdf
TEST_F(LdfcrFixture, getFileExt_pdf)
{
	string testName = ::testing::UnitTest::GetInstance()->current_test_info()->name();
	if (!JudgeFileJoinTest(testName))
		return;

	dlplog_info(g_log_handle, "[%d] ===========================================TEST_F getFileExt_pdf is started===========================================", __LINE__);
	std::string completePath = GetCompletePath("/test/data/FileExt7.data");
	dlplog_info(g_log_handle, "[%d] %s", __LINE__, completePath.c_str());
	EXPECT_EQ(ldfcr.init(_T("test/data/FileExt7.data")), 0);
	IFCRResult *result = NULL;
	EXPECT_TRUE(ldfcr.detect(_T("test/FileExt/FileExt.pdf"), (void**)&result, NULL, 0, NULL, Ldfcr::_enTcrOpen::CLOSE));
	EXPECT_TRUE(ldfcr.getFileExt(result, "pdf")); //PDF文件类型
	CLEAN_COM(result);
}

//关键字偏移量校对
TEST_F(LdfcrFixture, HightLight)
{
	string testName = ::testing::UnitTest::GetInstance()->current_test_info()->name();
	if (!JudgeFileJoinTest(testName))
		return;

	dlplog_info(g_log_handle, "[%d] ===========================================TEST_F HightLight is started===========================================", __LINE__);
	std::string completePath = GetCompletePath("/test/data/HighLightPos.data");//********
	dlplog_info(g_log_handle, "[%d] %s", __LINE__, completePath.c_str());

	EXPECT_EQ(ldfcr.init(_T("test/data/HighLightPos.data")), 0);

	IFCRResult *result = NULL;
	BOOL sensitive;
	EXPECT_TRUE(sensitive = ldfcr.detect(_T("test/keyword-lessthen4096.txt"),(void**)&result));
	EXPECT_TRUE(ldfcr.checkPos(result, sensitive));
	CLEAN_COM(result);

	EXPECT_TRUE(sensitive = ldfcr.detect(_T("test/keyword-morethen4096.txt"), (void**)&result));
	EXPECT_TRUE(ldfcr.checkPos(result, sensitive));
	CLEAN_COM(result);
}

// 简单的文件权重测试 2 + 2 = 4
TEST_F(LdfcrFixture, SingleGroupKWUnitWeight2TotalWeight4) 
{
	string testName = ::testing::UnitTest::GetInstance()->current_test_info()->name();
	if (!JudgeFileJoinTest(testName))
		return;

	dlplog_info(g_log_handle, "[%d] ===========================================TEST_F SingleGroupKWUnitWeight2TotalWeight4 is started===========================================", __LINE__);
	std::string completePath = GetCompletePath("/test/data/strategy.data");//********
	dlplog_info(g_log_handle, "[%d] %s", __LINE__, completePath.c_str());

	EXPECT_EQ(ldfcr.init(_T("test/data/strategy.data")), 0);
	EXPECT_FALSE(ldfcr.detect(_T("test/text1.txt"), nullptr, nullptr, 0, NULL, Ldfcr::_enTcrOpen::CLOSE));
	EXPECT_TRUE(ldfcr.detect(_T("test/text1.txt"), nullptr, nullptr, 0, NULL, Ldfcr::_enTcrOpen::CLOSE));
}

// 简单的文本权重测试 2 + 2 = 4
TEST_F(LdfcrFixture, SingleGroupKWUnitWeight2TotalWeight4_Text) 
{
	string testName = ::testing::UnitTest::GetInstance()->current_test_info()->name();
	if (!JudgeFileJoinTest(testName))
		return;

	dlplog_info(g_log_handle, "[%d] ===========================================TEST_F SingleGroupKWUnitWeight2TotalWeight4_Text is started===========================================", __LINE__);
	std::string completePath = GetCompletePath("/test/data/strategy.data");//********
	dlplog_info(g_log_handle, "[%d] %s", __LINE__, completePath.c_str());

	EXPECT_EQ(ldfcr.init(_T("test/data/strategy.data")), 0);
	EXPECT_FALSE(ldfcr.detect(_T("test/text1.txt"), nullptr, nullptr));
	EXPECT_TRUE(ldfcr.detect(_T("test/text1.txt"), nullptr, nullptr));
}

// 计数模式文件测试  17次正则匹配
TEST_F(LdfcrFixture, TelNumber) 
{
	string testName = ::testing::UnitTest::GetInstance()->current_test_info()->name();
	if (!JudgeFileJoinTest(testName))
		return;

	dlplog_info(g_log_handle, "[%d] ===========================================TEST_F TelNumber is started===========================================", __LINE__);
	std::string completePath = GetCompletePath("/test/tel/s.data");//********
	dlplog_info(g_log_handle, "[%d] %s", __LINE__, completePath.c_str());

	EXPECT_EQ(ldfcr.init(_T("test/tel/s.data")), 0);
	auto files = read_dir(_T("test/tel/data"));
	if(files.size())
	{
		for (size_t i = 0; i < files.size(); ++i)
		{
			EXPECT_FALSE(ldfcr.detect(files[i].c_str(), nullptr, nullptr, 0, NULL, Ldfcr::_enTcrOpen::CLOSE));
		}
		EXPECT_TRUE(ldfcr.detect(files.back().c_str(), nullptr, nullptr, 0, NULL, Ldfcr::_enTcrOpen::CLOSE));
	}
	
}

// 计数模式文本测试  17次正则匹配
TEST_F(LdfcrFixture, TelNumber_Text) 
{
	string testName = ::testing::UnitTest::GetInstance()->current_test_info()->name();
	if (!JudgeFileJoinTest(testName))
		return;

	dlplog_info(g_log_handle, "[%d] ===========================================TEST_F TelNumber_Text is started===========================================", __LINE__);
	std::string completePath = GetCompletePath("/test/tel/s.data");//********
	dlplog_info(g_log_handle, "[%d] %s", __LINE__, completePath.c_str());

	EXPECT_EQ(ldfcr.init(_T("test/tel/s.data")), 0);
	auto files = read_dir(_T("test/tel/data"));
	if(files.size())
	{
		for (size_t i = 0; i < files.size(); ++i)
		{

			EXPECT_FALSE(ldfcr.detect(files[i].c_str(), nullptr, nullptr));
		}
		EXPECT_TRUE(ldfcr.detect(files.back().c_str(), nullptr, nullptr));
	}
}

// 计数模式(去重)文件测试  17次(16次唯一)正则匹配
TEST_F(LdfcrFixture, TelNumberUnique) 
{
	string testName = ::testing::UnitTest::GetInstance()->current_test_info()->name();
	if (!JudgeFileJoinTest(testName))
		return;

	dlplog_info(g_log_handle, "[%d] ===========================================TEST_F TelNumberUnique is started===========================================", __LINE__);
	std::string completePath = GetCompletePath("/test/tel/s_unique.data");//********
	dlplog_info(g_log_handle, "[%d] %s", __LINE__, completePath.c_str());

	EXPECT_EQ(ldfcr.init(_T("test/tel/s_unique.data")), 0);
	auto files = read_dir(_T("test/tel/data"));
	if(files.size())
	{
		for (size_t i = 0; i < files.size() - 1; ++i)
		{
			EXPECT_FALSE(ldfcr.detect(files[i].c_str(), nullptr, nullptr, 0, NULL, Ldfcr::_enTcrOpen::CLOSE));
		}
		EXPECT_FALSE(ldfcr.detect(files.back().c_str(), nullptr, nullptr, 0, NULL, Ldfcr::_enTcrOpen::CLOSE));
	}
}

// 计数模式(去重)文本测试  17次(16次唯一)正则匹配
TEST_F(LdfcrFixture, TelNumberUnique_Text) 
{
	string testName = ::testing::UnitTest::GetInstance()->current_test_info()->name();
	if (!JudgeFileJoinTest(testName))
		return;

	dlplog_info(g_log_handle, "[%d] ===========================================TEST_F TelNumberUnique_Text is started===========================================", __LINE__);
	std::string completePath = GetCompletePath("/test/tel/s_unique.data");//********
	dlplog_info(g_log_handle, "[%d] %s", __LINE__, completePath.c_str());

	EXPECT_EQ(ldfcr.init(_T("test/tel/s_unique.data")), 0);
	auto files = read_dir(_T("test/tel/data"));
	if(files.size())
	{		
		for (size_t i = 0; i < files.size() - 1; ++i)
		{
			EXPECT_FALSE(ldfcr.detect(files[i].c_str(), nullptr, nullptr));
		}
		EXPECT_FALSE(ldfcr.detect(files.back().c_str(), nullptr, nullptr));
	}
}

// 计数模式未超时测试 - 文件 3次匹配，超时3s
TEST_F(LdfcrFixture, TelNumberInTime) 
{
	string testName = ::testing::UnitTest::GetInstance()->current_test_info()->name();
	if (!JudgeFileJoinTest(testName))
		return;

	dlplog_info(g_log_handle, "[%d] ===========================================TEST_F TelNumberInTime is started===========================================", __LINE__);
	std::string completePath = GetCompletePath("/test/tel/s_time.data");//********
	dlplog_info(g_log_handle, "[%d] %s", __LINE__, completePath.c_str());

	EXPECT_EQ(ldfcr.init(_T("test/tel/s_time.data")), 0);
	auto files = read_dir(_T("test/tel/data"));
	if(files.size())
	{
		for (size_t i = 0; i < 2; ++i)
		{
			EXPECT_FALSE(ldfcr.detect(files[i].c_str(), nullptr, nullptr, 0, NULL, Ldfcr::_enTcrOpen::CLOSE));
			LdfcrSleep(1);
		}
		EXPECT_TRUE(ldfcr.detect(files.back().c_str(), nullptr, nullptr, 0, NULL, Ldfcr::_enTcrOpen::CLOSE));
	}
}

// 计数模式未超时测试 - 文本 3次匹配，超时3s
TEST_F(LdfcrFixture, TelNumberInTime_Text) 
{
	string testName = ::testing::UnitTest::GetInstance()->current_test_info()->name();
	if (!JudgeFileJoinTest(testName))
		return;

	dlplog_info(g_log_handle, "[%d] ===========================================TEST_F TelNumberInTime_Text is started===========================================", __LINE__);
	std::string completePath = GetCompletePath("/test/tel/s_time.data");//********
	dlplog_info(g_log_handle, "[%d] %s", __LINE__, completePath.c_str());

	EXPECT_EQ(ldfcr.init(_T("test/tel/s_time.data")), 0);
	auto files = read_dir(_T("test/tel/data"));
	if(files.size())
	{
		for (size_t i = 0; i < 2; ++i)
		{	
			EXPECT_FALSE(ldfcr.detect(files[i].c_str(), nullptr, nullptr));
			LdfcrSleep(1);
		}
		EXPECT_TRUE(ldfcr.detect(files.back().c_str(), nullptr, nullptr));
	}
}

// 计数模式超时测试 - 文件 3次匹配，超时3s
TEST_F(LdfcrFixture, TelNumberOutOfTime) 
{
	string testName = ::testing::UnitTest::GetInstance()->current_test_info()->name();
	if (!JudgeFileJoinTest(testName))
		return;

	dlplog_info(g_log_handle, "[%d] ===========================================TEST_F TelNumberOutOfTime is started===========================================", __LINE__);
	std::string completePath = GetCompletePath("/test/tel/s_time.data");//********
	dlplog_info(g_log_handle, "[%d] %s", __LINE__, completePath.c_str());

	EXPECT_EQ(ldfcr.init(_T("test/tel/s_time.data")), 0);

	FCRPARAM fcrParam;
	fcrParam.use_all_rule = TRUE;   //启用所有规则
	fcrParam.target_class_code = 3; //达到或超过代码3，即停止
	fcrParam.output_detail = TRUE;  //要求返回时输出策略的细节信息
	fcrParam.devType = 1;
	fcrParam.opType = 3;
	fcrParam.fcrInfo = "{\"IP\": \"***********\"}";

	auto files = read_dir(_T("test/tel/data"));
	if(files.size())
	{
		for (size_t i = 0; i < 3; ++i)
		{
			EXPECT_FALSE(ldfcr.detect(files[i].c_str(), nullptr, nullptr, 0, &fcrParam, Ldfcr::_enTcrOpen::CLOSE));
			if (i + 1 < 3)
				LdfcrSleep(2);
		}
	}
}

// 计数模式超时测试 - 文本 3次匹配，超时3s
TEST_F(LdfcrFixture, TelNumberOutOfTime_Text) 
{
	string testName = ::testing::UnitTest::GetInstance()->current_test_info()->name();
	if (!JudgeFileJoinTest(testName))
		return;

	dlplog_info(g_log_handle, "[%d] ===========================================TEST_F TelNumberOutOfTime_Text is started===========================================", __LINE__);
	std::string completePath = GetCompletePath("/test/tel/s_time.data");//********
	dlplog_info(g_log_handle, "[%d] %s", __LINE__, completePath.c_str());

	EXPECT_EQ(ldfcr.init(_T("test/tel/s_time.data")), 0);

	FCRPARAM fcrParam;
	fcrParam.use_all_rule = TRUE;   //启用所有规则
	fcrParam.target_class_code = 3; //达到或超过代码3，即停止
	fcrParam.output_detail = TRUE;  //要求返回时输出策略的细节信息
	fcrParam.devType = 1;
	fcrParam.opType = 3;
	fcrParam.fcrInfo = "{\"IP\": \"***********\"}";

	auto files = read_dir(_T("test/tel/data"));
	if(files.size())
	{
		for (size_t i = 0; i < 3; ++i)
		{
			EXPECT_FALSE(ldfcr.detect(files[i].c_str(), nullptr, nullptr,0,&fcrParam));
			if (i + 1 < 3)
				LdfcrSleep(2);
		}
	}
}
#endif

/* =====================================================2023 8 15
这几个单线程测试都存在问题，不管windows还是linux，
因为启动脚本带了多线程用脚本启动这段代码是不运行的要单独运行程序才会，
但是测试是过不了的之前可能都没发现这个问题，
目前先注释后续需要处理再核对
=====================================================*/
//const int num = 16;
//
//#if MULTITHREAD == 1
// 零星检测多线程测试 - 正则
//TEST_F(LdfcrFixture, RegexMultiThreadsTest) {
//	dlplog_info(g_log_handle, "[%d] ===========================================TEST_F RegexMultiThreadsTest is started===========================================",__LINE__);
//   std::string completePath = GetCompletePath("/test/multithread_test/s_regexMT.data");//********
//	dlplog_info(g_log_handle, "[%d] %s", __LINE__, completePath.c_str());
//
//	EXPECT_EQ(ldfcr.init(_T("test/multithread_test/s_regexMT.data")), 0);	
//    auto files = read_dir(_T("test/tel/data"));
//    EXPECT_EQ(files.size() - num, 1);
//
//  clock_t start = clock();
//  double total_eclapsed = 0;
//
//    size_t i = 0;
//    while (i++ < loop)
//    {
//        std::promise<BOOL> promises[num];
//        std::vector<std::thread> threads(num);
//        std::vector<std::future<BOOL>> fvec;
//
//        for (size_t j = 0; j < num; ++j)
//        {
//            fvec.push_back(promises[j].get_future());
//            threads[j] = std::thread([&, j]()->BOOL {return ldfcr.detect_promise(files[j].c_str(), nullptr, nullptr, 0, std::move(promises[j])); });
//        }
//        for (size_t j = 0; j < fvec.size(); ++j)
//        {
//            threads[j].join();
//            EXPECT_FALSE(fvec[j].get());
//        }
//
//        BOOL b = ldfcr.detect(files[num-1].c_str(), nullptr, nullptr, 0);
//        EXPECT_TRUE(b);
//    }
//}
//
//#endif
//
//#if MULTITHREAD == 1
// 零星检测多线程测试 - 单关键字
//TEST_F(LdfcrFixture, KWSingleMultiThreadsTest) {
//	dlplog_info(g_log_handle, "[%d] ===========================================TEST_F KWSingleMultiThreadsTest is started===========================================",__LINE__);
//    std::string completePath = GetCompletePath("/test/multithread_test/singlekw.data");//********
//	dlplog_info(g_log_handle, "[%d] %s", __LINE__, completePath.c_str());
//
//	EXPECT_EQ(ldfcr.init(_T("test/multithread_test/singlekw.data")), 0);
//    auto files = read_dir(_T("test/multithread_test/data"));
//    EXPECT_EQ(files.size() - num, 1);
//
//  clock_t start = clock();
//  double total_eclapsed = 0;
//
//    size_t i = 0;
//    while (i++ < loop)
//    {
//        std::promise<BOOL> promises[num];
//        std::vector<std::thread> threads(num);
//        std::vector<std::future<BOOL>> fvec;
//
//        for (size_t j = 0; j < num; ++j)
//        {
//            fvec.push_back(promises[j].get_future());
//            threads[j] = std::thread([&, j]()->BOOL{return ldfcr.detect_promise(files[j].c_str(), nullptr, nullptr, 0, std::move(promises[j]));});
//        }
//        for (size_t j = 0; j < fvec.size(); ++j)
//        {
//            threads[j].join();
//            EXPECT_FALSE(fvec[j].get());
//        }
//
//        BOOL b = ldfcr.detect(files[num-1].c_str(), nullptr, nullptr, 0);
//        EXPECT_TRUE(b);
//    }
//}
//#endif
//
//#if MULTITHREAD == 1
// 零星检测多线程测试 - 关键字对
//TEST_F(LdfcrFixture, KWPairMultiThreadsTest) {
//	dlplog_info(g_log_handle, "[%d] ===========================================TEST_F KWPairMultiThreadsTest is started===========================================",__LINE__);
//    std::string completePath = GetCompletePath("/test/multithread_test/pairkw.data");//********
//	dlplog_info(g_log_handle, "[%d] %s", __LINE__, completePath.c_str());
//
//	EXPECT_EQ(ldfcr.init(_T("test/multithread_test/pairkw.data")), 0);	
//    auto files = read_dir(_T("test/multithread_test/data"));
//    EXPECT_EQ(files.size() - num, 1);
//
//    size_t i = 0;
//  clock_t start = clock();
//  double total_eclapsed = 0;
//    while (i++ < loop)
//    {
//        std::vector<std::future<BOOL>> fvec;
//        std::promise<BOOL> promises[num];
//        std::vector<std::thread> threads(num);
//        for (size_t j = 0; j < num; ++j)
//        {
//            fvec.push_back(promises[j].get_future());
//            threads[j] = std::thread([&, j](){return ldfcr.detect_promise(files[j].c_str(), nullptr, nullptr, 0, std::move(promises[j]));});
//        }
//        for (size_t j = 0; j < fvec.size(); ++j)
//        {
//            threads[j].join();
//            EXPECT_FALSE(fvec[j].get());
//        }
//        fvec.clear();
//        BOOL b = FALSE;
//        b = ldfcr.detect(files[num-1].c_str(), nullptr, nullptr, 0);
//        EXPECT_TRUE(b);
//    }
//}
//#endif
//
//#if MULTITHREAD == 1
// 零星检测多线程测试 - 关键字组
//TEST_F(LdfcrFixture, KWGroupMultiThreadsTest) {
//	dlplog_info(g_log_handle, "[%d] ===========================================TEST_F KWGroupMultiThreadsTest is started===========================================",__LINE__);
//   std::string completePath = GetCompletePath("/test/multithread_test/groupkw.data");//********
//	dlplog_info(g_log_handle, "[%d] %s", __LINE__, completePath.c_str());
//
//	EXPECT_EQ(ldfcr.init(_T("test/multithread_test/groupkw.data")), 0);
//    auto files = read_dir(_T("test/multithread_test/data"));
//    EXPECT_EQ(files.size() - num, 1);
//
//  clock_t start = clock();
//  double total_eclapsed = 0;
//    size_t i = 0;
//    while (i++ < loop)
//    {
//        std::vector<std::future<BOOL>> fvec;
//        std::promise<BOOL> promises[num];
//        std::vector<std::thread> threads(num);
//        for (size_t j = 0; j < num; ++j)
//        {
//            fvec.push_back(promises[j].get_future());
//            threads[j] = std::thread([&, j](){return ldfcr.detect_promise(files[j].c_str(), nullptr, nullptr, 0, std::move(promises[j]));});
//        }
//        for (size_t j = 0; j < fvec.size(); ++j)
//        {
//            threads[j].join();
//            EXPECT_FALSE(fvec[j].get());
//        }
//        fvec.clear();
//        BOOL b = FALSE;
//        b = ldfcr.detect(files[num-1].c_str(), nullptr, nullptr, 0);
//        EXPECT_TRUE(b);
//    }
//}
//
//#endif

#if 1

// 零星检测权重模式累积测试 - 文件重复检测100次
TEST_F(LdfcrFixture, 50GroupKWUnitWeight2TotalWeight4) 
{
	string testName = ::testing::UnitTest::GetInstance()->current_test_info()->name();
	if (!JudgeFileJoinTest(testName))
		return;

	dlplog_info(g_log_handle, "[%d] ===========================================TEST_F GroupKWUnitWeight2TotalWeight4 is started===========================================", __LINE__);
	std::string completePath = GetCompletePath("/test/data/strategy.data");//********
	dlplog_info(g_log_handle, "[%d] %s", __LINE__, completePath.c_str());

	EXPECT_EQ(ldfcr.init(_T("test/data/strategy.data")), 0);
	for (auto i = 0; i < 100; i++)
	{
		if (i % 2)
			EXPECT_TRUE(ldfcr.detect(_T("test/text1.txt"), nullptr, nullptr, 0, NULL, Ldfcr::_enTcrOpen::CLOSE));
		else
			EXPECT_FALSE(ldfcr.detect(_T("test/text1.txt"), nullptr, nullptr, 0, NULL, Ldfcr::_enTcrOpen::CLOSE));
	}
}

// 零星检测权重模式累积测试 - 文本重复检测100次
TEST_F(LdfcrFixture, 50GroupKWUnitWeight2TotalWeight4_Text) 
{
	string testName = ::testing::UnitTest::GetInstance()->current_test_info()->name();
	if (!JudgeFileJoinTest(testName))
		return;

	dlplog_info(g_log_handle, "[%d] ===========================================TEST_F GroupKWUnitWeight2TotalWeight4_Text is started===========================================", __LINE__);
	std::string completePath = GetCompletePath("/test/data/strategy.data");//********
	dlplog_info(g_log_handle, "[%d] %s", __LINE__, completePath.c_str());

	EXPECT_EQ(ldfcr.init(_T("test/data/strategy.data")), 0);
	for (auto i = 0; i < 100; i++)
	{
		if (i % 2)
			EXPECT_TRUE(ldfcr.detect(_T("test/text1.txt"), nullptr, nullptr));
		else
			EXPECT_FALSE(ldfcr.detect(_T("test/text1.txt"), nullptr, nullptr));
	}
}

// 零星检测操作类型测试 - 文件 操作类型列表之外的类型
TEST_F(LdfcrFixture, OtherOp) 
{
	string testName = ::testing::UnitTest::GetInstance()->current_test_info()->name();
	if (!JudgeFileJoinTest(testName))
		return;

	dlplog_info(g_log_handle, "[%d] ===========================================TEST_F OtherOp is started===========================================", __LINE__);
	std::string completePath = GetCompletePath("/test/data/strategy.data");//********
	dlplog_info(g_log_handle, "[%d] %s", __LINE__, completePath.c_str());

	EXPECT_EQ(ldfcr.init(_T("test/data/strategy.data")), 0);
	for (auto i = 0; i < 100; i++)
	{
		EXPECT_FALSE(ldfcr.detect(_T("test/text1.txt"), nullptr, nullptr, 100, NULL, Ldfcr::_enTcrOpen::CLOSE));
	}
}

// 零星检测操作类型测试 - 文本 操作类型列表之外的类型
TEST_F(LdfcrFixture, OtherOp_Text) 
{
	string testName = ::testing::UnitTest::GetInstance()->current_test_info()->name();
	if (!JudgeFileJoinTest(testName))
		return;

	dlplog_info(g_log_handle, "[%d] ===========================================TEST_F OtherOp_Text is started===========================================", __LINE__);
	std::string completePath = GetCompletePath("/test/data/strategy.data");//********
	dlplog_info(g_log_handle, "[%d] %s", __LINE__, completePath.c_str());

	EXPECT_EQ(ldfcr.init(_T("test/data/strategy.data")), 0);
	for (auto i = 0; i < 100; i++)
	{
		EXPECT_FALSE(ldfcr.detect(_T("test/text1.txt"), nullptr, nullptr, 100));
	}
}

// 零星检测 - 文件 结果只有零星
TEST_F(LdfcrFixture, OnlyDrip) 
{
	string testName = ::testing::UnitTest::GetInstance()->current_test_info()->name();
	if (!JudgeFileJoinTest(testName))
		return;

	dlplog_info(g_log_handle, "[%d] ===========================================TEST_F OnlyDrip is started===========================================", __LINE__);
	std::string completePath = GetCompletePath("/test/data/strategy.data_single");//********
	dlplog_info(g_log_handle, "[%d] %s", __LINE__, completePath.c_str());

	EXPECT_EQ(ldfcr.init(_T("test/data/strategy.data_single")), 0);
	IFCRResult *result = NULL;
	IFCRDripResult *dripResult = NULL;

	EXPECT_TRUE(ldfcr.detect(_T("test/text1.txt"), (void **)&result, (void **)&dripResult, 0, NULL, Ldfcr::_enTcrOpen::CLOSE));
	ASSERT_TRUE(dripResult != NULL);
	EXPECT_TRUE(IsResultEmpty(result));
	EXPECT_FALSE(IsResultEmpty(dripResult));
	CLEAN_UP(result, dripResult);
}

// 零星检测 - 文本 结果只有零星
TEST_F(LdfcrFixture, OnlyDrip_Text) 
{
	string testName = ::testing::UnitTest::GetInstance()->current_test_info()->name();
	if (!JudgeFileJoinTest(testName))
		return;

	dlplog_info(g_log_handle, "[%d] ===========================================TEST_F OnlyDrip_Text is started===========================================", __LINE__);
	std::string completePath = GetCompletePath("/test/data/strategy.data_single");//********
	dlplog_info(g_log_handle, "[%d] %s", __LINE__, completePath.c_str());

	EXPECT_EQ(ldfcr.init(_T("test/data/strategy.data_single")), 0);
	IFCRResult *result = NULL;
	IFCRDripResult *dripResult = NULL;
	EXPECT_TRUE(ldfcr.detect(_T("test/text1.txt"), (void **)&result, (void **)&dripResult));
	ASSERT_TRUE(dripResult != nullptr);
	EXPECT_TRUE(IsResultEmpty(result));
	EXPECT_FALSE(IsResultEmpty(dripResult));
	CLEAN_UP(result, dripResult);
}

// 普通检测 - 文件 结果只有常规
TEST_F(LdfcrFixture, OnlyNormal) 
{
	string testName = ::testing::UnitTest::GetInstance()->current_test_info()->name();
	if (!JudgeFileJoinTest(testName))
		return;

	dlplog_info(g_log_handle, "[%d] ===========================================TEST_F OnlyNormal is started===========================================", __LINE__);
	std::string completePath = GetCompletePath("/test/data/strategy.data_single_normal");//********
	dlplog_info(g_log_handle, "[%d] %s", __LINE__, completePath.c_str());

	EXPECT_EQ(ldfcr.init(_T("test/data/strategy.data_single_normal")), 0);
	IFCRResult *result = NULL;
	IFCRDripResult *dripResult = NULL;
	EXPECT_TRUE(ldfcr.detect(_T("test/text1.txt"), (void **)&result, (void **)&dripResult, 0, NULL, Ldfcr::_enTcrOpen::CLOSE));
	EXPECT_FALSE(IsResultEmpty(result));
	EXPECT_TRUE(IsResultEmpty(dripResult));
	CLEAN_UP(result, dripResult);
}

// 普通检测 - 文本 结果只有常规
TEST_F(LdfcrFixture, OnlyNormal_Text) 
{
	string testName = ::testing::UnitTest::GetInstance()->current_test_info()->name();
	if (!JudgeFileJoinTest(testName))
		return;

	dlplog_info(g_log_handle, "[%d] ===========================================TEST_F OnlyNormal_Text is started===========================================", __LINE__);
	std::string completePath = GetCompletePath("/test/data/strategy.data_single_normal");//********
	dlplog_info(g_log_handle, "[%d] %s", __LINE__, completePath.c_str());

	EXPECT_EQ(ldfcr.init(_T("test/data/strategy.data_single_normal")), 0);
	IFCRResult *result = NULL;
	IFCRDripResult *dripResult = NULL;
	EXPECT_TRUE(ldfcr.detect(_T("test/text1.txt"), (void **)&result, (void **)&dripResult));
	EXPECT_FALSE(IsResultEmpty(result));
	EXPECT_TRUE(IsResultEmpty(dripResult));
	CLEAN_UP(result, dripResult);
}

// 结果只有常规检测 - 重复 4096/6 次
TEST_F(LdfcrFixture, OnlyNormalTrimText) 
{
	string testName = ::testing::UnitTest::GetInstance()->current_test_info()->name();
	if (!JudgeFileJoinTest(testName))
		return;

	dlplog_info(g_log_handle, "[%d] ===========================================TEST_F OnlyNormalTrimText is started===========================================", __LINE__);
	std::string completePath = GetCompletePath("/test/data/strategy.data_single_normal");//********
	dlplog_info(g_log_handle, "[%d] %s", __LINE__, completePath.c_str());

	EXPECT_EQ(ldfcr.init(_T("test/data/strategy.data_single_normal")), 0);
	IFCRResult *result = NULL;
	IFCRDripResult *dripResult = NULL;

	long size = 0;
	char *content = getFileContent(_T("test/texttrimed"), &size);
	ASSERT_NE(content, nullptr);

	for (int i = 6; i < 4096; i++)
	{
		EXPECT_TRUE(ldfcr.detect_text(content, i, (void **)&result, (void **)&dripResult));
		EXPECT_FALSE(IsResultEmpty(result));
		EXPECT_TRUE(IsResultEmpty(dripResult));
		CLEAN_UP(result, dripResult);
	}

	delete[] content;
}

// 常规和零星检测都触发 - 文件
TEST_F(LdfcrFixture, NormalAndDrip) 
{
	string testName = ::testing::UnitTest::GetInstance()->current_test_info()->name();
	if (!JudgeFileJoinTest(testName))
		return;

	dlplog_info(g_log_handle, "[%d] ===========================================TEST_F NormalAndDrip is started===========================================", __LINE__);
	std::string completePath = GetCompletePath("/test/data/strategy.data_single_both");//********
	dlplog_info(g_log_handle, "[%d] %s", __LINE__, completePath.c_str());

	EXPECT_EQ(ldfcr.init(_T("test/data/strategy.data_single_both")), 0);
	IFCRResult *result = NULL;
	IFCRDripResult *dripResult = NULL;
	EXPECT_TRUE(ldfcr.detect(_T("test/text1.txt"), (void **)&result, (void **)&dripResult, 0, NULL, Ldfcr::_enTcrOpen::CLOSE));
	EXPECT_FALSE(IsResultEmpty(result));
	EXPECT_FALSE(IsResultEmpty(dripResult));
	CLEAN_UP(result, dripResult);
}

// 常规和零星检测都触发 - 文本
TEST_F(LdfcrFixture, NormalAndDrip_Text) 
{
	string testName = ::testing::UnitTest::GetInstance()->current_test_info()->name();
	if (!JudgeFileJoinTest(testName))
		return;

	dlplog_info(g_log_handle, "[%d] ===========================================TEST_F NormalAndDrip_Text is started===========================================", __LINE__);
	std::string completePath = GetCompletePath("/test/data/strategy.data_single_both");//********
	dlplog_info(g_log_handle, "[%d] %s", __LINE__, completePath.c_str());

	EXPECT_EQ(ldfcr.init(_T("test/data/strategy.data_single_both")), 0);
	IFCRResult *result = NULL;
	IFCRDripResult *dripResult = NULL;
	EXPECT_TRUE(ldfcr.detect(_T("test/text1.txt"), (void **)&result, (void **)&dripResult));
	EXPECT_FALSE(IsResultEmpty(result));
	EXPECT_FALSE(IsResultEmpty(dripResult));
	CLEAN_UP(result, dripResult);
}

// 常规检测 -文件 测试 UTF-8 文本截断是否正常
TEST_F(LdfcrFixture, UTF8Crop) 
{
	string testName = ::testing::UnitTest::GetInstance()->current_test_info()->name();
	if (!JudgeFileJoinTest(testName))
		return;

	dlplog_info(g_log_handle, "[%d] ===========================================TEST_F UTF8Crop is started===========================================", __LINE__);
	std::string completePath = GetCompletePath("/test/data/strategy_utf8_crop.data");//********
	dlplog_info(g_log_handle, "[%d] %s", __LINE__, completePath.c_str());

	EXPECT_EQ(ldfcr.init(_T("test/data/strategy_utf8_crop.data")), 0);
	IFCRDripResult *dripResult = NULL;
	BOOL sensitive;
	EXPECT_TRUE(sensitive = ldfcr.detect(_T("test/关键字.txt"), nullptr, (void **)&dripResult, 0, NULL, Ldfcr::_enTcrOpen::CLOSE));
	EXPECT_TRUE(ldfcr.checkUTF8Crop(dripResult, sensitive));
	CLEAN_DRIP(dripResult);
}

// 常规检测 - 文本 测试 UTF-8 文本截断是否正常
TEST_F(LdfcrFixture, UTF8Crop_Text) 
{
	string testName = ::testing::UnitTest::GetInstance()->current_test_info()->name();
	if (!JudgeFileJoinTest(testName))
		return;

	dlplog_info(g_log_handle, "[%d] ===========================================TEST_F UTF8Crop_Text is started===========================================", __LINE__);
	std::string completePath = GetCompletePath("/test/data/strategy_utf8_crop.data");//********
	dlplog_info(g_log_handle, "[%d] %s", __LINE__, completePath.c_str());

	EXPECT_EQ(ldfcr.init(_T("test/data/strategy_utf8_crop.data")), 0);
	IFCRDripResult *dripResult = NULL;
	BOOL sensitive;
	//LdfcrSleep(20);
	EXPECT_TRUE(sensitive = ldfcr.detect(_T("test/keywords_utf8.txt"), nullptr, (void **)&dripResult));
	EXPECT_TRUE(ldfcr.checkUTF8Crop(dripResult, sensitive));
	CLEAN_DRIP(dripResult);
}

// 零星检测 - 文件 测试 UTF-8 文本截断是否正常
TEST_F(LdfcrFixture, UTF8Crop2) {
	string testName = ::testing::UnitTest::GetInstance()->current_test_info()->name();
	if (!JudgeFileJoinTest(testName))
		return;

	dlplog_info(g_log_handle, "[%d] ===========================================TEST_F UTF8Crop2 is started===========================================", __LINE__);
	std::string completePath = GetCompletePath("/test/data/strategy_utf8_crop.data");//********
	dlplog_info(g_log_handle, "[%d] %s", __LINE__, completePath.c_str());

	EXPECT_EQ(ldfcr.init(_T("test/data/strategy_utf8_crop.data")), 0);
	IFCRDripResult *dripResult = NULL;
	BOOL sensitive;
	EXPECT_TRUE(sensitive = ldfcr.detect(_T("test/words.txt"), nullptr, (void **)&dripResult, 0, NULL, Ldfcr::_enTcrOpen::CLOSE));
	EXPECT_TRUE(ldfcr.checkUTF8Crop(dripResult, sensitive));
	CLEAN_DRIP(dripResult);
}

// 零星检测 - 文本 测试 UTF-8 文本截断是否正常
TEST_F(LdfcrFixture, UTF8Crop_Text2) {
	string testName = ::testing::UnitTest::GetInstance()->current_test_info()->name();
	if (!JudgeFileJoinTest(testName))
		return;

	dlplog_info(g_log_handle, "[%d] ===========================================TEST_F UTF8Crop_Text2 is started===========================================", __LINE__);
	std::string completePath = GetCompletePath("/test/data/strategy_utf8_crop.data");//********
	dlplog_info(g_log_handle, "[%d] %s", __LINE__, completePath.c_str());

	EXPECT_EQ(ldfcr.init(_T("test/data/strategy_utf8_crop.data")), 0);
	IFCRDripResult *dripResult = NULL;
	BOOL sensitive;
	EXPECT_TRUE(sensitive = ldfcr.detect(_T("test/words_utf8.txt"), nullptr, (void **)&dripResult));
	EXPECT_TRUE(ldfcr.checkUTF8Crop(dripResult, sensitive));
	CLEAN_DRIP(dripResult);
}

// 只包含零星检测 - 文件 并且内容非空
TEST_F(LdfcrFixture, OnlyDripWithResult) 
{
	string testName = ::testing::UnitTest::GetInstance()->current_test_info()->name();
	if (!JudgeFileJoinTest(testName))
		return;

	dlplog_info(g_log_handle, "[%d] ===========================================TEST_F OnlyDripWithResult is started===========================================", __LINE__);
	std::string completePath = GetCompletePath("/test/data/strategy.data_single");//********
	dlplog_info(g_log_handle, "[%d] %s", __LINE__, completePath.c_str());

	EXPECT_EQ(ldfcr.init(_T("test/data/strategy.data_single")), 0);
	IFCRResult *result = NULL;
	IFCRDripResult *dripResult = NULL;
	EXPECT_TRUE(ldfcr.detect(_T("test/text1.txt"), (void **)&result, (void **)&dripResult, 0, NULL, Ldfcr::_enTcrOpen::CLOSE));
	EXPECT_TRUE(IsResultEmpty(result));
	EXPECT_FALSE(IsResultEmpty(dripResult));
	EXPECT_TRUE(ResultHasContent(dripResult));
	CLEAN_UP(result, dripResult);
}

// 只包含零星检测 - 文本 并且内容非空
TEST_F(LdfcrFixture, OnlyDrip_TextWithResult) 
{
	string testName = ::testing::UnitTest::GetInstance()->current_test_info()->name();
	if (!JudgeFileJoinTest(testName))
		return;

	dlplog_info(g_log_handle, "[%d] ===========================================TEST_F OnlyDrip_TextWithResult is started===========================================", __LINE__);
	std::string completePath = GetCompletePath("/test/data/strategy.data_single");//********
	dlplog_info(g_log_handle, "[%d] %s", __LINE__, completePath.c_str());

	EXPECT_EQ(ldfcr.init(_T("test/data/strategy.data_single")), 0);
	IFCRResult *result = NULL;
	IFCRDripResult *dripResult = NULL;
	EXPECT_TRUE(ldfcr.detect(_T("test/text1.txt"), (void **)&result, (void **)&dripResult));
	ASSERT_TRUE(dripResult != nullptr);
	EXPECT_TRUE(IsResultEmpty(result));
	EXPECT_FALSE(IsResultEmpty(dripResult));
	EXPECT_TRUE(ResultHasContent(dripResult));
	CLEAN_UP(result, dripResult);
}

// 只包含常规检测 - 文件 并且内容非空
TEST_F(LdfcrFixture, OnlyNormalWithResult) 
{
	string testName = ::testing::UnitTest::GetInstance()->current_test_info()->name();
	if (!JudgeFileJoinTest(testName))
		return;

	dlplog_info(g_log_handle, "[%d] ===========================================TEST_F OnlyNormalWithResult is started===========================================", __LINE__);
	std::string completePath = GetCompletePath("/test/data/strategy.data_single_normal");//********
	dlplog_info(g_log_handle, "[%d] %s", __LINE__, completePath.c_str());

	EXPECT_EQ(ldfcr.init(_T("test/data/strategy.data_single_normal")), 0);
	IFCRResult *result = NULL;
	IFCRDripResult *dripResult = NULL;
	EXPECT_TRUE(ldfcr.detect(_T("test/text1.txt"), (void **)&result, (void **)&dripResult, 0, NULL, Ldfcr::_enTcrOpen::CLOSE));
	EXPECT_FALSE(IsResultEmpty(result));
	EXPECT_TRUE(IsResultEmpty(dripResult));
	EXPECT_TRUE(ResultHasContent(result));
	CLEAN_UP(result, dripResult);
}

// 只包含常规检测 - 文本 并且内容非空
TEST_F(LdfcrFixture, OnlyNormal_TextWithResult) 
{
	string testName = ::testing::UnitTest::GetInstance()->current_test_info()->name();
	if (!JudgeFileJoinTest(testName))
		return;

	dlplog_info(g_log_handle, "[%d] ===========================================TEST_F OnlyNormal_TextWithResult is started===========================================", __LINE__);
	std::string completePath = GetCompletePath("/test/data/strategy.data_single_normal");//********
	dlplog_info(g_log_handle, "[%d] %s", __LINE__, completePath.c_str());

	EXPECT_EQ(ldfcr.init(_T("test/data/strategy.data_single_normal")), 0);
	IFCRResult *result = NULL;
	IFCRDripResult *dripResult = NULL;
	EXPECT_TRUE(ldfcr.detect(_T("test/text1.txt"), (void **)&result, (void **)&dripResult));
	EXPECT_FALSE(IsResultEmpty(result));
	EXPECT_TRUE(IsResultEmpty(dripResult));
	EXPECT_TRUE(ResultHasContent(result));
	CLEAN_UP(result, dripResult);
}

// 零星检测 - 计数 两个策略共享一组规则
TEST_F(LdfcrFixture, SharedClassifyCount) 
{
	string testName = ::testing::UnitTest::GetInstance()->current_test_info()->name();
	if (!JudgeFileJoinTest(testName))
		return;

	dlplog_info(g_log_handle, "[%d] ===========================================TEST_F SharedClassifyCount is started===========================================", __LINE__);
	std::string completePath = GetCompletePath("/test/data/strategy.data_same_classify_count");//********
	dlplog_info(g_log_handle, "[%d] %s", __LINE__, completePath.c_str());

	EXPECT_EQ(ldfcr.init(_T("test/data/strategy.data_same_classify_count")), 0);
	IFCRResult *result = NULL;
	IFCRDripResult *dripResult = NULL;
	EXPECT_TRUE(ldfcr.detect(_T("test/numbers.txt"), (void **)&result, (void **)&dripResult));
	EXPECT_TRUE(IsResultEmpty(result));
	EXPECT_FALSE(IsResultEmpty(dripResult));
	EXPECT_EQ(NumResult(dripResult), 2);
	CLEAN_UP(result, dripResult);
}

// 零星检测 - 权重 两个策略共享一组规则
TEST_F(LdfcrFixture, SharedClassifyWeight) 
{
	string testName = ::testing::UnitTest::GetInstance()->current_test_info()->name();
	if (!JudgeFileJoinTest(testName))
		return;

	dlplog_info(g_log_handle, "[%d] ===========================================TEST_F SharedClassifyWeight is started===========================================", __LINE__);
	std::string completePath = GetCompletePath("/test/data/strategy.data_same_classify_weight");//********
	dlplog_info(g_log_handle, "[%d] %s", __LINE__, completePath.c_str());

	EXPECT_EQ(ldfcr.init(_T("test/data/strategy.data_same_classify_weight")), 0);
	IFCRResult *result = NULL;
	IFCRDripResult *dripResult = NULL;
	EXPECT_TRUE(ldfcr.detect(_T("test/numbers.txt"), (void **)&result, (void **)&dripResult));
	EXPECT_TRUE(IsResultEmpty(result));
	EXPECT_FALSE(IsResultEmpty(dripResult));
	EXPECT_EQ(NumResult(dripResult), 2);
	CLEAN_UP(result, dripResult);
}

// 测试例外关键字在零星检测中没有效果
TEST_F(LdfcrFixture, DripExceptCondNotMeet) 
{
	string testName = ::testing::UnitTest::GetInstance()->current_test_info()->name();
	if (!JudgeFileJoinTest(testName))
		return;

	dlplog_info(g_log_handle, "[%d] ===========================================TEST_F DripExceptCondNotMeet is started===========================================", __LINE__);
	std::string completePath = GetCompletePath("/test/data/strategy.data_drip_except_cond");//********
	dlplog_info(g_log_handle, "[%d] %s", __LINE__, completePath.c_str());

	EXPECT_EQ(ldfcr.init(_T("test/data/strategy.data_drip_except_cond")), 0);
	IFCRResult *result = NULL;
	IFCRDripResult *dripResult = NULL;
	EXPECT_FALSE(ldfcr.detect(_T("test/drip_except_cond.txt"), (void **)&result, (void **)&dripResult));
	EXPECT_TRUE(IsResultEmpty(result));
	EXPECT_TRUE(IsResultEmpty(dripResult));
	CLEAN_UP(result, dripResult);
}

//  测试关闭零星检测是否生效 "dripScan":false
TEST_F(LdfcrFixture, DripScanOff) 
{
	string testName = ::testing::UnitTest::GetInstance()->current_test_info()->name();
	if (!JudgeFileJoinTest(testName))
		return;

	dlplog_info(g_log_handle, "[%d] ===========================================TEST_F DripScanOff is started===========================================", __LINE__);
	std::string completePath = GetCompletePath("/test/data/strategy.data_dripscan_off");//********
	dlplog_info(g_log_handle, "[%d] %s", __LINE__, completePath.c_str());

	EXPECT_EQ(ldfcr.init(_T("test/data/strategy.data_dripscan_off")), 0);
	IFCRResult *result = NULL;
	IFCRDripResult *dripResult = NULL;
	EXPECT_TRUE(ldfcr.detect(_T("test/drip_normal_text.txt"), (void **)&result, (void **)&dripResult));
	EXPECT_FALSE(IsResultEmpty(result));
	EXPECT_EQ(NumResult(result), 1);
	EXPECT_TRUE(IsResultEmpty(dripResult));
	CLEAN_UP(result, dripResult);
}

// 测试零星检测返回策略是否包含名称
TEST_F(LdfcrFixture, CheckDripRuleStrategyName) 
{
	string testName = ::testing::UnitTest::GetInstance()->current_test_info()->name();
	if (!JudgeFileJoinTest(testName))
		return;

	dlplog_info(g_log_handle, "[%d] ===========================================TEST_F CheckDripRuleStrategyName is started===========================================", __LINE__);
	std::string completePath = GetCompletePath("/test/data/strategy.data_with_name.data");//********
	dlplog_info(g_log_handle, "[%d] %s", __LINE__, completePath.c_str());

	EXPECT_EQ(ldfcr.init(_T("test/data/strategy.data_with_name.data")), 0);
	IFCRResult *result = NULL;
	IFCRDripResult *dripResult = NULL;
	EXPECT_TRUE(ldfcr.detect(_T("test/text1.txt"), (void **)&result, (void **)&dripResult));
	EXPECT_TRUE(IsResultEmpty(result));
	EXPECT_FALSE(IsResultEmpty(dripResult));
	EXPECT_EQ(NumResult(dripResult), 1);
	EXPECT_TRUE(CheckName(dripResult));
	CLEAN_UP(result, dripResult);
}

// 测试常规检测返回策略是否包含名称
TEST_F(LdfcrFixture, CheckRegRuleStrategyName) 
{
	string testName = ::testing::UnitTest::GetInstance()->current_test_info()->name();
	if (!JudgeFileJoinTest(testName))
		return;

	dlplog_info(g_log_handle, "[%d] ===========================================TEST_F CheckRegRuleStrategyName is started===========================================", __LINE__);
	std::string completePath = GetCompletePath("/test/data/strategy.data_reg_with_name");//********
	dlplog_info(g_log_handle, "[%d] %s", __LINE__, completePath.c_str());

	EXPECT_EQ(ldfcr.init(_T("test/data/strategy.data_reg_with_name")), 0);
	IFCRResult *result = NULL;
	IFCRDripResult *dripResult = NULL;
	EXPECT_TRUE(ldfcr.detect(_T("test/text1.txt"), (void **)&result, (void **)&dripResult));
	EXPECT_TRUE(IsResultEmpty(dripResult));
	EXPECT_FALSE(IsResultEmpty(result));
	EXPECT_EQ(NumResult(result), 1);
	EXPECT_TRUE(CheckName(result));
	CLEAN_UP(result, dripResult);
}

// 测试单个文件包含大量关键字时，零星检测的结果数量是否为 1
TEST_F(LdfcrFixture, DripMassResult) 
{
	string testName = ::testing::UnitTest::GetInstance()->current_test_info()->name();
	if (!JudgeFileJoinTest(testName))
		return;

	dlplog_info(g_log_handle, "[%d] ===========================================TEST_F DripMassResult is started===========================================", __LINE__);
	std::string completePath = GetCompletePath("/test/data/mass_drip_hits.data");//********
	dlplog_info(g_log_handle, "[%d] %s", __LINE__, completePath.c_str());

	EXPECT_EQ(ldfcr.init(_T("test/data/mass_drip_hits.data")), 0);
	IFCRResult *result = NULL;
	IFCRDripResult *dripResult = NULL;
	EXPECT_TRUE(ldfcr.detect(_T("test/mass_hit.txt"), (void **)&result, (void **)&dripResult));
	EXPECT_TRUE(IsResultEmpty(result));
	EXPECT_FALSE(IsResultEmpty(dripResult));
	EXPECT_EQ(NumResult(dripResult), 1);
	EXPECT_TRUE(ResultHasContent(dripResult));
	CLEAN_UP(result, dripResult);
}

//零星检测 检测 返回 json 能否解析成功
TEST_F(LdfcrFixture, Drip_ParseJson) 
{
	string testName = ::testing::UnitTest::GetInstance()->current_test_info()->name();
	if (!JudgeFileJoinTest(testName))
		return;

	dlplog_info(g_log_handle, "[%d] ===========================================TEST_F Drip_ParseJson is started===========================================", __LINE__);
	std::string completePath = GetCompletePath("/test/data/DripKeyword.data");
	dlplog_info(g_log_handle, "[%d] %s", __LINE__, completePath.c_str());

	EXPECT_EQ(ldfcr.init(_T("test/data/DripKeyword.data")), 0);
	IFCRDripResult *dripResult = NULL;
	BOOL sensitive;
	EXPECT_TRUE(sensitive = ldfcr.detect(_T("test/mass_hit.txt"),NULL, (void **)&dripResult));
	EXPECT_TRUE(ldfcr.getDripResult(dripResult, sensitive));
	CLEAN_DRIP(dripResult);
}

// 零星检测 嵌套2个关键词——doc文件嵌套图片（doc文本内容包含一个关键字，嵌套图片亦然）
TEST_F(LdfcrFixture, DripDocEmbedImage_KW2) {
	string testName = ::testing::UnitTest::GetInstance()->current_test_info()->name();
	if (!JudgeFileJoinTest(testName))
		return;

	dlplog_info(g_log_handle, "[%d] ===========================================TEST_F DripDocEmbedImage_KW2 is started===========================================", __LINE__);
	std::string completePath = GetCompletePath("/test/data/DripKW_chhahaha_moreEasy.data");
	dlplog_info(g_log_handle, "[%d] %s", __LINE__, completePath.c_str());

	EXPECT_EQ(ldfcr.init(_T("test/data/DripKW_chhahaha_moreEasy.data")), 0); // 策略包含关键词：哈哈哈，更容易
	IFCRResult *result = NULL;
	IFCRDripResult *dripResult = NULL;
	EXPECT_TRUE(ldfcr.detect(_T("test/hahaha_Embed_Image.docx"), (void **)&result, (void **)&dripResult));
	EXPECT_TRUE(IsResultEmpty(result));
	EXPECT_FALSE(IsResultEmpty(dripResult));
	EXPECT_EQ(NumResult(dripResult), 1);
	EXPECT_TRUE(ResultHasContent(dripResult));
	CLEAN_UP(result, dripResult);
}

// 零星检测 嵌套2个关键词——txt1 + txt2(分别包含一个关键字，整体压缩)
TEST_F(LdfcrFixture, DripCompress2Txt_KW2) 
{
	string testName = ::testing::UnitTest::GetInstance()->current_test_info()->name();
	if (!JudgeFileJoinTest(testName))
		return;

	dlplog_info(g_log_handle, "[%d] ===========================================TEST_F DripCompress2Txt_KW2 is started===========================================", __LINE__);
	std::string completePath = GetCompletePath("/test/data/DripKW_chhahaha_moreEasy.data");
	dlplog_info(g_log_handle, "[%d] %s", __LINE__, completePath.c_str());

	EXPECT_EQ(ldfcr.init(_T("test/data/DripKW_chhahaha_moreEasy.data")), 0); // 策略包含关键词：哈哈哈，更容易
	IFCRResult *result = NULL;
	IFCRDripResult *dripResult = NULL;
	EXPECT_TRUE(ldfcr.detect(_T("test/hahaha_moreEasy_txt.zip"), (void **)&result, (void **)&dripResult));
	EXPECT_TRUE(IsResultEmpty(result));
	EXPECT_FALSE(IsResultEmpty(dripResult));
	EXPECT_EQ(NumResult(dripResult), 1);
	EXPECT_TRUE(ResultHasContent(dripResult));
	CLEAN_UP(result, dripResult);
}

// 零星检测 嵌套2个关键词——压缩txt + 压缩包注释(分别包含一个关键字）
TEST_F(LdfcrFixture, DripNoteCompressTxt_KW2) 
{
	string testName = ::testing::UnitTest::GetInstance()->current_test_info()->name();
	if (!JudgeFileJoinTest(testName))
		return;

	dlplog_info(g_log_handle, "[%d] ===========================================TEST_F DripNoteCompressTxt_KW2 is started===========================================", __LINE__);
	std::string completePath = GetCompletePath("/test/data/DripKW_enhahaha_moreEasy.data");
	dlplog_info(g_log_handle, "[%d] %s", __LINE__, completePath.c_str());

	EXPECT_EQ(ldfcr.init(_T("test/data/DripKW_enhahaha_moreEasy.data")), 0); // 策略包含关键词：hahaha，更容易
	IFCRResult *result = NULL;
	IFCRDripResult *dripResult = NULL;
	EXPECT_TRUE(ldfcr.detect(_T("test/moreEasy_NoteHahaha.zip"), (void **)&result, (void **)&dripResult)); //压缩包注释为：hahahaha
	EXPECT_TRUE(IsResultEmpty(result));
	EXPECT_FALSE(IsResultEmpty(dripResult));
	EXPECT_EQ(NumResult(dripResult), 1);
	EXPECT_TRUE(ResultHasContent(dripResult));
	CLEAN_UP(result, dripResult);
}

// 测试文件属性例外条件是否生效 exceptFile
TEST_F(LdfcrFixture, CheckExceptFileObject) 
{
	string testName = ::testing::UnitTest::GetInstance()->current_test_info()->name();
	if (!JudgeFileJoinTest(testName))
		return;

	dlplog_info(g_log_handle, "[%d] ===========================================TEST_F CheckExceptFileObject is started===========================================", __LINE__);
	std::string completePath = GetCompletePath("/test/data/strategy.data_exceptfile_object");//********
	dlplog_info(g_log_handle, "[%d] %s", __LINE__, completePath.c_str());

	EXPECT_EQ(ldfcr.init(_T("test/data/strategy.data_exceptfile_object")), 0);
	IFCRResult *result = NULL;
	IFCRDripResult *dripResult = NULL;
	EXPECT_FALSE(ldfcr.detect(_T("test/文件属性检测例外.txt"), (void **)&result, (void **)&dripResult));
	EXPECT_TRUE(IsResultEmpty(dripResult));
	EXPECT_TRUE(IsResultEmpty(result));
	CLEAN_UP(result, dripResult);
}

//  检查正则大小写敏感
TEST_F(LdfcrFixture, CheckRegexCaseSensitive) 
{
	string testName = ::testing::UnitTest::GetInstance()->current_test_info()->name();
	if (!JudgeFileJoinTest(testName))
		return;

	dlplog_info(g_log_handle, "[%d] ===========================================TEST_F CheckRegexCaseSensitive is started===========================================", __LINE__);
	std::string completePath = GetCompletePath("/test/data/strategy.data_regex_caseSense");//********
	dlplog_info(g_log_handle, "[%d] %s", __LINE__, completePath.c_str());

	EXPECT_EQ(ldfcr.init(_T("test/data/strategy.data_regex_caseSense")), 0);
	IFCRResult *result = NULL;
	IFCRDripResult *dripResult = NULL;
	EXPECT_FALSE(ldfcr.detect(_T("test/regex_case"), (void **)&result, (void **)&dripResult));
	EXPECT_TRUE(IsResultEmpty(dripResult));
	EXPECT_TRUE(IsResultEmpty(result));
	CLEAN_UP(result, dripResult);
}

////正则匹配，不需要检查大小写   2023  8 14 不需要测试
////检查正则大小写不敏感
//TEST_F(LdfcrFixture, CheckRegexCaseInsensitive)
//{
//	EXPECT_EQ(ldfcr.init(_T("test/data/strategy.data_regex_caseInsense")), 0);
//	IFCRResult *result = NULL;
//	IFCRDripResult *dripResult = NULL;
//	EXPECT_TRUE(ldfcr.detect(_T("test/regex_case"), (void **)&result, (void **)&dripResult));
//	EXPECT_TRUE(IsResultEmpty(dripResult));
//	EXPECT_FALSE(IsResultEmpty(result));
//	CLEAN_UP(result, dripResult);
//}

// no longer report multiple incident in one single file.
#if 0
TEST_F(LdfcrFixture, DripMultipleIncidents)
{
	string testName = ::testing::UnitTest::GetInstance()->current_test_info()->name();
	if (!JudgeFileJoinTest(testName))
		return;

	std::string completePath = GetCompletePath(L"/test/data/strategy.data_more_drip_match");//********
	dlplog_info(g_log_handle, "[%d] %s", __LINE__, completePath.c_str());

	EXPECT_EQ(ldfcr.init(_T("test/data/strategy.data_more_drip_match")), 0);
	IFCRResult *result = NULL;
	IFCRDripResult *dripResult = NULL;
	EXPECT_TRUE(ldfcr.detect(_T("test/dupclip"), (void **)&result, (void **)&dripResult));
	EXPECT_FALSE(IsResultEmpty(dripResult));
	EXPECT_TRUE(IsResultEmpty(result));
	EXPECT_EQ(NumResult(dripResult), 3);
	CLEAN_UP(result, dripResult);
}
#endif
#endif

// 检测文本截断功能 20个策略文件
TEST_F(LdfcrFixture, TextTrim)
{
	string testName = ::testing::UnitTest::GetInstance()->current_test_info()->name();
	if (!JudgeFileJoinTest(testName))
		return;

	dlplog_info(g_log_handle, "[%d] ===========================================TEST_F TextTrim is started===========================================", __LINE__);
	std::string completePath = GetCompletePath("/test/data/strategy.data_advstrategy");//********
	dlplog_info(g_log_handle, "[%d] %s", __LINE__, completePath.c_str());

	auto files = read_dir(_T("test/data/text_trim/"));
	for (auto &file : files)
	{
		long size = 0;
		char *content = getFileContent((TCHAR *)file.c_str(), &size);
		ASSERT_EQ(ldfcr.update_strategy(content), 0);
		delete[] content;

		IFCRResult *result = NULL;
		IFCRDripResult *dripResult = NULL;
		EXPECT_TRUE(ldfcr.detect(_T("test/4k.txt"), (void **)&result, (void **)&dripResult, 0, NULL, Ldfcr::_enTcrOpen::CLOSE));
		EXPECT_FALSE(IsResultEmpty(result));
		EXPECT_TRUE(IsResultEmpty(dripResult));
	
		ICRPart *part = NULL;
		result->moveFirstPart();
		while ((part = result->moveNextPart()) != NULL)
		{
			std::string s = part->getTextTrimed();
			EXPECT_LE(s.size(), 4096);
			EXPECT_EQ(s.find("..."), std::string::npos);
		}

		CLEAN_UP(result, dripResult);
		
	}
}

// 检测文本截断 20个策略文件
TEST_F(LdfcrFixture, TextTrim2)
{
	string testName = ::testing::UnitTest::GetInstance()->current_test_info()->name();
	if (!JudgeFileJoinTest(testName))
		return;

	dlplog_info(g_log_handle, "[%d] ===========================================TEST_F TextTrim2 is started===========================================", __LINE__);
	std::string completePath = GetCompletePath("/test/data/strategy.data_advstrategy");//********
	dlplog_info(g_log_handle, "[%d] %s", __LINE__, completePath.c_str());

	auto files = read_dir(_T("test/data/text_trim2/"));
	for (auto &file : files)
	{
		long size = 0;
		char *content = getFileContent((TCHAR *)file.c_str(), &size);
		ASSERT_EQ(ldfcr.update_strategy(content), 0);
		delete[] content;

		IFCRResult *result = NULL;
		IFCRDripResult *dripResult = NULL;
		EXPECT_TRUE(ldfcr.detect(_T("test/5k.txt"), (void **)&result, (void **)&dripResult, 0, NULL, Ldfcr::_enTcrOpen::CLOSE));
		EXPECT_FALSE(IsResultEmpty(result));
		EXPECT_TRUE(IsResultEmpty(dripResult));
		
		ICRPart *part = NULL;
		result->moveFirstPart();
		while ((part = result->moveNextPart()) != NULL)
		{
			std::string s = part->getTextTrimed();
			EXPECT_LE(s.size(), 4096);
			//EXPECT_EQ(s.find("..."), std::string::npos);
		}

		CLEAN_UP(result, dripResult);
	}
}

// 检测文本截断 20个策略文件
TEST_F(LdfcrFixture, TextTrim3)
{
	string testName = ::testing::UnitTest::GetInstance()->current_test_info()->name();
	if (!JudgeFileJoinTest(testName))
		return;

	dlplog_info(g_log_handle, "[%d] ===========================================TEST_F TextTrim3 is started===========================================", __LINE__);
	std::string completePath = GetCompletePath("/test/data/strategy.data_advstrategy");//********
	dlplog_info(g_log_handle, "[%d] %s", __LINE__, completePath.c_str());

	auto files = read_dir(_T("test/data/text_trim3/"));
	for (auto &file : files)
	{
		long size = 0;
		char *content = getFileContent((TCHAR *)file.c_str(), &size);
		ASSERT_EQ(ldfcr.update_strategy(content), 0);
		delete[] content;

		IFCRResult *result = NULL;
		IFCRDripResult *dripResult = NULL;
		EXPECT_TRUE(ldfcr.detect(_T("test/6k.txt"), (void **)&result, (void **)&dripResult, 0, NULL, Ldfcr::_enTcrOpen::CLOSE));
		EXPECT_FALSE(IsResultEmpty(result));
		EXPECT_TRUE(IsResultEmpty(dripResult));
	
		ICRPart *part = NULL;
		result->moveFirstPart();
		while ((part = result->moveNextPart()) != NULL)
		{
			std::string s = part->getTextTrimed();
			EXPECT_LE(s.size(), 4096);
		}

		CLEAN_UP(result, dripResult);
	}
}

// 检测文本截断 20个策略文件
TEST_F(LdfcrFixture, TextTrim4)
{
	string testName = ::testing::UnitTest::GetInstance()->current_test_info()->name();
	if (!JudgeFileJoinTest(testName))
		return;

	dlplog_info(g_log_handle, "[%d] ===========================================TEST_F TextTrim4 is started===========================================", __LINE__);
	std::string completePath = GetCompletePath("/test/data/strategy.data_advstrategy");//********
	dlplog_info(g_log_handle, "[%d] %s", __LINE__, completePath.c_str());

	auto files = read_dir(_T("test/data/text_trim4/"));
	for (auto &file : files)
	{
		long size = 0;
		char *content = getFileContent((TCHAR *)file.c_str(), &size);
		ASSERT_EQ(ldfcr.update_strategy(content), 0);
		delete[] content;

		IFCRResult *result = NULL;
		IFCRDripResult *dripResult = NULL;
		EXPECT_TRUE(ldfcr.detect(_T("test/7k.txt"), (void **)&result, (void **)&dripResult, 0, NULL, Ldfcr::_enTcrOpen::CLOSE));
		EXPECT_FALSE(IsResultEmpty(result));
		EXPECT_TRUE(IsResultEmpty(dripResult));

		ICRPart *part = NULL;
		result->moveFirstPart();
		while ((part = result->moveNextPart()) != NULL)
		{
			std::string s = part->getTextTrimed();
			EXPECT_LE(s.size(), 4096);
		}

		CLEAN_UP(result, dripResult);
	}
}

// 检测文本截断 20个策略文件
TEST_F(LdfcrFixture, TextTrim5)
{
	string testName = ::testing::UnitTest::GetInstance()->current_test_info()->name();
	if (!JudgeFileJoinTest(testName))
		return;

	dlplog_info(g_log_handle, "[%d] ===========================================TEST_F TextTrim5 is started===========================================", __LINE__);
	std::string completePath = GetCompletePath("/test/data/strategy.data_advstrategy");//********
	dlplog_info(g_log_handle, "[%d] %s", __LINE__, completePath.c_str());

	auto files = read_dir(_T("test/data/text_trim5/"));
	for (auto &file : files)
	{
		long size = 0;
		char *content = getFileContent((TCHAR *)file.c_str(), &size);
#ifdef __GNUC__
		ASSERT_EQ(ldfcr.update_strategy(content), 0);
#else
		/*tr::str::CUnicodeString content_sstr;
		content_sstr.convertFromUtf8(content);
		ASSERT_EQ(ldfcr.update_strategy(content_sstr.wstr()), 0);*/
		ASSERT_EQ(ldfcr.update_strategy(content), 0);
#endif
		delete[] content;

		IFCRResult *result = NULL;
		IFCRDripResult *dripResult = NULL;
		EXPECT_TRUE(ldfcr.detect(_T("test/8k.txt"), (void **)&result, (void **)&dripResult, 0, NULL, Ldfcr::_enTcrOpen::CLOSE));
		EXPECT_FALSE(IsResultEmpty(result));
		EXPECT_TRUE(IsResultEmpty(dripResult));
		
		ICRPart *part = NULL;
		result->moveFirstPart();
		while ((part = result->moveNextPart()) != NULL)
		{
			std::string s = part->getTextTrimed();
			EXPECT_LE(s.size(), 4096);
		}

		CLEAN_UP(result, dripResult);
	}
}

#define STRA_DIR _T("test/data/merge/")

// 常规策略与零星策略合并接口
TEST_F(LdfcrFixture, mergeStrategy)
{
	string testName = ::testing::UnitTest::GetInstance()->current_test_info()->name();
	if (!JudgeFileJoinTest(testName))
		return;

	dlplog_info(g_log_handle, "[%d] ===========================================TEST_F mergeStrategy is started===========================================", __LINE__);
	std::string completePath = GetCompletePath("/test/data/strategy.data");//********
	dlplog_info(g_log_handle, "[%d] %s", __LINE__, completePath.c_str());

	EXPECT_EQ(ldfcr.init(_T("test/data/strategy.data")), 0);
	struct {
		const TCHAR *reg_stra_file;
		const TCHAR *drip_stra_file;
		const char *comment;
		bool 		expect;
	} strategy_pair[]{
		{ STRA_DIR _T("succ/reg.data"), STRA_DIR _T("succ/drip.data"), "simple success case", true },
		{ STRA_DIR _T("fail/reg.data"), STRA_DIR _T("fail/drip.data"), "duplicate strategy id", false },
	};

	for (auto i = 0u; i < sizeof(strategy_pair) / sizeof(strategy_pair[0]); ++i)
	{
		long size = 0;
		char* reg_stra = getFileContent(strategy_pair[i].reg_stra_file, &size);
		char *drip_stra = getFileContent(strategy_pair[i].drip_stra_file, &size);
		if (reg_stra && drip_stra)
		{
			BOOL bRet = ldfcr.merge_update_strategy(reg_stra, drip_stra);
			EXPECT_EQ(bRet, strategy_pair[i].expect);
		}
		delete[] reg_stra;
		delete[] drip_stra;

	}
}

// linux开始支持高级规则 2024 9 10

// 检测策略包含高级规则，普通规则是否能正常匹配
TEST_F(LdfcrFixture, AdvStrategy)
{
	string testName = ::testing::UnitTest::GetInstance()->current_test_info()->name();
	if (!JudgeFileJoinTest(testName))
		return;

	dlplog_info(g_log_handle, "[%d] ===========================================TEST_F AdvStrategy is started===========================================", __LINE__);
	std::string completePath = GetCompletePath("/test/data/strategy.data_advstrategy");//********
	dlplog_info(g_log_handle, "[%d] %s", __LINE__, completePath.c_str());

	EXPECT_EQ(ldfcr.init(_T("test/data/strategy.data_advstrategy")), 0);
	IFCRResult *result = NULL;
	IFCRDripResult *dripResult = NULL;
	EXPECT_TRUE(ldfcr.detect(_T("test/text.txt"), (void **)&result, (void **)&dripResult));
	EXPECT_FALSE(IsResultEmpty(result));
	EXPECT_EQ(NumResult(result), 1);
	EXPECT_TRUE(IsResultEmpty(dripResult));
	CLEAN_UP(result, dripResult);
}

// 测试 普通规则 | 高级规则，当普通规则满足时，高级规则不需要进一步检测
TEST_F(LdfcrFixture, Regular_Or_Advanced_DBFP)
{
	string testName = ::testing::UnitTest::GetInstance()->current_test_info()->name();
	if (!JudgeFileJoinTest(testName))
		return;

	dlplog_info(g_log_handle, "[%d] ===========================================TEST_F Regular_Or_Advanced_DBFP is started===========================================", __LINE__);
	std::string completePath = GetCompletePath("/test/data/reg_adv_dbfp.data");//********
	dlplog_info(g_log_handle, "[%d] %s", __LINE__, completePath.c_str());

	EXPECT_EQ(ldfcr.init(_T("test/data/reg_adv_dbfp.data")), 0);
	
	IFCRResult *result = NULL;
	IFCRDripResult *dripResult = NULL;
	
	EXPECT_TRUE(ldfcr.detect(_T("test/text.txt"), (void **)&result, (void **)&dripResult));
	if(result)
	{
		const char *ids = result->GetAdvancedStrategyIdsFCR();
		EXPECT_NE(ids, nullptr);
		EXPECT_EQ(*ids, '\0');
		
	}
	CLEAN_UP(result, dripResult);
}

/* 2023 02 13 测试 普通规则 | 高级规则，当普通规则不满足时，高级规则需要进一步检测
因为数据库指纹算法下移为普通算法了，所以用文件指纹代替高级算法 之前的高级算法暂时先不动他们
*/
TEST_F(LdfcrFixture, Regular_Or_Advanced_FILEFP1)
{
	string testName = ::testing::UnitTest::GetInstance()->current_test_info()->name();
	if (!JudgeFileJoinTest(testName))
		return;

	dlplog_info(g_log_handle, "[%d] ===========================================TEST_F Regular_Or_Advanced_FILEFP1 is started===========================================", __LINE__);
	std::string completePath = GetCompletePath("/test/data/fileFPorKeyWord.data");//********
	dlplog_info(g_log_handle, "[%d] %s", __LINE__, completePath.c_str());

	EXPECT_EQ(ldfcr.init(_T("test/data/fileFPorKeyWord.data")), 0);

	IFCRResult *result = NULL;
	IFCRDripResult *dripResult = NULL;
	EXPECT_FALSE(ldfcr.detect(_T("test/text.txt"), (void **)&result, (void **)&dripResult));
	if(result)
	{
		const char *ids = result->GetAdvancedStrategyIdsFCR();
		EXPECT_NE(ids, nullptr);
		EXPECT_NE(*ids, '\0');
	}
	CLEAN_UP(result, dripResult);
}

//数据库指纹下移 测试数据库指纹   
TEST_F(LdfcrFixture, Advanced_DATABaseFP)
{
	string testName = ::testing::UnitTest::GetInstance()->current_test_info()->name();
	if (!JudgeFileJoinTest(testName))
		return;

	dlplog_info(g_log_handle, "[%d] ===========================================TEST_F Advanced_DATABaseFP is started===========================================", __LINE__);
	std::string completePath = GetCompletePath("/test/data/DB.data");//********
	dlplog_info(g_log_handle, "[%d] %s", __LINE__, completePath.c_str());

	IFCRResult *result = NULL;
	IFCRDripResult *dripResult = NULL;

	if (!operSysJugXP()) // XP版本不支持 高级算法
	{
		EXPECT_EQ(ldfcr.insert_key("14fee2b0ffb3836b224721215778d1018a062ce16055d41e247fd87ad0ad021ae1"), 1);
		EXPECT_EQ(ldfcr.init(_T("test/data/DB.data")), 0);
		EXPECT_TRUE(ldfcr.detect(_T("test/DataFile.txt"), (void **)&result, (void **)&dripResult));
	}

	CLEAN_UP(result, dripResult);
}

//数据库指纹下移 测试数据库指纹   
TEST_F(LdfcrFixture, Advanced_DATABaseFPNew)
{
	string testName = ::testing::UnitTest::GetInstance()->current_test_info()->name();
	if (!JudgeFileJoinTest(testName))
		return;

	dlplog_info(g_log_handle, "[%d] ===========================================TEST_F Advanced_DATABaseFPNew is started===========================================", __LINE__);
	std::string completePath = GetCompletePath("/test/data/DBNew.data");//********
	dlplog_info(g_log_handle, "[%d] %s", __LINE__, completePath.c_str());

	IFCRResult *result = NULL;
	IFCRDripResult *dripResult = NULL;

	if (!operSysJugXP()) // XP版本不支持 高级算法
	{
		EXPECT_EQ(ldfcr.insert_key("14fee2b0ffb3836b224721215778d1018a062ce16055d41e247fd87ad0ad021ae1"), 1);
		EXPECT_EQ(ldfcr.init(_T("test/data/DBNew.data")), 0);
		EXPECT_TRUE(ldfcr.detect(_T("test/DataFileNew.txt"), (void **)&result, (void **)&dripResult));
	}

	CLEAN_UP(result, dripResult);
}

//数据库指纹  测试带空数据表格	//20230912
TEST_F(LdfcrFixture, Advanced_DATABaseFP_Empty)
{
	string testName = ::testing::UnitTest::GetInstance()->current_test_info()->name();
	if (!JudgeFileJoinTest(testName))
		return;

	dlplog_info(g_log_handle, "[%d] ===========================================TEST_F Advanced_DATABaseFP_Empty is started===========================================", __LINE__);
	std::string completePath = GetCompletePath("/test/data/DB_Empty.data");
	dlplog_info(g_log_handle, "[%d] %s", __LINE__, completePath.c_str());

	IFCRResult *result = NULL;
	IFCRDripResult *dripResult = NULL;

	if (!operSysJugXP()) // XP版本不支持 高级算法
	{
		EXPECT_EQ(ldfcr.insert_key("14fee2b0ffb3836b224721215778d1018a062ce16055d41e247fd87ad0ad021ae1"), 1);
		EXPECT_EQ(ldfcr.init(_T("test/data/DB_Empty.data")), 0);	
		EXPECT_TRUE(ldfcr.detect(_T("test/DataFileEmpty.csv"), (void **)&result, (void **)&dripResult));
	}

	CLEAN_UP(result, dripResult);
}

//数据库指纹  测试带换行数据的表格	//20230912
TEST_F(LdfcrFixture, Advanced_DATABaseFP_Line)
{
	string testName = ::testing::UnitTest::GetInstance()->current_test_info()->name();
	if (!JudgeFileJoinTest(testName))
		return;

	dlplog_info(g_log_handle, "[%d] ===========================================TEST_F Advanced_DATABaseFP_Line is started===========================================", __LINE__);
	std::string completePath = GetCompletePath("/test/data/DB_line.data");
	dlplog_info(g_log_handle, "[%d] %s", __LINE__, completePath.c_str());

	IFCRResult *result = NULL;
	IFCRDripResult *dripResult = NULL;

	if (!operSysJugXP()) // XP版本不支持 高级算法
	{
		EXPECT_EQ(ldfcr.insert_key("14fee2b0ffb3836b224721215778d1018a062ce16055d41e247fd87ad0ad021ae1"), 1);
		EXPECT_EQ(ldfcr.init(_T("test/data/DB_line.data")), 0);
		EXPECT_TRUE(ldfcr.detect(_T("test/DataFileLine.csv"), (void **)&result, (void **)&dripResult));
	}

	CLEAN_UP(result, dripResult);
}

//数据库指纹下移 测试数据库指纹获取未下载规则文件列表  （请注意：data/rulefiles文件夹中的规则文件如果不在策略中，将被删除）
TEST_F(LdfcrFixture, Advanced_DATABaseFP_Regular_FileList)
{
	string testName = ::testing::UnitTest::GetInstance()->current_test_info()->name();
	if (!JudgeFileJoinTest(testName))
		return;

	dlplog_info(g_log_handle, "[%d] ===========================================TEST_F Advanced_DATABaseFP_Regular_FileList is started===========================================", __LINE__);
	std::string completePath = GetCompletePath("/test/data/DataBFP.data");//********	
	dlplog_info(g_log_handle, "[%d] %s", __LINE__, completePath.c_str());
	
	if (!operSysJugXP()) // XP版本不支持	
	{
		EXPECT_EQ(ldfcr.init(_T("test/data/DataBFP.data")), 0);
		std::string fileMd5 = "44ec00d71b70fddc2f26f886d0235b7c";
		std::string fileGuid = "46F26677-5D5F-4EFE-9433-38D42644F851";
		int ftpSvrId = 2147483647;
		EXPECT_TRUE(ldfcr.getRuleFilesList(fileMd5, fileGuid, ftpSvrId));
	}
}

//数据库指纹下移 测试数据库指纹获取未下载规则文件列表（服务端模式）
TEST_F(LdfcrFixture, Advanced_DATABaseFP_Regular_FileList_ServerMode)
{
	string testName = ::testing::UnitTest::GetInstance()->current_test_info()->name();
	if (!JudgeFileJoinTest(testName))
		return;

	dlplog_info(g_log_handle, "[%d] ===========================================TEST_F Advanced_DATABaseFP_Regular_FileList_ServerMode is started===========================================", __LINE__);
	std::string completePath = GetCompletePath("/test/data/DataBFP.data");
	dlplog_info(g_log_handle, "[%d] %s", __LINE__, completePath.c_str());

	if (!operSysJugXP()) // XP版本不支持
	{
		EXPECT_EQ(ldfcr.insert_key("14fee2b0ffb3836b224721215778d1018a062ce16055d41e247fd87ad0ad021ae1"), 1);
		EXPECT_EQ(ldfcr.init(_T("test/data/DataBFP.data")), 0);
		std::string fileMd5 = "44ec00d71b70fddc2f26f886d0235b7c";
		std::string fileGuid = "46F26677-5D5F-4EFE-9433-38D42644F851";
		int ftpSvrId = 2147483647;
		EXPECT_TRUE(ldfcr.getRuleFilesListForServer(fileMd5, fileGuid, ftpSvrId));
	}
}

// 初步验证3.50版本的策略能否获取未下载规则文件列表
TEST_F(LdfcrFixture, Get_Undownload_Regular_FileList)
{
	string testName = ::testing::UnitTest::GetInstance()->current_test_info()->name();
	if (!JudgeFileJoinTest(testName))
		return;

	dlplog_info(g_log_handle, "[%d] ===========================================TEST_F Get_Undownload_Regular_FileList is started===========================================", __LINE__);
	std::string completePath = GetCompletePath("/test/data/getundownloaderror.data");
	dlplog_info(g_log_handle, "[%d] %s", __LINE__, completePath.c_str());
	
	if (!operSysJugXP()) // XP版本不支持	
	{
		EXPECT_EQ(ldfcr.init(_T("test/data/getundownloaderror.data")), 0);
		std::string fileMd5 = "44ec00d71b70fddc2f26f886d0235b7c";
		std::string fileGuid = "46F26677-5D5F-4EFE-9433-38D42644F851";
		int ftpSvrId = 2147483647;
		EXPECT_FALSE(ldfcr.getRuleFilesList(fileMd5, fileGuid, ftpSvrId));
	}
}

// 测试 普通规则 & 高级规则，当普通规则满足时，高级规则需要进一步检测
TEST_F(LdfcrFixture, Regular_And_Advanced_DBFP)
{
	string testName = ::testing::UnitTest::GetInstance()->current_test_info()->name();
	if (!JudgeFileJoinTest(testName))
		return;

	dlplog_info(g_log_handle, "[%d] ===========================================TEST_F Regular_And_Advanced_DBFP is started===========================================", __LINE__);
	std::string completePath = GetCompletePath("/test/data/reg_and_adv_dbfp.data");//********
	dlplog_info(g_log_handle, "[%d] %s", __LINE__, completePath.c_str());

	EXPECT_EQ(ldfcr.init(_T("test/data/reg_and_adv_dbfp.data")), 0);

	IFCRResult *result = NULL;
	IFCRDripResult *dripResult = NULL;
	EXPECT_FALSE(ldfcr.detect(_T("test/text.txt"), (void **)&result, (void **)&dripResult));
	if(result)
	{
		const char *ids = result->GetAdvancedStrategyIdsFCR();
		EXPECT_NE(ids, nullptr);
		EXPECT_NE(*ids, '\0');
	}
	

	CLEAN_UP(result, dripResult);
}

// 2023 2 13 测试 普通规则 & 高级规则，当普通规则不满足时，高级规则不需要进一步检测
TEST_F(LdfcrFixture, Regular_And_Advanced_FILEFP2)
{
	string testName = ::testing::UnitTest::GetInstance()->current_test_info()->name();
	if (!JudgeFileJoinTest(testName))
		return;

	dlplog_info(g_log_handle, "[%d] ===========================================TEST_F Regular_And_Advanced_FILEFP2 is started===========================================", __LINE__);
	std::string completePath = GetCompletePath("/test/data/fileFPandKeyWord.data");//********
	dlplog_info(g_log_handle, "[%d] %s", __LINE__, completePath.c_str());

	EXPECT_EQ(ldfcr.init(_T("test/data/fileFPandKeyWord.data")), 0);

	IFCRResult *result = NULL;
	IFCRDripResult *dripResult = NULL;
	EXPECT_FALSE(ldfcr.detect(_T("test/text.txt"), (void **)&result, (void **)&dripResult));
	if(result)
	{
		const char *ids = result->GetAdvancedStrategyIdsFCR();
		EXPECT_NE(ids, nullptr);
		EXPECT_EQ(*ids, '\0');
	}

	CLEAN_UP(result, dripResult);
}

// 测试 普通规则 & 高级规则，当普通规则满足时，高级规则被禁用，高级规则不需要进一步检测
TEST_F(LdfcrFixture, Regular_And_Advanced_ControlModule_DBFP)
{
	string testName = ::testing::UnitTest::GetInstance()->current_test_info()->name();
	if (!JudgeFileJoinTest(testName))
		return;

	dlplog_info(g_log_handle, "[%d] ===========================================TEST_F Regular_And_Advanced_ControlModule_DBFP is started===========================================", __LINE__);
	std::string completePath = GetCompletePath("/test/data/reg_and_adv_dbfp.data");//********
	dlplog_info(g_log_handle, "[%d] %s", __LINE__, completePath.c_str());

	EXPECT_EQ(ldfcr.init(_T("test/data/reg_and_adv_dbfp.data")), 0);

	ldfcr_ControlModule(LDFCR_MODULE_DBFP, 0);
	IFCRResult *result = NULL;
	IFCRDripResult *dripResult = NULL;
	EXPECT_FALSE(ldfcr.detect(_T("test/text.txt"), (void **)&result, (void **)&dripResult));
	if(result)
	{
		const char *ids = result->GetAdvancedStrategyIdsFCR();
		EXPECT_EQ(*ids, '\0');
	}
	CLEAN_UP(result, dripResult);

	ldfcr_ControlModule(LDFCR_MODULE_DBFP, 1);
	EXPECT_FALSE(ldfcr.detect(_T("test/text.txt"), (void **)&result, (void **)&dripResult));
	
	if(result)
	{
		const char *ids = result->GetAdvancedStrategyIdsFCR();
		EXPECT_NE(*ids, '\0');
	}
	CLEAN_UP(result, dripResult);
}

// 测试 普通规则 | 高级规则，当普通规则满足时，高级规则被禁用，高级规则不需要进一步检测
TEST_F(LdfcrFixture, Regular_Or_Advanced_ControlModule_DBFP)
{
	string testName = ::testing::UnitTest::GetInstance()->current_test_info()->name();
	if (!JudgeFileJoinTest(testName))
		return;

	dlplog_info(g_log_handle, "[%d] ===========================================TEST_F Regular_Or_Advanced_ControlModule_DBFP is started===========================================", __LINE__);
	std::string completePath = GetCompletePath("/test/data/reg_adv_dbfp.data");//********
	dlplog_info(g_log_handle, "[%d] %s", __LINE__, completePath.c_str());

	EXPECT_EQ(ldfcr.init(_T("test/data/reg_adv_dbfp.data")), 0);
	ldfcr_ControlModule(LDFCR_MODULE_DBFP, 0);

	IFCRResult *result = NULL;
	IFCRDripResult *dripResult = NULL;
	EXPECT_TRUE(ldfcr.detect(_T("test/text.txt"), (void **)&result, (void **)&dripResult));
	if(result)
	{
		const char *ids = result->GetAdvancedStrategyIdsFCR();
		EXPECT_EQ(*ids, '\0');
	}
	CLEAN_UP(result, dripResult);

	ldfcr_ControlModule(LDFCR_MODULE_DBFP, 1);
	result = NULL;
	dripResult = NULL;
	EXPECT_TRUE(ldfcr.detect(_T("test/text.txt"), (void **)&result, (void **)&dripResult));
	if(result)
	{
		const char *ids = result->GetAdvancedStrategyIdsFCR();
		EXPECT_EQ(*ids, '\0');
	}
	CLEAN_UP(result, dripResult);
}

// 测试 普通规则 & 高级规则，当普通规则满足时，高级规则被禁用，高级规则不需要进一步检测
TEST_F(LdfcrFixture, Regular_And_Advanced_ControlModule_Rules)
{
	string testName = ::testing::UnitTest::GetInstance()->current_test_info()->name();
	if (!JudgeFileJoinTest(testName))
		return;

	dlplog_info(g_log_handle, "[%d] ===========================================TEST_F Regular_And_Advanced_ControlModule_Rules is started===========================================", __LINE__);
	std::string completePath = GetCompletePath("/test/data/reg_and_adv_rules.data");//********
	dlplog_info(g_log_handle, "[%d] %s", __LINE__, completePath.c_str());

	EXPECT_EQ(ldfcr.init(_T("test/data/reg_and_adv_rules.data")), 0);
	IFCRResult *result = NULL;
	IFCRDripResult *dripResult = NULL;
	const char *ids = NULL;

	ldfcr_ControlModule(LDFCR_MODULE_DBFP, 0);
	ldfcr_ControlModule(LDFCR_MODULE_SVM, 0);
	EXPECT_FALSE(ldfcr.detect(_T("test/Mix_DBFP_and_SVM_Alog.txt"), (void **)&result, (void **)&dripResult));
	
	if(result)
	{
		ids = result->GetAdvancedStrategyIdsFCR();
		EXPECT_EQ(*ids, '\0');
	}
	CLEAN_UP(result, dripResult);

	ldfcr_ControlModule(LDFCR_MODULE_DBFP, 0);
	ldfcr_ControlModule(LDFCR_MODULE_SVM, 1);
	EXPECT_FALSE(ldfcr.detect(_T("test/Mix_DBFP_and_SVM_Alog.txt"), (void **)&result, (void **)&dripResult));

	if(result)
	{	
		ids = result->GetAdvancedStrategyIdsFCR();
		EXPECT_NE(*ids, '\0');
	}
	CLEAN_UP(result, dripResult);

	ldfcr_ControlModule(LDFCR_MODULE_DBFP, 1);
	EXPECT_FALSE(ldfcr.detect(_T("test/Mix_DBFP_and_SVM_Alog.txt"), (void **)&result, (void **)&dripResult));
	if(result)
	{
		ids = result->GetAdvancedStrategyIdsFCR();
		EXPECT_NE(*ids, '\0');
	}
	CLEAN_UP(result, dripResult);
}

// 测试 高级规则 FILEDB 被禁用，高级规则不需要进一步检测
TEST_F(LdfcrFixture, ControlModule_DBFP)
{
	string testName = ::testing::UnitTest::GetInstance()->current_test_info()->name();
	if (!JudgeFileJoinTest(testName))
		return;

	dlplog_info(g_log_handle, "[%d] ===========================================TEST_F ControlModule_DBFP is started===========================================", __LINE__);
	std::string completePath = GetCompletePath("/test/data/control_dbfp.data");//********
	dlplog_info(g_log_handle, "[%d] %s", __LINE__, completePath.c_str());

	EXPECT_EQ(ldfcr.init(_T("test/data/control_dbfp.data")), 0);

	ldfcr_ControlModule(LDFCR_MODULE_DBFP, 0);
	IFCRResult *result = NULL;
	IFCRDripResult *dripResult = NULL;
	EXPECT_FALSE(ldfcr.detect(_T("test/text.txt"), (void **)&result, (void **)&dripResult));
	if(result)
	{
		const char *ids = result->GetAdvancedStrategyIdsFCR();
		EXPECT_EQ(*ids, '\0');
	}
	CLEAN_UP(result, dripResult);

	ldfcr_ControlModule(LDFCR_MODULE_DBFP, 1);
	EXPECT_FALSE(ldfcr.detect(_T("test/text.txt"), (void **)&result, (void **)&dripResult));
	if(result)
	{
		const char *ids = result->GetAdvancedStrategyIdsFCR();
		EXPECT_NE(*ids, '\0');
	}
	CLEAN_UP(result, dripResult);
}

//// 测试 高级规则 FILEDB 被禁用，高级规则不需要进一步检测
TEST_F(LdfcrFixture, ControlModule_DBFP1)
{
	string testName = ::testing::UnitTest::GetInstance()->current_test_info()->name();
	if (!JudgeFileJoinTest(testName))
		return;

	dlplog_info(g_log_handle, "[%d] ===========================================TEST_F ControlModule_DBFP1 is started===========================================", __LINE__);
	std::string completePath = GetCompletePath("/test/data/DB.data");//********
	dlplog_info(g_log_handle, "[%d] %s", __LINE__, completePath.c_str());

	EXPECT_EQ(ldfcr.init(_T("test/data/DB.data")), 0);

	ldfcr_ControlModule(LDFCR_MODULE_DBFP, 0);
	IFCRResult *result = NULL;
	IFCRDripResult *dripResult = NULL;
	EXPECT_FALSE(ldfcr.detect(_T("test/dataBase.txt"), (void **)&result, (void **)&dripResult));
	if(result)
	{
		const char *ids = result->GetAdvancedStrategyIdsFCR();
		EXPECT_EQ(*ids, '\0');
	}
	CLEAN_UP(result, dripResult);

	ldfcr_ControlModule(LDFCR_MODULE_DBFP, 1);
	EXPECT_FALSE(ldfcr.detect(_T("test/dataBase.txt"), (void **)&result, (void **)&dripResult));
	if(result)
	{
		const char *ids = result->GetAdvancedStrategyIdsFCR();
		EXPECT_NE(*ids, '\0');
	}
	CLEAN_UP(result, dripResult);
}

// 测试 高级规则 SVM 被禁用，高级规则不需要进一步检测
TEST_F(LdfcrFixture, ControlModule_SVM)
{
	string testName = ::testing::UnitTest::GetInstance()->current_test_info()->name();
	if (!JudgeFileJoinTest(testName))
		return;

	dlplog_info(g_log_handle, "[%d] ===========================================TEST_F ControlModule_SVM is started===========================================", __LINE__);
	std::string completePath = GetCompletePath("/test/data/control_svm.data");//********
	dlplog_info(g_log_handle, "[%d] %s", __LINE__, completePath.c_str());

	EXPECT_EQ(ldfcr.init(_T("test/data/control_svm.data")), 0);

	ldfcr_ControlModule(LDFCR_MODULE_SVM, 0);
	IFCRResult *result = NULL;
	IFCRDripResult *dripResult = NULL;
	EXPECT_FALSE(ldfcr.detect(_T("test/Mix_DBFP_and_SVM_Alog.txt"), (void **)&result, (void **)&dripResult));
	if(result)
	{
		const char *ids = result->GetAdvancedStrategyIdsFCR();
		EXPECT_EQ(*ids, '\0');
	}
	CLEAN_UP(result, dripResult);

	ldfcr_ControlModule(LDFCR_MODULE_SVM, 1);
	EXPECT_FALSE(ldfcr.detect(_T("test/Mix_DBFP_and_SVM_Alog.txt"), (void **)&result, (void **)&dripResult));
	if(result)
	{
		const char *ids = result->GetAdvancedStrategyIdsFCR();
		EXPECT_NE(*ids, '\0');
	}
	CLEAN_UP(result, dripResult);
}

// 测试 高级规则 FILEFP 被禁用，高级规则不需要进一步检测
TEST_F(LdfcrFixture, ControlModule_FILEFP)
{
	string testName = ::testing::UnitTest::GetInstance()->current_test_info()->name();
	if (!JudgeFileJoinTest(testName))
		return;

	dlplog_info(g_log_handle, "[%d] ===========================================TEST_F ControlModule_FILEFP is started===========================================", __LINE__);
	std::string completePath = GetCompletePath("/test/data/control_filefp.data");//********
	dlplog_info(g_log_handle, "[%d] %s", __LINE__, completePath.c_str());

	EXPECT_EQ(ldfcr.init(_T("test/data/control_filefp.data")), 0);

	ldfcr_ControlModule(LDFCR_MODULE_FILEFP, 0);
	IFCRResult *result = NULL;
	IFCRDripResult *dripResult = NULL;
	EXPECT_FALSE(ldfcr.detect(_T("test/text.txt"), (void **)&result, (void **)&dripResult));
	
	if(result)
	{
		const char *ids = result->GetAdvancedStrategyIdsFCR();
		EXPECT_EQ(*ids, '\0');		
	}
	CLEAN_UP(result, dripResult);
	ldfcr_ControlModule(LDFCR_MODULE_FILEFP, 1);
	EXPECT_FALSE(ldfcr.detect(_T("test/text.txt"), (void **)&result, (void **)&dripResult));
	if(result)
	{
		const char *ids = result->GetAdvancedStrategyIdsFCR();
		EXPECT_NE(*ids, '\0');
	}
	CLEAN_UP(result, dripResult);
}

// 测试 多操作类型与实例模式混合 高级规则 FILEDB 被开闭，高级规则是否需要进一步检测
TEST_F(LdfcrFixture, ControlModule_MixedtypeInstances_DBFP)
{
	string testName = ::testing::UnitTest::GetInstance()->current_test_info()->name();
	if (!JudgeFileJoinTest(testName))
		return;

	dlplog_info(g_log_handle, "[%d] ===========================================TEST_F ControlModule_MixedtypeInstances_DBFP is started===========================================", __LINE__);
	std::string completePath1 = GetCompletePath("/test/data/control_dbfp.data");//********
	std::string completePath2 = GetCompletePath("/test/data/multiops_control_dbfp.data");
	dlplog_info(g_log_handle, "[%d] %s", __LINE__, completePath1.c_str());
	dlplog_info(g_log_handle, "[%d] %s", __LINE__, completePath2.c_str());

	//================= Unmanaged ======================
	EXPECT_EQ(ldfcr.init(_T("test/data/control_dbfp.data")), 0);
	//================== Managed =======================
	EXPECT_EQ(ldfcr.g_init(_T("test/data/multiops_control_dbfp.data")), 0);
	
	//================= Unmanaged ======================
	ldfcr_ControlModule(LDFCR_MODULE_DBFP, 0);
	IFCRResult *result = NULL;
	IFCRDripResult *dripResult = NULL;
	EXPECT_FALSE(ldfcr.detect(_T("test/Mix_DBFP_and_SVM_Alog.txt"), (void **)&result, (void **)&dripResult));
	if(result)
	{
		const char *ids = result->GetAdvancedStrategyIdsFCR();
		EXPECT_EQ(*ids, '\0');		
	}
	CLEAN_UP(result, dripResult);

	//================== Managed =======================
	EXPECT_FALSE(ldfcr.g_detect(_T("test/Mix_DBFP_and_SVM_Alog.txt"), (void **)&result, (void **)&dripResult,1));
	if(result)
	{
		const char *ids = result->GetAdvancedStrategyIdsFCR();
		EXPECT_EQ(*ids, '\0');		
	}
	CLEAN_UP(result, dripResult);

	//================= Unmanaged ======================
	ldfcr_ControlModule(LDFCR_MODULE_DBFP, 1);
	EXPECT_FALSE(ldfcr.detect(_T("test/Mix_DBFP_and_SVM_Alog.txt"), (void **)&result, (void **)&dripResult));
	if(result)
	{
		const char *ids = result->GetAdvancedStrategyIdsFCR();
		EXPECT_EQ(*ids, '\0');		
	}
	CLEAN_UP(result, dripResult);

	//================== Managed =======================
	EXPECT_FALSE(ldfcr.g_detect(_T("test/Mix_DBFP_and_SVM_Alog.txt"), (void **)&result, (void **)&dripResult, 1));
	if(result)
	{
		const char *ids = result->GetAdvancedStrategyIdsFCR();
		EXPECT_EQ(*ids, '\0');		
	}
	CLEAN_UP(result, dripResult);
}

// 测试 文件属性（普通规则） & 文件指纹（高级规则），当普通规则满足时，高级规则需要进一步检测
TEST_F(LdfcrFixture, FilePropAndFileFP)
{
	string testName = ::testing::UnitTest::GetInstance()->current_test_info()->name();
	if (!JudgeFileJoinTest(testName))
		return;

	dlplog_info(g_log_handle, "[%d] ===========================================TEST_F FilePropAndFileFP is started===========================================", __LINE__);
	std::string completePath = GetCompletePath("/test/data/FilePropAndFileFP.data");
	dlplog_info(g_log_handle, "[%d] %s", __LINE__, completePath.c_str());

	EXPECT_EQ(ldfcr.init(_T("test/data/FilePropAndFileFP.data")), 0);

	IFCRResult *result = NULL;
	IFCRDripResult *dripResult = NULL;

	EXPECT_FALSE(ldfcr.detect(_T("test/long.doc"), (void **)&result, (void **)&dripResult));
	if (result)
	{
		const char *ids = result->GetAdvancedStrategyIdsFCR();
		EXPECT_NE(ids, nullptr);
		EXPECT_NE(*ids, '\0');

		char *fileProp = result->GetAdvancedFilePropFCR();
		EXPECT_NE(fileProp, nullptr);
		EXPECT_NE(*fileProp, '\0');
	}
	CLEAN_UP(result, dripResult);
}

//测试后缀防篡改大集合——不精确版本
TEST_F(LdfcrFixture, AntiModifiedSuffix_Impreci)
{
	string testName = ::testing::UnitTest::GetInstance()->current_test_info()->name();
	if (!JudgeFileJoinTest(testName))
		return;

	dlplog_info(g_log_handle, "[%d] ===========================================TEST_F AntiModifiedSuffix_Impreci is started===========================================", __LINE__);

	EXPECT_EQ(ldfcr.init(_T("/test/data/SuffixRecognition/AllfilesSuffix.data")), 0);
	std::string completePath = GetCompletePath("/test/data/SuffixRecognition/AllfilesSuffix.data");
	dlplog_info(g_log_handle, "[%d] %s", __LINE__, completePath.c_str());
	EXPECT_TRUE(ldfcr.AntiModifiedSuffix(_T("test/SuffixAntiModify")));
}

//测试后缀防篡改大集合——精确版本——匹配返回json中格式
TEST_F(LdfcrFixture, AntiModifiedSuffix_preci)
{
	string testName = ::testing::UnitTest::GetInstance()->current_test_info()->name();
	if (!JudgeFileJoinTest(testName))
		return;

	dlplog_info(g_log_handle, "[%d] ===========================================TEST_F AntiModifiedSuffix_preci is started===========================================", __LINE__);

	EXPECT_EQ(ldfcr.init(_T("/test/data/SuffixRecognition/AllfilesSuffix.data")), 0);
	std::string completePath = GetCompletePath("/test/data/SuffixRecognition/AllfilesSuffix.data");
	dlplog_info(g_log_handle, "[%d] %s", __LINE__, completePath.c_str());
	EXPECT_TRUE(ldfcr.FileSuffixPos(_T("test/SuffixAntiModify")));
}

// 新增文件后缀防篡改大集合  北京客户需求
TEST_F(LdfcrFixture, AntiModifiedSuffix_beijingSYK300)
{
	string testName = ::testing::UnitTest::GetInstance()->current_test_info()->name();
	if (!JudgeFileJoinTest(testName))
		return;

	dlplog_info(g_log_handle, "[%d] ===========================================TEST_F AntiModifiedSuffix_beijingSYK300 is started===========================================", __LINE__);
	std::string completePath = GetCompletePath("/test/data/SuffixRecognition/beijingSYK300.data");
	dlplog_info(g_log_handle, "[%d] %s", __LINE__, completePath.c_str());

	EXPECT_EQ(ldfcr.init(_T("test/data/SuffixRecognition/beijingSYK300.data")), 0);
	EXPECT_TRUE(ldfcr.FileSuffixPos(_T("test/beijingSYK300")));
}

#ifdef WIN32
//测试base64加密过的个人信息
TEST_F(LdfcrFixture, Base64Endoce_PersonInfo)
{
	string testName = ::testing::UnitTest::GetInstance()->current_test_info()->name();
	if (!JudgeFileJoinTest(testName))
		return;

	dlplog_info(g_log_handle, "[%d] ===========================================TEST_F Base64Endoce_PersonInfo is started===========================================", __LINE__);

	EXPECT_EQ(ldfcr.init(_T("/test/data/Base64Endoce_name.data")), 0);
	std::string completePath = GetCompletePath("/test/data/Base64Endoce_name.data");
	dlplog_info(g_log_handle, "[%d] %s", __LINE__, completePath.c_str());
	EXPECT_TRUE(ldfcr.detectBase64Decode(_T("test/Base64Endoce_PersonInfo_10.txt"), 0));//姓名、地址、身份证、银行卡、手机号各10个
	EXPECT_EQ(ldfcr.init(_T("/test/data/Base64Endoce_name.data")), 0);
	EXPECT_FALSE(ldfcr.detectBase64Decode(_T("test/Base64Endoce_name_100.txt"), 0));//姓名100个
}
#endif

// 超时默认策略
TEST_F(LdfcrFixture, TimeoutDefaultAction)
{
	string testName = ::testing::UnitTest::GetInstance()->current_test_info()->name();
	if (!JudgeFileJoinTest(testName))
		return;

	dlplog_info(g_log_handle, "[%d] ===========================================TEST_F TimeoutDefaultAction is started===========================================", __LINE__);
	std::string completePath = GetCompletePath("/test/data/strategy.data");//********
	dlplog_info(g_log_handle, "[%d] %s", __LINE__, completePath.c_str());

	EXPECT_EQ(ldfcr.init(_T("test/data/strategy.data")), 0);
	FCRPARAM fcrParam;
	fcrParam.use_all_rule = TRUE;   //启用所有规则
	fcrParam.target_class_code = 3; //达到或超过代码3，即停止
	fcrParam.output_detail = TRUE;  //要求返回时输出策略的细节信息
	fcrParam.devType = 1;
	fcrParam.opType = 0;

	IFCRResult *result = NULL;
	IFCRDripResult *dripResult = NULL;

	// Interrupt at text extraction
	fcrParam.timeout = 100;
	fcrParam.interrupt = 0;
	fcrParam.block_on_timeout = false;
	EXPECT_FALSE(ldfcr.detect(_T("test/haha.txt"), (void **)&result, (void **)&dripResult, 0, &fcrParam));
	EXPECT_EQ(IsResultEmpty(result), true);
	EXPECT_EQ(IsResultEmpty(dripResult), true);
	CLEAN_UP(result, dripResult);

	// Interrupt at text extraction
	fcrParam.timeout = 100;
	fcrParam.interrupt = 0;
	fcrParam.block_on_timeout = true;
	EXPECT_TRUE(ldfcr.detect(_T("test/haha.txt"), (void **)&result, (void **)&dripResult, 0, &fcrParam));
	EXPECT_EQ(IsResultEmpty(result), false);
	EXPECT_EQ(IsResultEmpty(dripResult), true);
	EXPECT_EQ(ResponseCheckItem(result, "stopOutgoing", 1), true);
	CLEAN_UP(result, dripResult);

	// Interrupt at detection
	fcrParam.timeout = 300;
	fcrParam.interrupt = 0;
	fcrParam.block_on_timeout = false;
	EXPECT_FALSE(ldfcr.detect(_T("test/haha.txt"), (void **)&result, (void **)&dripResult, 0, &fcrParam));
	EXPECT_EQ(IsResultEmpty(result), true);
	EXPECT_EQ(IsResultEmpty(dripResult), true);
	CLEAN_UP(result, dripResult);

	// Interrupt at detection
	fcrParam.timeout = 30;
	fcrParam.interrupt = 0;
	fcrParam.block_on_timeout = true;
	EXPECT_TRUE(ldfcr.detect(_T("test/haha.txt"), (void **)&result, (void **)&dripResult, 0, &fcrParam));
	EXPECT_EQ(IsResultEmpty(result), false);
	EXPECT_EQ(IsResultEmpty(dripResult), true);
	EXPECT_EQ(ResponseCheckItem(result, "stopOutgoing", 1), true);
	CLEAN_UP(result, dripResult);
}

// 测试 多操作类型接口 高级规则 FILEDB 被禁用，高级规则不需要进一步检测
TEST_F(LdfcrFixture, MultiOpsStrategy_ControlModule_DBFP)
{
	string testName = ::testing::UnitTest::GetInstance()->current_test_info()->name();
	if (!JudgeFileJoinTest(testName))
		return;

	dlplog_info(g_log_handle, "[%d] ===========================================TEST_F MultiOpsStrategy_ControlModule_DBFP is started===========================================", __LINE__);
	std::string completePath = GetCompletePath("/test/data/multiops_control_dbfp.data");//********
	dlplog_info(g_log_handle, "[%d] %s", __LINE__, completePath.c_str());

	EXPECT_EQ(ldfcr.g_init(_T("test/data/multiops_control_dbfp.data")), 0);

	ldfcr_ControlModule(LDFCR_MODULE_DBFP, 0);
	IFCRResult* result = NULL;
	IFCRDripResult* dripResult = NULL;
	FCRPARAM fcrParam;
	fcrParam.use_all_rule = TRUE;   //启用所有规则
	fcrParam.target_class_code = 3; //达到或超过代码3，即停止
	fcrParam.output_detail = TRUE;  //要求返回时输出策略的细节信息
	fcrParam.opType = 1;
	fcrParam.fcrInfo = "{ \"opType\": 0, \"IP\": \"***********\"}";

	EXPECT_FALSE(ldfcr.g_detect(_T("test/text.txt"), (void **)&result, (void **)&dripResult, 0, &fcrParam, Ldfcr::_enTcrOpen::CLOSE));
	if (result)
	{
		const char *ids = result->GetAdvancedStrategyIdsFCR();
		EXPECT_EQ(*ids, '\0');
	}
	CLEAN_UP(result, dripResult);

	ldfcr_ControlModule(LDFCR_MODULE_DBFP, 1);
	EXPECT_FALSE(ldfcr.g_detect(_T("test/text.txt"), (void **)&result, (void **)&dripResult, 0, &fcrParam));

	if (result)
	{
		const char *ids = result->GetAdvancedStrategyIdsFCR();
		EXPECT_NE(*ids, '\0');
	}
	CLEAN_UP(result, dripResult);
}

#if 1
// 多操作类型接口匹配测试
TEST_F(LdfcrFixture, MultiOpsStrategy_SimpleMatch)
{
	string testName = ::testing::UnitTest::GetInstance()->current_test_info()->name();
	if (!JudgeFileJoinTest(testName))
		return;

	dlplog_info(g_log_handle, "[%d] ===========================================TEST_F SimpleMatch is started===========================================", __LINE__);
	std::string completePath = GetCompletePath("/test/data/strategy.data_multi_strategy_inf");//********
	dlplog_info(g_log_handle, "[%d] %s", __LINE__, completePath.c_str());

	EXPECT_EQ(ldfcr.g_init(_T("test/data/strategy.data_multi_strategy_inf")), 0);

	IFCRResult* result = NULL;
	IFCRDripResult* dripResult = NULL;
	FCRPARAM fcrParam;
	fcrParam.use_all_rule = TRUE;   //启用所有规则
	fcrParam.target_class_code = 3; //达到或超过代码3，即停止
	fcrParam.output_detail = TRUE;  //要求返回时输出策略的细节信息
	fcrParam.opType = 0;
	fcrParam.fcrInfo = "{ \"opType\": 0, \"IP\": \"***********\"}";

	EXPECT_TRUE(ldfcr.g_detect(_T("test/text.txt"), (void **)&result, (void **)&dripResult, 0, &fcrParam, Ldfcr::_enTcrOpen::CLOSE));

	EXPECT_FALSE(IsResultEmpty(result));
	CLEAN_UP(result, dripResult);

	EXPECT_TRUE(ldfcr.g_detect(_T("test/text.txt"), (void **)&result, (void **)&dripResult, 0, &fcrParam));
	
	EXPECT_FALSE(IsResultEmpty(result));
	CLEAN_UP(result, dripResult);
}

// 多操作类型接口不匹配测试
TEST_F(LdfcrFixture, MultiOpsStrategy_SimpleNotMatch)
{
	string testName = ::testing::UnitTest::GetInstance()->current_test_info()->name();
	if (!JudgeFileJoinTest(testName))
		return;

	dlplog_info(g_log_handle, "[%d] ===========================================TEST_F SimpleNotMatch is started===========================================", __LINE__);
	std::string completePath = GetCompletePath("/test/data/strategy.data_multi_strategy_inf");//********
	dlplog_info(g_log_handle, "[%d] %s", __LINE__, completePath.c_str());

	EXPECT_EQ(ldfcr.g_init(_T("test/data/strategy.data_multi_strategy_inf")), 0);

	IFCRResult* result = NULL;
	IFCRDripResult* dripResult = NULL;
	FCRPARAM fcrParam;
	fcrParam.use_all_rule = TRUE;   //启用所有规则
	fcrParam.target_class_code = 3; //达到或超过代码3，即停止
	fcrParam.output_detail = TRUE;  //要求返回时输出策略的细节信息
	fcrParam.opType = 41;
	fcrParam.fcrInfo = "{ \"IP\": \"***********\"}";

	EXPECT_FALSE(ldfcr.g_detect(_T("test/text.txt"), (void **)&result, (void **)&dripResult, 0, &fcrParam, Ldfcr::_enTcrOpen::CLOSE));
	EXPECT_TRUE(IsResultEmpty(result));
	EXPECT_EQ(result->getErrCode(), LDFCR_STRATEGY_NOTFOUND);
	CLEAN_UP(result, dripResult);

	EXPECT_FALSE(ldfcr.g_detect(_T("test/text.txt"), (void **)&result, (void **)&dripResult, 0, &fcrParam));

	EXPECT_TRUE(IsResultEmpty(result));
	EXPECT_EQ(result->getErrCode(), LDFCR_STRATEGY_NOTFOUND);
	CLEAN_UP(result, dripResult);

}
#endif

// 多操作类型策略合并接口匹配测试
TEST_F(LdfcrFixture, MultiOpsMergeStrategy_SimpleMatchMerge)
{
	string testName = ::testing::UnitTest::GetInstance()->current_test_info()->name();
	if (!JudgeFileJoinTest(testName))
		return;

	dlplog_info(g_log_handle, "[%d] ===========================================TEST_F SimpleMatchMerge is started===========================================", __LINE__);
	std::string completePathSucc = GetCompletePath("/succ");//********
	std::string completePathFail = GetCompletePath("/fail");//********
	dlplog_info(g_log_handle, "[%d] [%s] [%s] ", __LINE__, completePathSucc.c_str(), completePathFail.c_str());

	struct {
		const TCHAR *reg_stra_file;
		const TCHAR *drip_stra_file;
		const char *comment;
		bool 		expect;
	} strategy_pair[]{
#ifdef WIN32
		{ STRA_DIR _T("succ/multiops_reg.data"), STRA_DIR _T("succ/multiops_drip.data"), "simple success case", true },
		{ STRA_DIR _T("succ/multiops_reg.data"), NULL, "regular success case", true },
		{ NULL, STRA_DIR _T("succ/multiops_drip.data"), "drip success case", true },
		{ STRA_DIR _T("fail/multiops_reg_op.data"), STRA_DIR _T("fail/multiops_drip_op.data"), "op not equal", false },
		{ STRA_DIR _T("fail/multiops_reg_type.data"), STRA_DIR _T("fail/multiops_drip_type.data"), "type not equal", false },
#else
		{ STRA_DIR _T("succ/multiops_reg.data"), STRA_DIR _T("succ/multiops_drip.data"), "simple success case", true },
		{ STRA_DIR _T("succ/multiops_reg.data"), _T(""), "regular success case", true },
		{ _T(""), STRA_DIR _T("succ/multiops_drip.data"), "drip success case", true },
		{ STRA_DIR _T("fail/multiops_reg_op.data"), STRA_DIR _T("fail/multiops_drip_op.data"), "op not equal", false },
		{ STRA_DIR _T("fail/multiops_reg_type.data"), STRA_DIR _T("fail/multiops_drip_type.data"), "type not equal", false },
#endif
	};

	for (auto i = 0u; i < sizeof(strategy_pair) / sizeof(strategy_pair[0]); ++i)
	{
		long size = 0;
		char* reg_stra = getFileContent(strategy_pair[i].reg_stra_file, &size);
		char *drip_stra = getFileContent(strategy_pair[i].drip_stra_file, &size);
		if (reg_stra || drip_stra)
		{
#ifdef WIN32
			std::wstring reg, drip;
			if (reg_stra)
				reg = str2ws(reg_stra, strlen(reg_stra));
			if (drip_stra)
				drip = str2ws(drip_stra, strlen(drip_stra));

			std::string regStg = WstringToUtf8(reg);
			std::string dripStg = WstringToUtf8(drip);
#else
			std::string regStg = reg_stra;
			std::string dripStg = drip_stra;
#endif
			BOOL bRet = ldfcr_MergeUpdateStrategy(regStg.c_str(), dripStg.c_str());

			EXPECT_EQ(bRet, strategy_pair[i].expect) << strategy_pair[i].comment;
		}
		delete[] reg_stra;
		delete[] drip_stra;
	}

	struct {
		const char *reg_strategy;
		const char *drip_strategy;
		const char *comment;
		bool 		expect;
	} strategies[]{
		{ R"({
   "checkRule":[
      {
         "exceptWord":"忘羡",
         "keywords":"哈哈哈,磨难,TRDLP,天锐dlp,^%^$,1823",
         "checkTimes":1,
         "createTime":1610588282000,
         "type":2,
         "ruleType":"2",
         "name":"关键字",
         "dbkeywords":"",
         "id":1,
         "caseSensitive":true
      }
   ],
   "respondRule":[
      {
         "warnContent":"",
         "takeScreenshot":false,
         "createTime":1610596207000,
         "name":"零星检测",
         "sendMail":false,
         "enableCancel":0,
         "id":15,
         "filters":[
            {
               "type":"deviceType",
               "relation":"either",
               "object":["terminal-agent"]
            },
            {
               "type":"lossType",
               "relation":"either",
               "object":[ "1", "2", "3", "4", "5", "7", "8", "9", "11", "13", "14", "15", "16", "17", "18", "20"]
            }
         ],
         "alarmInfo":{
            "alarmLimit":3,
            "msgFormType":0,
            "msgFormPosition":1,
            "showSensContent":0,
            "msgFormClose":20
         },
         "stopOutgoing":true
      }
   ],
   "strategy":[
      {
         "severity":"1",
         "respondRule":"15",
         "createTime":1612665305503,
         "checkRule":"1",
         "name":"关键字_零星策略",
         "dripScan":true,
         "id":102,
         "classification":[
            {
               "dripProps":[
                  {
                     "opTypes":"1,2",
                     "weight":6,
                     "timeSpan":"10m",
                     "id":1,
                     "uniqueMatch":false
                  }
               ],
               "checkExpr":"1",
               "name":"关键字_零星规则",
               "weightThreshold":42,
               "exceptRule":"",
               "id":61
            }
         ]
      }
   ],
   "businessType":{
      "opTypes":[ 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 20],
      "type":99
   }
})", "", "drip strategy success", true },
{ R"({
   "checkRule":[
      {
         "exceptWord":"忘羡",
         "keywords":"哈哈哈,磨难,TRDLP,天锐dlp,^%^$,1823",
         "checkTimes":1,
         "createTime":1610588282000,
         "type":2,
         "ruleType":"2",
         "name":"关键字",
         "dbkeywords":"",
         "id":1,
         "caseSensitive":true
      }
   ],
   "respondRule":[
      {
         "warnContent":"",
         "takeScreenshot":false,
         "createTime":1610596207000,
         "name":"普通检测",
         "sendMail":false,
         "enableCancel":0,
         "id":15,
         "filters":[
            {
               "type":"deviceType",
               "relation":"either",
               "object":["terminal-agent"]
            },
            {
               "type":"lossType",
               "relation":"either",
               "object":[ "1", "2", "3", "4", "5", "7", "8", "9", "11", "13", "14", "15", "16", "17", "18", "20"]
            }
         ],
         "alarmInfo":{
            "alarmLimit":3,
            "msgFormType":0,
            "msgFormPosition":1,
            "showSensContent":0,
            "msgFormClose":20
         },
         "stopOutgoing":true
      }
   ],
   "strategy":[
      {
         "severity":"1",
         "respondRule":"15",
         "createTime":1612665305505,
         "checkRule":"1",
         "name":"test",
         "dripScan":false,
         "id":104,
         "classification":[
            {
               "checkExpr":"1",
               "name":"关键字",
               "exceptRule":"",
               "id":1
            }
         ]
      }
   ],
   "businessType":{
      "opTypes":[ 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 20],
      "type":99
   }
})", "", "regular strategy success", true },
{ "", "", "both empty failure", false },
	};

	for (auto i = 0u; i < sizeof(strategies) / sizeof(strategies[0]); ++i)
	{

		BOOL bRet = ldfcr_MergeUpdateStrategy(strategies[i].reg_strategy, strategies[i].drip_strategy);

		EXPECT_EQ(bRet, strategies[i].expect) << strategies[i].comment;
	}
}

/*
* Test for utils functions
*/
// json 字段适配函数测试
TEST(JsonValueAdapter, ToString)
{
	string testName = ::testing::UnitTest::GetInstance()->current_test_info()->name();
	if (!JudgeFileJoinTest(testName))
		return;

	dlplog_info(g_log_handle, "[%d] ===========================================TEST_F ToString is started===========================================", __LINE__);
	struct {
		const char *json;
		bool		expect;
	} json_bool[] = {
		{ R"({"caseSensitive": true})", true },
		{ R"({"caseSensitive": "true"})", true },
		{ R"({"caseSensitive": 1})", true },
	};
	for (unsigned i = 0; i < sizeof(json_bool) / sizeof(json_bool[0]); ++i)
	{
		rapidjson::Json_Document doc;
		bool error = doc.Parse<0>(json_bool[i].json).HasParseError();
		ASSERT_FALSE(error);
		bool ok = false;
		bool b = JsonValueAdapterBool("caseSensitive", "bsU", doc, ok, -1);
		EXPECT_TRUE(ok);
		EXPECT_EQ(b, json_bool[i].expect);
	}

	struct {
		const char *json;
		unsigned long expect;
	} json_int[] = {
		{ R"({"size": 42})", 42 },
		{ R"({"size": "42"})", 42 },
	};
	for (unsigned i = 0; i < sizeof(json_int) / sizeof(json_int[0]); ++i)
	{
		rapidjson::Json_Document doc;
		bool error = doc.Parse<0>(json_int[i].json).HasParseError();
		ASSERT_FALSE(error);
		bool ok = false;
		unsigned long ret = JsonValueAdapterInt64("size", "bsU", doc, ok, -1);
		EXPECT_TRUE(ok);
		EXPECT_EQ(ret, json_int[i].expect);
	}

	struct {
		const char *json;
		std::string expect;
	} json_str[] = {
		{ R"({"severity": 42})", "42" },
		{ R"({"severity": "42"})", "42" },
	};
	for (unsigned i = 0; i < sizeof(json_str) / sizeof(json_str[0]); ++i)
	{
		rapidjson::Json_Document doc;
		bool error = doc.Parse<0>(json_str[i].json).HasParseError();
		ASSERT_FALSE(error);
		bool ok = false;
		std::string ret = JsonValueAdapterString("severity", "bsU", doc, ok, -1);
		EXPECT_TRUE(ok);
		EXPECT_EQ(ret, json_str[i].expect);
	}
}

#if 0
static void Parallel(Ldfcr &ldfcr, const char *path)
{
	IFCRResult *result;
	IFCRDripResult *dripResult;
	EXPECT_TRUE(ldfcr.detect(path, (void **)&result, (void **)&dripResult));
	EXPECT_FALSE(IsResultEmpty(result));
	EXPECT_TRUE(IsDripResultEmpty(dripResult));
	result->Release();
	if (dripResult)
		dripResult->Release();
}

TEST_F(LdfcrFixture, TaskIntegrityCheck) {
	string testName = ::testing::UnitTest::GetInstance()->current_test_info()->name();
	if (!JudgeFileJoinTest(testName))
		return;

	std::string completePath = GetCompletePath("/test/data/strategy.data_single_normal");//********
	dlplog_info(g_log_handle, "[%d] %s", __LINE__, completePath.c_str());

	EXPECT_EQ(ldfcr.init("test/data/strategy.data_single_normal"), 0);
	const int num = 8;
	size_t i = 0;

	while (i++ < loop)
	{
		std::vector<std::thread> threads(num);

		for (size_t j = 0; j < num; ++j)
		{
			threads[j] = std::thread([&]() {return Parallel(ldfcr, "test/text1.txt"); });
			//threads[j] = std::thread([&](){return ldfcr.detect_promise("test/text1.txt", nullptr, nullptr, 0, std::move(promises[j]));});
		}
		for (size_t j = 0; j < num; ++j)
		{
			threads[j].join();
		}
	}
}
#endif

