#pragma once
#include "afxwin.h"
#include "ldfcr.h"
#include "CodeT.h"
#include <io.h>
#include <windows.h>
#include "DragEdit.h"
#include "StringCoder.hpp"
#include <mutex>
#include "SimpleIni.h"
#include "ConfigLink.h"
#include <vector>
#include <set>

extern CSimpleIniA ini;

// Mode_P_Dialog 对话框

class Mode_P_Dialog : public CDialogEx
{
	DECLARE_DYNAMIC(Mode_P_Dialog)

public:
	Mode_P_Dialog(CWnd* pParent = NULL);   // 标准构造函数
	virtual ~Mode_P_Dialog();

// 对话框数据
#ifdef AFX_DESIGN_TIME
	enum { IDD = IDD_LDFCRTESTER_P };
#endif
private:
	ILDFcr*   m_pILDFcr;  // 检测指针
	ILDFcr*   m_pCheckILDFcr;  // 外发检测
	ILDFcr*   m_pEncryptILDFcr; // 智能加密
	ILDFcr*   m_pEncryptTagILDFcr; // 加密+加标签
	BOOL      m_bDoingDirFcr;
	BOOL	  m_bDoingDirTcr;

protected:
	virtual void DoDataExchange(CDataExchange* pDX);    // DDX/DDV 支持

	DECLARE_MESSAGE_MAP()
public:
	virtual BOOL OnInitDialog();

	CDragEdit m_CStrategyEdit;
	CString m_strStrategyFilePath;
	afx_msg void OnBnClickedOpenStrategy();
	//创建map容器
	map<int, string> idNameMap;
	std::vector<CString> v_idStrategy;
	set<CString> m_setFile;
	string m_strStrategyMerge;  //2023 8 17 弃用W接口 将宽字节做个转码

	afx_msg void OnBnClickedUpdateStrategy();
	afx_msg void OnBnClickedViewStrategy();
	afx_msg void OnBnClickedExecTcr();
	afx_msg void OnBnClickedOpenFile();
	afx_msg void ModifyInterfaceValues();
	CDragEdit m_CFcrEdit;
	CString m_strFilePath4FCR;
	string m_strFilePath4FCRMerge;  //2023 8 17 弃用W接口
	afx_msg void OnBnClickedBtnFcr();
	int m_intOpType;
	int m_iCount;
	int m_outTimeJug;
	afx_msg void OnBnClickedCheck9();
	BOOL m_pwd_error;
	CString m_str_time_count;
	afx_msg void OnBnClickedBtnTcr();
	int m_thread_num;
	afx_msg void OnBnClickedOpenFolder();
	CEdit m_CFolderPath4FcrEdit;
	CString m_strFolderPath4Fcr;
	afx_msg void OnBnClickedBtnThreadCheckFcrFolder();
	afx_msg void OnBnClickedBtnExecFcrFolder();

	CDragEdit m_mergeEdit;
	CString m_cstrMergeCstrategy;

	string m_strStrategyMerge2; 

private:
	std::list<std::wstring>  m_filename_listF; //改为全局
	std::list<std::wstring>  m_filename_listT; //改为全局
	int m_thread_count;					// 线程数
	std::mutex m_mutex;
	void ldfcrTesterThreadFCR(int thread_index);  //2023 11 2 文件多线程
	void ldfcrTesterThreadTCR(int thread_index);  //2023 11 7 文本多线程

	BOOL dlp_checkSingleFile(const wchar_t* file_path);
	BOOL dlp_checkSingleText(const wchar_t* file_path);//1008

	BOOL dlp_tcrInterface(std::string v_strFile, std::string& v_strTextContext);

	// 封装日志输出部分 MatchTypeF true 为文件匹配 false 为文本匹配
	BOOL dlp_Log(bool MatchTypeF, std::string v_strFile, IFCRResult* result_p, IFCRDripResult* drip_result_p);

	int m_file_count_sens;//敏感个数

	int checkCount; // 检测次数

	std::string m_IniPath;

	std::string strStrategyName;  //记录检测策略名

	// 2024 8 29 设置一个任务中断按钮 通过fcrparam中的interrupt 需要将每次进入配置的fcrparam设置为类成员对象
	ConfigLink *iniConfig;

	BOOL operateType; // 2025 1 8 合并单次检测接口 0为未设置状态 1为文件检测 2为文件夹加测 没测检测完充值为0

	BOOL CheckType;  // 检测类型

	void SetCheckType(int operateType, int checkType);
public:
	int m_iFileCount;
	int m_iSensFileCount;
	afx_msg void OnBnClickedBtnThreadCheckTcrFolder();
	afx_msg void OnBnClickedBtnExecTcrFolder();
	std::string GetIniPath();
	CComboBox m_ComboSelectIdName;
	afx_msg void OnBnClickedSetOutTime();
	BOOL FindRespRuleId(const string& strLogContent, int ruleId);
	afx_msg void OnBnClickedSetWithPwd();
	afx_msg void OnBnClickedAdvButtonSet();

	//输出框
	CEdit m_edit;
	afx_msg bool outPutFuc(const char * utf);
	int m_iIndex;  //计数君

	afx_msg void OnBnClickedGetCpu();
	afx_msg void OnBnClickedStopButton();
	afx_msg void OnBnClickedBtnOcrType();

	afx_msg void OnBnClickedBtn2Open();

private:
	enum
	{
		Check_Common_P,
		Check_Encrypt_P,
		Check_Tag_P,
		Check_G,
	};

	enum
	{
		Check_None,
		Check_File,
		Check_Text,
		Check_Text_Dlg  // 输入文本选项
	};

	enum
	{
		Operate_None,
		Operate_Single,
		Operate_Dir
	};
private:
	afx_msg void OnBnClickedBtnView();
	afx_msg void OnBnClickedBtnClearPt();
	afx_msg void OnBnClickedBrnClearLx();
	bool m_radioSelect;  // true - common p false - tag p
	afx_msg void OnBnClickedRadio1();
	afx_msg void OnBnClickedRadio2();
	afx_msg void OnBnClickedRadio3();
	
	int m_iCheckType; // 0 1 2 commonP tagP G
	int GetCheckType();
	int m_iCountFile;
	std::string strCheckType;
public:
	afx_msg void OnEnChangeEdit14();
	CComboBox m_combox_detect_file;
	afx_msg void OnBnClickedBtnComFcr();
	afx_msg void OnBnClickedBtnComTcr();
	afx_msg void OnBnClickedGetLeakStyle();
	afx_msg void OnBnClickedGetError();
	afx_msg void OnBnClickedRadio4();
	afx_msg void OnBnClickedBtnStategySelect();

	afx_msg void updateStrategyComBox();

	afx_msg void StrategySelect();  // 策略选取封装

	CEdit m_cstrFileProp_Edit;
	afx_msg void OnBnClickedBtnFileprop();
};
