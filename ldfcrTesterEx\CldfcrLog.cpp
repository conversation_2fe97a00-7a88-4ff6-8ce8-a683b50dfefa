﻿// CldfcrLog.cpp : 实现文件
//

#include "stdafx.h"
#include "ldfcrTester.h"
#include "CldfcrLog.h"
#include "afxdialogex.h"
#include <fstream>
#include <string>
#include "CodeT.h"


// CldfcrLog 对话框

IMPLEMENT_DYNAMIC(CldfcrLog, CDialog)

CldfcrLog::CldfcrLog(CWnd* pParent /*=NULL*/)
	: CDialog(IDD_DIALOG_LOG, pParent)
{

}

CldfcrLog::CldfcrLog(CString v_vstr, CWnd * pParent)
	: CDialog(IDD_DIALOG_LOG, pParent)
{
	this->m_showLogPath = v_vstr;

	m_editLog.Clear();
}

CldfcrLog::~CldfcrLog()
{
}

void CldfcrLog::DoDataExchange(CDataExchange* pDX)
{
	CDialog::DoDataExchange(pDX);
	DDX_Control(pDX, IDC_EDIT_LOG, m_editLog);
}


BEGIN_MESSAGE_MAP(CldfcrLog, CDialog)
	ON_BN_CLICKED(IDC_LOG_FLASH, &CldfcrLog::OnBnClickedLogFlash)
	ON_BN_CLICKED(IDC_LOG_EXIT, &CldfcrLog::OnBnClickedLogExit)
	ON_WM_CLOSE()
	ON_BN_CLICKED(IDC_LOG_EXIT, &CldfcrLog::OnBnClickedLogExit)
	ON_WM_DESTROY()
END_MESSAGE_MAP()


// CldfcrLog 消息处理程序


void CldfcrLog::OnBnClickedLogFlash()
{
	// TODO: 在此添加控件通知处理程序代码

	//清理edit control内容
	m_editLog.Clear();

	CEdit *pEdit = (CEdit*)GetDlgItem(IDC_EDIT_LOG);//获取edit control控件

	std::ifstream in(m_showLogPath);  //打开文件
	std::string line;
	std::string test;

	std::string::size_type idx;
	CString c_string = 0;
	if (!in) // 无该文件
	{
		MessageBox(_T("暂无日记!"));
		return;
	}
	int linecount = CountLogLine();
	int count = 0;
	int countflag = linecount;
	while (getline(in, test))
	{
		count++;
		if (count <= countflag)
		{
			line = test.c_str();
			c_string = line.c_str();
			LPCTSTR lp = c_string;
			m_editLog.LineScroll(m_editLog.GetLineCount());//滚轮默认自动跳转到最后
		}
	}
	in.close();

	// 取消文本选中
	pEdit->SetFocus();
	pEdit->SetSel(-1, -1);
}

int CldfcrLog::CountLogLine()
{

	std::ifstream in(m_showLogPath);
	std::string line;
	std::string Getline;
	if (!in) // 有该文件
	{
		return -1;
	}
	int linecount = 0;
	while (getline(in, Getline))
	{
		linecount++;
	}
	return linecount;
}

BOOL CldfcrLog::OnInitDialog()
{
	CDialog::OnInitDialog();

	CWnd *pWnd = GetDlgItem(IDC_EDIT_LOG);
	CString strMfc;
	
	// TODO: 在此添加控件通知处理程序代码
	CFile file(m_showLogPath, CFile::modeRead);

	int len = file.GetLength();//获取file文件中内容的长度；

	char *data = new char[len + 1];//定义一个存放数据的指针；

	memset(data, 0, len + 1);//   将已开辟内存空间 data的,长度为len+1首 个字节的值设为值 0

	file.Read(data, len);//读取文件内容并赋值给data;

						 //std::string _str;
						 //_str.assign(data, strlen(data));
						 //_str = data;
						 //strMfc = _str.c_str();

						 /*utf8转GBK*/
	std::string _str;
	_str = Utf8ToGbk(data);
	strMfc = _str.c_str();

	// 将日志内容显示在编辑框中	
	m_editLog.SetWindowText(strMfc);
	m_editLog.LineScroll(m_editLog.GetLineCount());//滚轮默认自动跳转到最后
	m_editLog.Clear();//测试是否被清空
	return TRUE;
}

//显示日志编辑框
void CldfcrLog::OnEnChangeEditLog()
{
	// TODO:  如果该控件是 RICHEDIT 控件，它将不
	// 发送此通知，除非重写 CDialog::OnInitDialog()
	// 函数并调用 CRichEditCtrl().SetEventMask()，
	// 同时将 ENM_CHANGE 标志“或”运算到掩码中。

	// TODO:  在此添加控件通知处理程序代码
}


void CldfcrLog::OnClose()
{
	// TODO: 在此添加消息处理程序代码和/或调用默认值

	CDialog::OnClose();
}


void CldfcrLog::OnBnClickedLogExit()
{
	// TODO: 在此添加控件通知处理程序代码
	this->CDialog::OnCancel();
}


void CldfcrLog::OnDestroy()
{
	CDialog::OnDestroy();

	// TODO: 在此处添加消息处理程序代码
}


void CldfcrLog::OnCancel()
{
	// TODO: 在此添加专用代码和/或调用基类

	CDialog::OnCancel();
}
