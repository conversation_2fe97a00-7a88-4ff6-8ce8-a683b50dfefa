#include "ldfcrtest.h"
#include "../utils.h"
#include "SimpleIni.h"
#include "DlpULog.h"
//#include <sys/time.h>
#include <iomanip>
#include <ctime>
#include <chrono>
#include <string.h>

LOG_HANDLE   g_log_handle = -1;

ldfcrtest::ldfcrtest()
{

}
ldfcrtest::~ldfcrtest()
{

}

CSimpleIniA ini;
void ldfcrtest::initSetting()
{
	ini.SetUnicode(1);
	std::string iniPath = GetCurPath() + "ldfcrTest.ini";
	//printf("%s\n",iniPath.c_str());
	int rc = ini.LoadFile(iniPath.c_str());
	if (rc) // rc == 0 表示正常打开
	{

		int tempElement;
		char Select;
		printToGbk("初次使用需要初始化ini配置文件\n");

		printToGbk("是否选用默认ini配置,使用输入y\n");
		scanf("%c", &Select);
		if (Select == 'y' || Select == 'Y')
		{
			ini.SetValue("MODE", "mode", "P");
			ini.SetValue("FUNC", "Func", "F");
			ini.SetValue("TYPE", "Type", "N");
			ini.SetValue("OPTYPE", "optype", "1");
			ini.SetValue("LDFCR_MODULE", "m_bocr", "1");
			ini.SetValue("LDFCR_MODULE", "m_bocr_embed", "1");
			ini.SetValue("pwdError", "user_password", "123456");
			ini.SetValue("pwdError", "m_pwdError", "1");
			ini.SetValue("LDFCR_RETURN_JSON_TAG", "m_btag", "0");
			ini.SetValue("LDFCR_CKECK_ALL", "m_bcheckALL", "1");
			ini.SetValue("outTime", "m_outTimeJug", "0");
			ini.SetValue("CIRCULATE", "circulate", "1");
			ini.SetValue("THREAD_COUNT", "thread_count ", "1");
			ini.SetValue("CONTEXTCOUT", "contextCount", "0");
			// 默认开启高级算法秘钥空值
			ini.SetValue("KEY", "NormalKey", "");
			ini.SaveFile(iniPath.c_str(), 0);
		}
		else
		{
			getchar();
			printToGbk("选择程序模式，全局输入G，对象输入P\n");
			scanf("%c", &Select);
			if (Select == 'g' || Select == 'G')
			{
				ini.SetValue("MODE", "mode", "G");
			}
			else
			{
				ini.SetValue("MODE", "mode", "P");
			}
			getchar();

			printToGbk("选择检测程序模式,文件检测输入F，文本检测输入T\n");
			scanf("%c", &Select);
			if (Select == 'f' || Select == 'F')
			{
				ini.SetValue("FUNC", "Func", "F");
			}
			else
			{
				ini.SetValue("FUNC", "Func", "T");
			}
			getchar();

			printToGbk("选择检测类型,常规检测输入N，只检测普通输入R，只检测零星输入D\n");
			scanf("%c", &Select);
			if (Select == 'r' || Select == 'R')
			{
				ini.SetValue("TYPE", "Type", "R");
			}
			else if (Select == 'd' || Select == 'D')
			{
				ini.SetValue("TYPE", "Type", "D");
			}
			else
			{
				ini.SetValue("TYPE", "Type", "N");
			}
			getchar();

			printToGbk("输入敏感检测操作类型，目前有效值在1~256\n");
			scanf("%d", &tempElement);
			char itc[10];
			sprintf(itc, "%d", tempElement);
			ini.SetValue("OPTYPE", "optype", itc);
			getchar();

			printToGbk("是否开启OCR，开启请输入y\n");
			scanf("%c", &Select);
			if (Select == 'y' || Select == 'Y')
			{
				ini.SetValue("LDFCR_MODULE", "m_bocr", "1");
			}
			else
			{
				ini.SetValue("LDFCR_MODULE", "m_bocr", "0");
			}
			getchar();

			printToGbk("是否开启OCR_EMBED，开启请输入y\n");
			scanf("%c", &Select);
			if (Select == 'y' || Select == 'Y')
			{
				ini.SetValue("LDFCR_MODULE", "m_bocr_embed", "1");
			}
			else
			{
				ini.SetValue("LDFCR_MODULE", "m_bocr_embed", "0");
			}
			getchar();

			printToGbk("是否开启密码错误阻断,开启请输入y\n");
			scanf("%c", &Select);
			getchar();
			if (Select == 'y' || Select == 'Y')
			{
				printToGbk("请输入密码进行验证，大部分情况是123456：\n");
				std::string inputPassword;
				std::string correctPassword = "123456";
				std::getline(std::cin, inputPassword);

				ini.SetValue("pwdError", "user_password", inputPassword.c_str());
				if (inputPassword != correctPassword)
				{
					ini.SetValue("pwdError", "m_pwdError", "1");
				}
				else
				{
					ini.SetValue("pwdError", "m_pwdError", "0");
				}
			}
			else
			{
				ini.SetValue("pwdError", "m_pwdError", "0");
			}

			printToGbk("是否只要标签检测结果，开启请输入y\n");
			scanf("%c", &Select);
			if (Select == 'y' || Select == 'Y')
			{
				ini.SetValue("LDFCR_RETURN_JSON_TAG", "m_btag", "1");
			}
			else
			{
				ini.SetValue("LDFCR_RETURN_JSON_TAG", "m_btag", "0");
			}
			getchar();

			printToGbk("是否全文检测，开启请输入y\n");
			scanf("%c", &Select);
			if (Select == 'y' || Select == 'Y')
			{
				ini.SetValue("LDFCR_CKECK_ALL", "m_bcheckALL", "1");
			}
			else
			{
				ini.SetValue("LDFCR_CKECK_ALL", "m_bcheckALL", "0");
			}
			getchar();

			//printToGbk("是否开启FILEXT，开启请输入y\n");
			//scanf("%c", &Select);
			//if (Select == 'y' || Select == 'Y')
			//{
			//	ini.SetValue("LDFCR_MODULE", "m_filext", "1");
			//}
			//else
			//{
			//	ini.SetValue("LDFCR_MODULE", "m_filext", "0");
			//}
			//getchar();

			printToGbk("输入阻断毫秒\n");
			scanf("%d", &tempElement);
			sprintf(itc, "%d", tempElement);
			ini.SetValue("outTime", "m_outTimeJug", itc);
			getchar();

			printToGbk("检测循环次数\n");
			scanf("%d", &tempElement);
			sprintf(itc, "%d", tempElement);
			ini.SetValue("CIRCULATE", "circulate", itc);
			getchar();

			printToGbk("文件夹检测线程数，建议1 - 32\n");
			scanf("%d", &tempElement);
			if (tempElement >= 1 && tempElement <= 32)
			{
				sprintf(itc, "%d", tempElement);
			}
			else
			{
				sprintf(itc, "%d", 1);
			}
			ini.SetValue("THREAD_COUNT", "thread_count ", itc);
			getchar();

			printToGbk("输入匹配文本时，敏感关键字上下文前后字符个数\n");
			scanf("%d", &tempElement);
			sprintf(itc, "%d", tempElement);
			ini.SetValue("CONTEXTCOUT", "contextCount", itc);
			getchar();

			// 默认开启高级算法秘钥空值
			ini.SetValue("KEY", "NormalKey", "");
			ini.SaveFile(iniPath.c_str(), 0);
		}

	}
}

/*
* 日志初始化
*/
void ldfcrtest::initLog()
{
	dlplog_init("ldfcrTest.log");

	//开启日志功能
	g_log_handle = dlplog_open("ldfcrTest");
}

void ldfcrtest::GetSetting(INISetting &iset)
{
	std::string iniPath = GetCurPath() + "ldfcrTest.ini";
	printToGbkWithN(iniPath.c_str());
	//printf("%s\n", iniPath.c_str());
	ini.LoadFile(iniPath.c_str());
	//system("pause");

	if (ini.GetValue("MODE", "mode", NULL) == NULL)
	{
		iset.modeSelect = "P";
		ini.SetValue("MODE", "mode", "P");
		ini.SaveFile(iniPath.c_str(), 0);
	}
	else
	{
		iset.modeSelect = ini.GetValue("MODE", "mode", NULL);
	}
	if (ini.GetValue("FUNC", "Func", NULL) == NULL)
	{
		iset.FuncSelect = "F";
		ini.SetValue("FUNC", "Func", "F");
		ini.SaveFile(iniPath.c_str(), 0);
	}
	else
	{
		iset.FuncSelect = ini.GetValue("FUNC", "Func", NULL);
	}
	if (ini.GetValue("TYPE", "Type", NULL) == NULL)
	{
		iset.CheckType = "N";
		ini.SetValue("TYPE", "Type", "N");
		ini.SaveFile(iniPath.c_str(), 0);
	}
	else
	{
		iset.CheckType = ini.GetValue("TYPE", "Type", NULL);
	}
	if (ini.GetValue("KEY", "NormalKey", NULL) == NULL)
	{
		iset.advInitParam = " ";
		ini.SetValue("KEY", "NormalKey", " ");
		ini.SaveFile(iniPath.c_str(), 0);
	}
	else
	{
		iset.advInitParam = ini.GetValue("KEY", "NormalKey", NULL);
	}
	iset.thread_count = ini.GetLongValue("THREAD_COUNT", "thread_count", 0);
	if (iset.thread_count == 0)
	{
		iset.thread_count = 1;
	}
	iset.optype = ini.GetLongValue("OPTYPE", "optype", 0);
	iset.bocrSelect = ini.GetValue("LDFCR_MODULE", "m_bocr", NULL);
	iset.bocr_embedSelect = ini.GetValue("LDFCR_MODULE", "m_bocr_embed", NULL);
	iset.block_pwderror = ini.GetLongValue("pwdError", "m_pwdError", NULL);
	//iset.filextSelect = ini.GetValue("LDFCR_MODULE", "m_filext", NULL);
	iset.outTime = ini.GetLongValue("OUTIME", "m_outTimeJug", 0);
	iset.circulate = ini.GetLongValue("CIRCULATE", "circulate", 0);
	iset.nearbyLen = ini.GetLongValue("CONTEXTCOUT", "contextCount", 0);
	iset.TagRespond = ini.GetLongValue("LDFCR_RETURN_JSON_TAG", "m_btag", 0);
	iset.scanAll = ini.GetLongValue("LDFCR_CKECK_ALL", "m_bcheckALL", 0);
}

bool ldfcrtest::p_init(ILDFcr *pILDFcr, tString def_test_file)
{
#ifdef WIN32
	const wchar_t *v_csStrategy = NULL; //
	struct _stat st;
	int ret = _wstat(def_test_file.c_str(), &st);
	if ((0 != ret) || (_S_IFREG != (_S_IFREG & st.st_mode)))
	{
		return false;
	}

	//加载策略文件
	int iStrategyLen = st.st_size;
	char* stra_content = new char[iStrategyLen + 1];
	FILE* fp = _wfopen(def_test_file.c_str(), L"rb");
	if (NULL == fp)
	{
		return false;
	}

	int iReadLen = fread(stra_content, sizeof(char), iStrategyLen, fp);
	if (iReadLen != iStrategyLen)
	{
		free(stra_content);
		return false;
	}

	stra_content[iStrategyLen] = 0; //设置字符串结尾符


	fclose(fp);
	fp = NULL;

#else
	//获取策略文件的路径
	FILE *fp = fopen(def_test_file.c_str(), "r");
	if (!fp) {
		return false;
	}
	fseek(fp, 0, SEEK_END);
	long size = ftell(fp);
	fseek(fp, 0, SEEK_SET);
	char *stra_content = (char *)calloc(size + 1, 1);
	if (!stra_content)
	{
		return false;
	}
	size_t nret = fread(stra_content, 1, size, fp);
	if (nret != (size_t)size)
		return false;
	if (stra_content == NULL)
	{
		stra_content = "";
	}
	fclose(fp);
#endif // WIN32
	//printf("%s\n", stra_content);
	BOOL bRes = pILDFcr->updateStrategy(stra_content); //8.17

	if (!bRes) {
		printToGbk("update failed\n");
		free(stra_content);
		return false;
	}
	free(stra_content);
	return true;
}

bool ldfcrtest::g_init(tString def_test_file)
{
#ifdef WIN32
	const wchar_t *v_csStrategy = NULL; //
	struct _stat st;
	int ret = _wstat(def_test_file.c_str(), &st);
	if ((0 != ret) || (_S_IFREG != (_S_IFREG & st.st_mode)))
	{
		return false;
	}

	//加载策略文件
	int iStrategyLen = st.st_size;
	char* stra_content = new char[iStrategyLen + 1];
	FILE* fp = _wfopen(def_test_file.c_str(), L"rb");
	if (NULL == fp)
	{
		return false;
	}

	int iReadLen = fread(stra_content, sizeof(char), iStrategyLen, fp);
	if (iReadLen != iStrategyLen)
	{
		free(stra_content);
		return false;
	}

	stra_content[iStrategyLen] = 0; //设置字符串结尾符


	fclose(fp);
	fp = NULL;


#else
	//获取策略文件的路径
	FILE *fp = fopen(def_test_file.c_str(), "r");
	if (!fp) {
		return false;
	}
	fseek(fp, 0, SEEK_END);
	long size = ftell(fp);
	fseek(fp, 0, SEEK_SET);
	char *stra_content = (char *)calloc(size + 1, 1);
	if (!stra_content)
	{
		return false;
	}
	size_t nret = fread(stra_content, 1, size, fp);
	if (nret != (size_t)size)
		return false;
	if (stra_content == NULL)
	{
		stra_content = "";
	}
	fclose(fp);
#endif // WIN32

	BOOL bRes = ldfcr_UpdateStrategy(stra_content);
	if (!bRes) {
		printToGbk("update failed\n");
		free(stra_content);
		return false;
	}
	free(stra_content);
	return true;
}

bool ldfcrtest::p_detect(ILDFcr *pILDFcr, tString path, void **result_p, void **dripResult_p, LPFCRPARAM pfcrParam, INISetting &iset)
{
	if (strcmp(iset.bocrSelect.c_str(), "1") == 0)
	{
		ldfcr_ControlModule(LDFCR_MODULE_OCR, 1);
	}
	else
	{
		ldfcr_ControlModule(LDFCR_MODULE_OCR, 0);
	}

	if (strcmp(iset.bocr_embedSelect.c_str(), "1") == 0)
	{
		ldfcr_ControlModule(LDFCR_MODULE_OCR_EMBED, 1);
	}
	else
	{
		ldfcr_ControlModule(LDFCR_MODULE_OCR_EMBED, 0);
	}
	//if (strcmp(iset.filextSelect.c_str(), "1") == 0)
	//{
	//	ldfcr_ControlModule(LDFCR_MODULE_FILEXT, 1);
	//}
	//else
	//{
	//	ldfcr_ControlModule(LDFCR_MODULE_FILEXT, 0);
	//}

	ldfcr_ControlModule(LDFCR_MODULE_DBFP, 1);
	ldfcr_ControlModule(LDFCR_MODULE_FILEFP, 1);
	ldfcr_ControlModule(LDFCR_MODULE_SVM, 1);

	long size = 0;
	//char* content = get_text_from_file_test(path, &size);

	if (pfcrParam == NULL)
		pfcrParam = &fcrParam;
	if (result_p != NULL)
		*result_p = NULL;
	if (dripResult_p != NULL)
		*dripResult_p = NULL;

	bool sensitive = MatchFunc(path, pILDFcr, (void**)&result_p, (void**)&dripResult_p, pfcrParam, iset);

	return sensitive;
}

bool ldfcrtest::g_detect(tString path, void **result_p, void **dripResult_p, LPFCRPARAM pfcrParam, INISetting &iset)
{
	FCRPARAM fcrParam;
	fcrParam.use_all_rule = TRUE;   //启用所有规则
	fcrParam.target_class_code = 3; //达到或超过代码3，即停止
	fcrParam.output_detail = TRUE;  //要求返回时输出策略的细节信息
	fcrParam.devType = 1;
	fcrParam.opType = iset.optype;
	fcrParam.disableTEModules = 0;
	fcrParam.timeout = iset.outTime;
	fcrParam.m_nearbyLen = iset.nearbyLen;
	fcrParam.onlyreturnTagRespond = iset.TagRespond;
	fcrParam.scanWholeFile = iset.scanAll;
	//std::cout<<fcrParam.timeout<<std::endl;
	fcrParam.timeout - 0; //超时阻断时间

	if (strcmp(iset.bocrSelect.c_str(), "1") == 0)
	{
		ldfcr_ControlModule(LDFCR_MODULE_OCR, 1);
	}
	else
	{
		ldfcr_ControlModule(LDFCR_MODULE_OCR, 0);
	}

	if (strcmp(iset.bocr_embedSelect.c_str(), "1") == 0)
	{
		ldfcr_ControlModule(LDFCR_MODULE_OCR_EMBED, 1);
	}
	else
	{
		ldfcr_ControlModule(LDFCR_MODULE_OCR_EMBED, 0);
	}

	//if (strcmp(iset.filextSelect.c_str(), "1") == 0)
	//{
	//	ldfcr_ControlModule(LDFCR_MODULE_FILEXT, 1);
	//}
	//else
	//{
	//	ldfcr_ControlModule(LDFCR_MODULE_FILEXT, 0);
	//}

	long size = 0;
	//char* content = get_text_from_file_test(path, &size);

	if (pfcrParam == NULL)
		pfcrParam = &fcrParam;
	if (result_p != NULL)
		*result_p = NULL;
	if (dripResult_p != NULL)
		*dripResult_p = NULL;
	//bool sensitive = ldfcr_ExecuteTCR(content, size, pfcrParam, result_p, dripResult_p);

	bool sensitive = MatchFunc(path, nullptr, (void**)&result_p, (void**)&dripResult_p, pfcrParam, iset);

	return sensitive;
}

void ldfcrtest::Gdetect(std::string v_strategy, std::vector<tString> vec, INISetting &iset)
{
	ldfcr_InitStartup();

	// // 插入秘钥测试
	// if(!iset.advInitParam.empty())
	// {
	// 	BOOL AdvInit = ldfcr_SetAdvInitParam(iset.advInitParam.c_str());//231010
	// 	if(AdvInit > 0)
	// 	{
	// 		printf("设置高级算法秘钥成功:[%s]\r\n",iset.advInitParam.c_str());
	// 	}
	// }
	tString strStrategy;
#if defined(_WIN32)
	strStrategy = string_to_wstring(v_strategy);
#elif defined(__GNUC__)
	strStrategy = v_strategy;
#endif
	bool iniRes = g_init(strStrategy);
	{
		if (!iniRes)
		{
			printToGbk("error init strategy\n");
			ldfcr_StopRelease();
			return;
		}
	}

	//2024 4 9 拆分文件检测和文件夹检测
	//根据容器大小 文件检测做循环次数，文件夹检测只做一次检测
	//ini中配置线程数，线程数为1不启动线程池，其他情况启用线程池
	int n_files = vec.size();
	if (1 == n_files)
	{
		int circulate = iset.circulate;
		while (circulate)
		{
			MatchStrategyG(strStrategy.c_str(), (tChar*)vec.front().c_str(), iset, 0, circulate);
			circulate--;
		}
	}
	else
	{
		if (1 == iset.thread_count)
		{
			for (int i = 0; i<n_files; ++i)
			{
				int n_CurI = i + 1;
				MatchStrategyG(strStrategy, (tChar*)vec[i].c_str(), iset, n_files, n_CurI);
			}
		}
		else
		{
			ThreadPool pool(iset.thread_count);
			pool.init();
			printf("thread pool start, [%d] thread counts\n", iset.thread_count);
			tString strCheckFile;
			int n_CurI = 0;
			std::vector<std::future<void>> m_vecFuture;
			for (int i = 0; i < vec.size(); ++i)
			{
				n_CurI = n_CurI + 1;
				auto res = pool.submit(std::bind(&ldfcrtest::MatchStrategyG, this, strStrategy, vec[i], iset, n_files, n_CurI));
				m_vecFuture.push_back(std::move(res));
			}
			for (auto& res : m_vecFuture)
			{
				res.get();
			}
			pool.shutdown();
			printToGbk("thread pool end\n");
		}
	}


	ldfcr_StopRelease();
}

void ldfcrtest::Pdetect(std::string v_strategy, std::vector<tString> vec, INISetting &iset)
{
	ldfcr_InitStartup();

	ILDFcr *pILDFcr = NULL;
	ldfcr_CreateInstance((ILDFcrHandle*)(&pILDFcr));
	if (!pILDFcr) {
		printToGbk("ldfcr instance create fail\n");
		return;
	}

	//高级秘钥设置
	if (!iset.advInitParam.empty())
	{
		BOOL AdvInit = pILDFcr->SetAdvInitParam(iset.advInitParam.c_str(), iset.advInitParam.c_str());//231010
		if (AdvInit > 0)
		{
			std::string strGbk = "设置高级算法秘钥成功:[" + iset.advInitParam + "]\n";
			//printf("设置高级算法秘钥成功:[%s]\r\n", iset.advInitParam.c_str());
			printToGbk(strGbk.c_str());
		}
	}
	tString strStrategy;
#if defined(_WIN32)
	strStrategy = string_to_wstring(v_strategy);
#elif defined(__GNUC__)
	strStrategy = v_strategy;
#endif
	bool iniRes = p_init(pILDFcr, strStrategy);
	{
		if (!iniRes)
		{
			printToGbk("error init strategy\n");
			pILDFcr->Release();
			ldfcr_StopRelease();
			return;
		}
	}

	//2024 4 9 拆分文件检测和文件夹检测
	//根据容器大小 文件检测做循环次数，文件夹检测只做一次检测
	//ini中配置线程数，线程数为1不启动线程池，其他情况启用线程池
	int n_files = vec.size();
	if (1 == n_files)
	{
		int circulate = iset.circulate;
		while (circulate)
		{
			MatchStrategyP(strStrategy, (tChar*)vec.front().c_str(), pILDFcr, iset, 0, circulate);
			circulate--;
		}
	}
	else
	{
		if (1 == iset.thread_count)
		{
			for (int i = 0; i<n_files; ++i)
			{
				int n_CurI = i + 1;
				MatchStrategyP(strStrategy, (tChar*)vec[i].c_str(), pILDFcr, iset, n_files, n_CurI);
			}
		}
		else
		{
			ThreadPool pool(iset.thread_count);
			pool.init();
			printf("thread pool start, [%d] thread counts\n", iset.thread_count);
			tString strCheckFile;
			int n_CurI = 0;
			std::vector<std::future<void>> m_vecFuture;
			for (int i = 0; i < vec.size(); ++i)
			{
				n_CurI = n_CurI + 1;
				auto res = pool.submit(std::bind(&ldfcrtest::MatchStrategyP, this, strStrategy, vec[i], pILDFcr, iset, n_files, n_CurI));
				m_vecFuture.push_back(std::move(res));
			}
			for (auto& res : m_vecFuture)
			{
				res.get();
			}
			pool.shutdown();
			printToGbk("thread pool end\n");
		}
	}
	pILDFcr->Release();
	ldfcr_StopRelease();
}

void ldfcrtest::MatchStrategyG(tString v_strategyFile, tString v_strFile, INISetting &iset, int n_Count, int n_curCount)
{
	auto startT = std::chrono::duration_cast<std::chrono::microseconds>(std::chrono::system_clock::now().time_since_epoch()).count();
	int detectRes = g_detect(v_strFile, NULL, NULL, NULL, iset);
	std::string strRes;
	if (detectRes == 0)
	{
#if defined(_WIN32)
		strRes = "[" + wstring_to_string(v_strategyFile.c_str()) + "] [" + wstring_to_string(v_strFile.c_str()) + "] Matching failed [" + std::to_string(n_curCount) + "/" + std::to_string(n_Count) + "]\n";
#elif defined(__GNUC__)
		strRes = "[" + charToString(v_strategyFile.c_str()) + "] [" + charToString(v_strFile.c_str()) + "] Matching failed [" + std::to_string(n_curCount) + "/" + std::to_string(n_Count) + "]\n";
#endif
	}
	else
	{
		auto endT = std::chrono::duration_cast<std::chrono::microseconds>(std::chrono::system_clock::now().time_since_epoch()).count();
		double tim = (double)(endT - startT) / 1000;
		char itc[10];
		sprintf(itc, "%.3f", tim);
#if defined(_WIN32)
		strRes = "[" + wstring_to_string(v_strFile.c_str()) + "] 文本匹配成功，用时" + itc + "毫秒 [" + std::to_string(n_curCount) + "/" + std::to_string(n_Count) + "]\n";
#elif defined(__GNUC__)
		strRes = "[" + charToString(v_strFile.c_str()) + "] 文本匹配成功，用时" + itc + "毫秒 [" + std::to_string(n_curCount) + "/" + std::to_string(n_Count) + "]\n";
#endif
		//printf("[%s] 文本匹配成功，用时%s毫秒 [%d/%d]\n", v_strFile, itc, n_curCount, n_Count);
		if (0 == n_Count)
		{
			//printf("[%s] [%s] 文本匹配成功，用时%s毫秒 [%d]\n", v_strategyFile, v_strFile, itc, n_curCount);
#if defined(_WIN32)
			strRes = "[" + wstring_to_string(v_strategyFile.c_str()) + "] [" + wstring_to_string(v_strFile.c_str()) + "] 文本匹配成功，用时" + itc + "毫秒 [" + std::to_string(n_curCount) + "]\n";
#elif defined(__GNUC__)
			strRes = "[" + charToString(v_strategyFile.c_str()) + "] [" + charToString(v_strFile.c_str()) + "] 文本匹配成功，用时" + itc + "毫秒 [" + std::to_string(n_curCount) + "]\n";
#endif
		}
		else
		{
			//printf("[%s] [%s] 文本匹配成功，用时%s毫秒 [%d/%d]\n", v_strategyFile, v_strFile, itc, n_curCount, n_Count);
#if defined(_WIN32)
			strRes = "[" + wstring_to_string(v_strategyFile.c_str()) + "] [" + wstring_to_string(v_strFile.c_str()) + "] 文本匹配成功，用时" + itc + "毫秒 [" + std::to_string(n_curCount) + "/" + std::to_string(n_Count) + "]\n";
#elif defined(__GNUC__)
			strRes = "[" + charToString(v_strategyFile.c_str()) + "] [" + charToString(v_strFile.c_str()) + "] 文本匹配成功，用时" + itc + "毫秒 [" + std::to_string(n_curCount) + "/" + std::to_string(n_Count) + "]\n";
#endif
		}
	}
	printToGbk(strRes.c_str());
}

void ldfcrtest::MatchStrategyP(tString v_strategyFile, tString v_strFile, ILDFcr *pILDFcr, INISetting &iset, int n_Count, int n_curCount)
{
	auto startT = std::chrono::duration_cast<std::chrono::microseconds>(std::chrono::system_clock::now().time_since_epoch()).count();

	fcrParam.use_all_rule = TRUE;   //启用所有规则
	fcrParam.target_class_code = 3; //达到或超过代码3，即停止
	fcrParam.output_detail = TRUE;  //要求返回时输出策略的细节信息
	fcrParam.devType = 1;
	fcrParam.opType = iset.optype;
	fcrParam.disableTEModules = 0;
	fcrParam.timeout = iset.outTime;
	fcrParam.m_nearbyLen = iset.nearbyLen;
	fcrParam.onlyreturnTagRespond = iset.TagRespond;
	fcrParam.scanWholeFile = iset.scanAll;
	//std::cout<<fcrParam.timeout<<std::endl;
	fcrParam.timeout - 0; //超时阻断时间	
	fcrParam.block_on_pwderror = iset.block_pwderror;//密码错误阻断

	int detectRes = p_detect(pILDFcr, v_strFile, NULL, NULL, &fcrParam, iset);
	std::string strRes;
	if (detectRes == 0)
	{
		//printf("[%s] [%s] Matching failed [%d/%d]\n", v_strategyFile, v_strFile, n_curCount, n_Count);
#if defined(_WIN32)
		strRes = "[" + wstring_to_string(v_strategyFile.c_str()) + "] [" + wstring_to_string(v_strFile.c_str()) + "] Matching failed [" + std::to_string(n_curCount) + "/" + std::to_string(n_Count) + "]\n";
#elif defined(__GNUC__)
		strRes = "[" + charToString(v_strategyFile.c_str()) + "] [" + charToString(v_strFile.c_str()) + "] Matching failed [" + std::to_string(n_curCount) + "/" + std::to_string(n_Count) + "]\n";
#endif
	}
	else
	{
		auto endT = std::chrono::duration_cast<std::chrono::microseconds>(std::chrono::system_clock::now().time_since_epoch()).count();
		double tim = (double)(endT - startT) / 1000;
		char itc[10];
		sprintf(itc, "%.3f", tim);
		if (0 == n_Count)
		{
			//printf("[%s] [%s] 文本匹配成功，用时%s毫秒 [%d]\n", v_strategyFile, v_strFile, itc, n_curCount);
#if defined(_WIN32)
			strRes = "[" + wstring_to_string(v_strategyFile.c_str()) + "] [" + wstring_to_string(v_strFile.c_str()) + "] 文本匹配成功，用时" + itc + "毫秒 [" + std::to_string(n_curCount) + "]\n";
#elif defined(__GNUC__)
			strRes = "[" + charToString(v_strategyFile.c_str()) + "] [" + charToString(v_strFile.c_str()) + "] 文本匹配成功，用时" + itc + "毫秒 [" + std::to_string(n_curCount) + "]\n";
#endif
		}
		else
		{
			//printf("[%s] [%s] 文本匹配成功，用时%s毫秒 [%d/%d]\n", v_strategyFile, v_strFile, itc, n_curCount, n_Count);
#if defined(_WIN32)
			strRes = "[" + wstring_to_string(v_strategyFile.c_str()) + "] [" + wstring_to_string(v_strFile.c_str()) + "] 文本匹配成功，用时" + itc + "毫秒 [" + std::to_string(n_curCount) + "/" + std::to_string(n_Count) + "]\n";
#elif defined(__GNUC__)
			strRes = "[" + charToString(v_strategyFile.c_str()) + "] [" + charToString(v_strFile.c_str()) + "] 文本匹配成功，用时" + itc + "毫秒 [" + std::to_string(n_curCount) + "/" + std::to_string(n_Count) + "]\n";
#endif
		}
	}
	printToGbk(strRes.c_str());
}

BOOL ldfcrtest::MatchFunc(tString v_strFile, ILDFcr * pILDFcr, void ** result_p, void ** dripResult_p, LPFCRPARAM pfcrParam, INISetting & iset)
{
	initLog();
	printf("currently,mode is [%s] ,func is [%s] ,type is [%s]\n", iset.modeSelect.c_str(), iset.FuncSelect.c_str(), iset.CheckType.c_str());
	BOOL sensitive;
#if defined(_WIN32)
	std::string filename = wstring_to_string(v_strFile.c_str());
	if (pILDFcr == nullptr) // G
	{
		if (strcmp(iset.FuncSelect.c_str(), "F") == 0) //2024 1 4 插入文件和文本分支
		{
			sensitive = ldfcr_ExecuteFCRW(v_strFile.c_str(), pfcrParam, result_p, dripResult_p);
			if(sensitive)
			{
				MatchLog(false, true, filename, result_p, dripResult_p);  // G F
			}
		}
		else if (strcmp(iset.FuncSelect.c_str(), "T") == 0)
		{
			std::string strContext = ContextRead(v_strFile.c_str());

			sensitive = ldfcr_ExecuteTCR(strContext.c_str(), -1, pfcrParam, result_p, dripResult_p);
			if(sensitive)
			{
				MatchLog(false, false, filename, result_p, dripResult_p);  // G T
			}
		}
	}
	else // P 
	{
		if (strcmp(iset.FuncSelect.c_str(), "F") == 0) //2024 1 4 插入文件和文本分支
		{
			sensitive = pILDFcr->executeFCRW(v_strFile.c_str(), pfcrParam, result_p, dripResult_p);
			if(sensitive)
			{
				MatchLog(true, true, filename, result_p, dripResult_p);  // P F
			}
		}
		else if (strcmp(iset.FuncSelect.c_str(), "T") == 0)
		{
			std::string strContext = ContextRead(v_strFile.c_str());

			sensitive = pILDFcr->executeTCR(strContext.c_str(), -1, pfcrParam, result_p, dripResult_p);
			if(sensitive)
			{
				MatchLog(true, false, filename, result_p, dripResult_p);  // P T
			}
		}
	}
#elif defined(__GNUC__)
	if (pILDFcr == nullptr) // G
	{
		if (strcmp(iset.FuncSelect.c_str(), "F") == 0) //2024 1 4 插入文件和文本分支
		{
			sensitive = ldfcr_ExecuteFCR(v_strFile.c_str(), pfcrParam, result_p, dripResult_p);
			if(sensitive)
			{
				MatchLog(false, true, v_strFile, result_p, dripResult_p); // G F
			}
		}
		else if (strcmp(iset.FuncSelect.c_str(), "T") == 0)
		{
			std::string strContext = ContextRead(v_strFile.c_str());

			sensitive = ldfcr_ExecuteTCR(strContext.c_str(), -1, pfcrParam, result_p, dripResult_p);
			if(sensitive)
			{
				MatchLog(false, false, v_strFile, result_p, dripResult_p); // G T
			}
		}
	}
	else // P 
	{
		if (strcmp(iset.FuncSelect.c_str(), "F") == 0) //2024 1 4 插入文件和文本分支
		{
			sensitive = pILDFcr->executeFCR(v_strFile.c_str(), pfcrParam, result_p, dripResult_p);
			if(sensitive)
			{
				MatchLog(true, true, v_strFile, result_p, dripResult_p); // P F
			}
		}

		else if (strcmp(iset.FuncSelect.c_str(), "T") == 0)
		{
			std::string strContext = ContextRead(v_strFile.c_str());

			sensitive = pILDFcr->executeTCR(strContext.c_str(), -1, pfcrParam, result_p, dripResult_p);
			if(sensitive)
			{
				MatchLog(true, false, v_strFile, result_p, dripResult_p);  // P T
			}
		}
	}
#endif
	if (sensitive == 0)
	{
		return sensitive;
	}
	else
	{
		//普通 / 零星 / 常规 / 判断
		if (strcmp(iset.CheckType.c_str(), "R") == 0)
		{
			if (result_p == nullptr)
			{
				return 0;
			}
		}
		else if (strcmp(iset.CheckType.c_str(), "D") == 0)
		{
			if (dripResult_p == nullptr)
			{
				return 0;
			}
		}
		else
		{
			return sensitive;
		}
	}

}

BOOL ldfcrtest::MatchLog(bool MatchTypeP, bool matchFuncF, const std::string v_strFile, void** result_p, void** dripResult_p)
{
	if (NULL == result_p) {
		return FALSE;
	}
	IFCRResult* result_pp = static_cast<IFCRResult*>(*result_p);
	if (NULL == result_pp) {
		return FALSE;
	}
	if (MatchTypeP)
	{
		dlplog_info(g_log_handle, "常规检测——P");
	}
	else
	{
		dlplog_info(g_log_handle, "常规检测——G");
	}
	result_pp->moveFirstPart();
	if (matchFuncF)
	{
		dlplog_info(g_log_handle, "常规检测——F");
		std::string FileType = result_pp->getFileExt();
		dlplog_info(g_log_handle, "[%s] match file type is: %s", __FUNCTION__, FileType.c_str());
	}
	else
	{
		dlplog_info(g_log_handle, "常规检测——T");
	}
	ICRPart* icrpaet = nullptr;
	while ((icrpaet = result_pp->moveNextPart()) != NULL)
	{
		int i3 = icrpaet->getClassCode();
		std::string gbTextTrimed = icrpaet->getTextTrimed();
		std::string gbStrategyMatched = icrpaet->getStrategyMatched();
		unsigned int uiMatchType = icrpaet->getStrategyMatchedType();
		dlplog_info(g_log_handle, "[%s] 当前被检测文件: %s", __FUNCTION__, v_strFile.c_str());
		dlplog_info(g_log_handle, "[%s] gbStrategyMatched: %s", __FUNCTION__, gbStrategyMatched.c_str());
		dlplog_info(g_log_handle, "[%s] gbClassCode: %d", __FUNCTION__, i3);
		dlplog_info(g_log_handle, "[%s] gbTextTrimed: %s", __FUNCTION__, gbTextTrimed.c_str());
		dlplog_info(g_log_handle, "[%s] strategy Match Type is: %u", __FUNCTION__, uiMatchType);
		if (matchFuncF)
		{
			std::string curFileType = icrpaet->getFileExt();
			std::string curSenFile = icrpaet->getSubFilePath();
			dlplog_info(g_log_handle, "[%s] current match file type is: %s", __FUNCTION__, curFileType.c_str());
			dlplog_info(g_log_handle, "[%s] current Sentive file type is: %s", __FUNCTION__, curSenFile.c_str());
		}
		dlplog_info(g_log_handle, "***********************************************************************************************");
	}

	result_pp->Release();
	*result_p = nullptr;

	if (NULL != dripResult_p)// 零星检测
	{
		IFCRDripResult* dripResult_pp = static_cast<IFCRDripResult*>(*dripResult_p);
		if (!dripResult_pp) {
			return FALSE;
		}
		dripResult_pp->moveFirstIncident();
		ICRIncident* incident = NULL;
		while ((incident = dripResult_pp->moveNextIncident()) != NULL)
		{
			if (strlen(incident->getStrategyName()) <= 0)
			{
				return FALSE;
			}
			incident->moveFirstMatch();
			ICRMatch* match = NULL;
			dlplog_info(g_log_handle, ("零星检测——P"));
			std::string gbStrategyMatched = incident->getStrategyMatched();
			std::string gbStrategyName = incident->getStrategyName();
			int iClassCode = incident->getClassCode();
			dlplog_info(g_log_handle, "[%s] 当前被检测文件: %s", __FUNCTION__, v_strFile.c_str());
			dlplog_info(g_log_handle, "[%s] gbStrategyMatched: %s", __FUNCTION__, gbStrategyMatched.c_str());
			dlplog_info(g_log_handle, "[%s] gbClassCode: %d", __FUNCTION__, iClassCode);
			dlplog_info(g_log_handle, "[%s] gbStrategyName: %s", __FUNCTION__, gbStrategyName.c_str());
			while ((match = incident->moveNextMatch()) != NULL)
			{
				if (strlen(match->getRuleName()) <= 0)
				{
					return FALSE;
				}
				uint64 i3 = match->getRuleId();
				std::string RuleName = match->getRuleName();
				std::string FileName = match->getFilename();
				std::string FilePath = match->getFilePath();
				int iOPType = match->getOPType();
				time_t timestamp = match->getTimestamp();
				std::string gbTextTrimed = match->getTextTrimed();
				std::string gbFCRInfo = match->getFCRInfo();
				dlplog_info(g_log_handle, "[%s] 匹配规则ID: %llu 规则名称: %s", __FUNCTION__, i3, RuleName.c_str());
				dlplog_info(g_log_handle, "[%s] 匹配文件名: %s", __FUNCTION__, FileName.c_str());
				dlplog_info(g_log_handle, "[%s] 匹配文件路径: %s", __FUNCTION__, FilePath.c_str());
				dlplog_info(g_log_handle, "[%s] 匹配操作类型: %d", __FUNCTION__, iOPType);
				dlplog_info(g_log_handle, "[%s] 匹配时间戳: %ld", __FUNCTION__, timestamp);
				dlplog_info(g_log_handle, "[%s] 匹配文本片段: %s", __FUNCTION__, gbTextTrimed.c_str());
				dlplog_info(g_log_handle, "[%s] 匹配检测时传入的相关信息(json字符串): %s", __FUNCTION__, gbFCRInfo.c_str());
				dlplog_info(g_log_handle, "***********************************************************************************************");
			}
		}
		dripResult_pp->Release();
		*dripResult_p = NULL;
	}
	return TRUE;
}
