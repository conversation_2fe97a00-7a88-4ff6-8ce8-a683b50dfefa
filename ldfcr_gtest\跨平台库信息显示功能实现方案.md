# 跨平台库信息显示功能实现方案

## 功能概述

为ldfcr_gtest和ldfcr_gtest_advance工具添加跨平台库信息显示功能，实现：
- 程序开头和结尾显示一致的组件信息
- 支持Linux (.so) 和 Mac (.dylib) 系统
- 按指定顺序排列组件
- 显示完整的绝对路径

## 核心修改文件

### 1. main.cpp 修改

#### 1.1 添加全局变量和自定义Environment类

```cpp
// 全局变量保存基础库信息，确保开头和结尾一致
#if defined(__GNUC__) || defined(__APPLE__)
static libaryVersionInfo* g_baselineLibVersion = nullptr;

// 简化的Environment类，只负责清理
class SimpleEnvironment : public ::testing::Environment {
public:
	~SimpleEnvironment() override {}
	void SetUp() override {} // 不做任何事，因为已经在main中初始化了
	void TearDown() override {
		ldfcr_StopRelease();
	}
};
#endif
```

#### 1.2 修改main函数逻辑

```cpp
#if defined(__GNUC__) || defined(__APPLE__)
    ::testing::AddGlobalTestEnvironment(new SimpleEnvironment);
	GetTestinfo();
	
	// === 初始化ldfcr并检测库信息 ===
	if (ldfcr_InitStartup()) {
		// 等待一小段时间确保所有动态库都加载完毕
		usleep(100000); // 100ms
		
		// === 显示程序启动时的完整库信息（开头） ===
		printf("\n=== Baseline Libraries (Program Startup) ===\n");
		g_baselineLibVersion = new libaryVersionInfo();
		g_baselineLibVersion->GetVersionInfoByOrder();
		g_baselineLibVersion->PrintVersionInfo();
	}
#elif WIN32
    ::testing::AddGlobalTestEnvironment(new Environment);
	GetTestinfo();
	PEinfo pe;
	pe.GetPEinfo();
#endif

    // 运行所有测试
    RUN_ALL_TESTS();
    
	GetTestinfo();
#ifdef WIN32
	pe.GetPEinfo();
#elif defined(__GNUC__) || defined(__APPLE__)
    // === 显示程序结束时的基础库信息（结尾，应该和开头一致） ===
    printf("\n=== Baseline Libraries (Program End) ===\n");
    if (g_baselineLibVersion != nullptr) {
        g_baselineLibVersion->PrintVersionInfo();
        delete g_baselineLibVersion;
        g_baselineLibVersion = nullptr;
    }
#endif
```

### 2. libaryVersionInfo.h 修改

#### 2.1 更新头文件包含

```cpp
#include <unistd.h>  // for getcwd and Mac system calls

#ifdef __APPLE__
#include <sys/types.h>
#endif
```

#### 2.2 添加Mac系统支持函数

```cpp
#ifdef __APPLE__
void getRealLoadedLibrariesMac()
{
    // Mac系统使用lsof命令获取加载的动态库，这比vmmap更可靠
    string cmd = "lsof -p " + to_string(getpid()) + " 2>/dev/null | grep '\\.dylib' | awk '{for(i=9;i<=NF;i++) printf \"%s \", $i; print \"\"}' | sed 's/ $//' | sort | uniq";
    
    FILE* pipe = popen(cmd.c_str(), "r");
    if (pipe == NULL) {
        printf("Warning: Failed to execute lsof command on Mac\n");
        return;
    }

    char line[1024];
    while (fgets(line, sizeof(line), pipe) != NULL) {
        string fullPath = string(line);
        // 移除换行符
        if (!fullPath.empty() && fullPath.back() == '\n') {
            fullPath.pop_back();
        }

        if (!fullPath.empty()) {
            std::vector<std::string> libNames;
            extractLibNameFromPath(fullPath, libNames);

            for (const auto& libName : libNames) {
                if (isTargetLibrary(libName, fullPath)) {
                    addLibraryIfNotExists(libName, fullPath);
                }
            }
        }
    }

    pclose(pipe);
}
#endif
```

#### 2.3 统一路径提取函数

```cpp
// 统一的路径提取函数，支持跨平台
void extractLibNameFromPath(const string& fullPath, vector<string>& libNames)
{     
    size_t lastSlash = fullPath.find_last_of('/');
    if (lastSlash == std::string::npos) return;
    
    std::string fileName = fullPath.substr(lastSlash + 1);
    
    // 跨平台文件扩展名处理
#if __linux__
    size_t versionPos = fileName.find(".so.");
    if (versionPos != std::string::npos) {
        fileName = fileName.substr(0, versionPos + 3);
    }
    if (fileName.length() > 3 && fileName.substr(fileName.length() - 3) == ".so") {
        fileName = fileName.substr(0, fileName.length() - 3);
    }
#elif __APPLE__
    size_t dylibPos = fileName.find(".dylib");
    if (dylibPos != std::string::npos) {
        fileName = fileName.substr(0, dylibPos);
    }
#endif
    
    // 移除lib前缀（通用逻辑）
    if (fileName.length() > 3 && fileName.substr(0, 3) == "lib") {
        fileName = fileName.substr(3);
    }

    if (!fileName.empty()) {
        libNames.push_back(fileName);
    }
}
```

#### 2.4 跨平台库路径检测

```cpp
string detectLibraryPath()
{
    vector<string> possiblePaths = {
        "libs_x64/",
        "lib/dlpcomm/",
        "../libs_x64/",
        "../lib/dlpcomm/"
    };

    for (const auto& path : possiblePaths) {
#if __linux__
        string testFile = path + "libtrcrt.so";
        FILE* file = fopen(testFile.c_str(), "r");
        if (file != NULL) {
            fclose(file);
            return path;
        }

        testFile = path + "TrArchive.so";
        FILE* file2 = fopen(testFile.c_str(), "r");
        if (file2 != NULL) {
            fclose(file2);
            return path;
        }
#elif __APPLE__
        string testFile = path + "libtrcrt.dylib";
        FILE* file = fopen(testFile.c_str(), "r");
        if (file != NULL) {
            fclose(file);
            return path;
        }

        testFile = path + "TrArchive.dylib";
        FILE* file2 = fopen(testFile.c_str(), "r");
        if (file2 != NULL) {
            fclose(file2);
            return path;
        }
#endif
    }

    printf("Warning: No valid library path found, using default lib/dlpcomm/\n");
    return "lib/dlpcomm/";
}
```

#### 2.5 优化PrintVersionInfo函数

```cpp
void PrintVersionInfo()
{
    for(int i = 0 ;i< m_vec.size(); ++i)
    {
        printf("%d. %s", i + 1, m_vec[i].c_str());

        if (i < m_libPaths.size()) {
            string fullPath = m_libPaths[i];
            
            // 如果是相对路径，转换为绝对路径
            if (fullPath[0] != '/') {
                char currentDir[1024];
                if (getcwd(currentDir, sizeof(currentDir)) != NULL) {
                    fullPath = string(currentDir) + "/" + fullPath;
                }
            }
            
            printf("   Location: %s\n", fullPath.c_str());
        }
    }
}
```

## 关键技术要点

### 1. 跨平台库检测方法
- **Linux**: 使用`/proc/self/maps`读取进程内存映射
- **Mac**: 使用`lsof`命令获取进程打开的文件

### 2. 组件排序
使用预定义顺序：
```cpp
vector<string> orderedLibs = {
    "ldfcr", "DlpPolicyEngine", "KWRuleEngine", "RegexRuleEngine",
    "FilePropEngine", "FileFpRuleEngine", "SVMRuleEngine", "FpDbRuleEngine",
    "TrCadFilter", "TrCompressFilter", "TrOLEFilter", "TrOOXMLFilter",
    "TrPdfFilter", "TrRtfFilter", "TrTextExtractor", "TrTxtFilter",
    "TrArchive", "TrODFFilter", "DlpOCR", "DlpSCR", "TrOCRFilter",
    "DlpULog", "trcrt", "lua", "pcre", "svm", "jieba", "xcrbnfa", "memstream"
};
```

### 3. 时机控制
- **初始化时机**: 在`ldfcr_InitStartup()`之后检测
- **显示时机**: 程序开头和结尾使用相同的检测结果

## 实施步骤

1. **备份原文件**
2. **修改main.cpp**: 添加全局变量和修改main函数逻辑
3. **修改libaryVersionInfo.h**: 添加Mac支持和优化函数
4. **编译测试**: 确保Linux和Mac都能正常编译
5. **功能验证**: 检查开头结尾信息一致性

## 预期效果

- Linux和Mac系统都能显示完整的库信息
- 开头和结尾显示相同的组件列表
- 组件按预定义顺序排列
- 显示完整的绝对路径
- 代码结构清晰，无冗余

## 注意事项

- 确保函数声明顺序正确，避免编译错误
- Mac系统需要`lsof`命令支持
- 路径显示使用`printf`而非`cout`以避免截断
- 删除重复的函数定义

## 常见问题及解决方案

### 1. 编译错误：函数未声明
**问题**: `detectLibraryPath`、`extractLibNameFromPath`等函数未声明
**解决**: 确保这些基础函数在调用它们的函数之前声明

### 2. Mac系统路径显示省略号
**问题**: 路径显示为`...d/Desktop/...`
**解决**:
- 使用`lsof`替代`vmmap`命令
- 使用`printf`替代`cout`
- 在显示时转换相对路径为绝对路径

### 3. 开头结尾信息不一致
**问题**: 开头显示3个库，结尾显示更多库
**解决**:
- 在`ldfcr_InitStartup()`后检测并保存库信息
- 开头和结尾使用相同的保存结果

### 4. 组件顺序错乱
**问题**: 组件显示顺序不符合要求
**解决**: 使用`GetVersionInfoByOrder()`方法按预定义顺序排列

## 代码优化要点

### 1. 消除重复代码
- 统一`extractLibNameFromPath`函数处理Linux和Mac
- 使用`addLibraryIfNotExists`避免重复的去重逻辑
- 删除重复的函数定义

### 2. 改进错误处理
- 添加命令执行失败的错误提示
- 处理文件路径不存在的情况
- 优雅处理内存分配失败

### 3. 提升可维护性
- 使用条件编译分离平台特定代码
- 统一函数命名和代码风格
- 添加必要的注释说明

## 测试验证清单

### Linux系统测试
- [ ] 编译成功
- [ ] 开头显示库信息
- [ ] 结尾显示相同库信息
- [ ] 组件按指定顺序排列
- [ ] 显示完整绝对路径

### Mac系统测试
- [ ] 编译成功
- [ ] 开头显示库信息
- [ ] 结尾显示相同库信息
- [ ] 组件按指定顺序排列
- [ ] 显示完整绝对路径（无省略号）

### Windows系统测试
- [ ] 编译成功
- [ ] 保持原有PE信息显示功能
- [ ] 不影响原有逻辑

## 提交信息建议

```bash
feat: cross-platform library detection with ordered display

- Add Mac system support using lsof command
- Implement ordered library display (startup/end consistency)
- Fix path display issues (complete absolute paths)
- Optimize code structure (remove duplicates, fix function order)
```

## 后续扩展建议

1. **配置化排序**: 将组件顺序配置到外部文件
2. **性能优化**: 缓存库检测结果，避免重复扫描
3. **日志记录**: 添加详细的调试日志
4. **错误恢复**: 当检测失败时的备用方案
