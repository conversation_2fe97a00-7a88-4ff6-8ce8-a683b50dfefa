#include "gtest/stdafx.h"
#include "gtest/gtest.h"
#include "test_class.h"

#ifdef WIN32
#include "PEinfo.h"
#elif __GNUC__
#include <unistd.h>
#include "libaryVersionInfo.h"
#endif

#include "trcrt.h"
#include "DlpULog.h"

int g_log_handle = -1;
using namespace std;

// 全局变量保存基线库信息，用于程序头部和尾部的组件信息对比
// Windows下使用PEinfo类，Linux/Mac下使用libaryVersionInfo类
#if defined(__GNUC__) || defined(__APPLE__)
static libaryVersionInfo* g_baselineLibVersion = nullptr;

class SimpleEnvironment : public ::testing::Environment {
public:
	~SimpleEnvironment() override {}
	void SetUp() override {}
	void TearDown() override {
		ldfcr_StopRelease();
	}
};
#endif

#ifdef WIN32
// Windows下使用PEinfo进行组件版本信息的获取和格式化输出
static PEinfo* g_baselinePEinfo = nullptr;
#endif

//char* get_text_from_file(const char *file, long *nsize)
//{
//    char *content = NULL;
//    FILE *fp = fopen(file, "r+");
//
//    if (fp) 
//	{
//        fseek(fp, 0, SEEK_END);
//        *nsize = ftell(fp);
//        fseek(fp, 0, SEEK_SET);
//        content = (char *)malloc(*nsize+1);
//        if (!content) {
//            return NULL;
//        }
//        char buffer[65536];
//        size_t nread = 0, length = 0;
//        while ((nread = fread(buffer, 1, sizeof buffer, fp)) != 0) 
//		{
//            memcpy(content+length, buffer, nread);
//            length += nread;
//        }
//        fclose(fp);
//    }
//    return content;
//}
//
//#if 0
//static int writer(char *data, size_t size, size_t nmemb, string *writerData)
//{
//    if(writerData == NULL)
//        return 0;
//    writerData->append(data, size*nmemb);
//    return size * nmemb;
//}
//#endif
//
//#if 0
//int request(const char *req, string &content)
//{
//    CURL *curl;
//    CURLcode res;
//
//    curl = curl_easy_init();
//    if (curl) {
//        curl_easy_setopt(curl, CURLOPT_URL, req);
//        CURLcode code = curl_easy_setopt(curl, CURLOPT_WRITEFUNCTION, writer);
//        if (code != CURLE_OK) {
//            return -1;
//        }
//        code = curl_easy_setopt(curl, CURLOPT_WRITEDATA, &content);
//        if (code != CURLE_OK) {
//            return -1;
//        }
//        res = curl_easy_perform(curl);
//        if (res != CURLE_OK) {
//            curl_easy_cleanup(curl);
//            return -1;
//        }
//        curl_easy_cleanup(curl);
//        return 0;
//    }
//    return -1;
//}
//
//bool new_keywords_request(const char *source, int entries)
//{
//    string s;
//    const char *reqfmt = "http://localhost:8080/newkeywords?source=%s&entries=%d";
//    char reqbuf[4096] = {0};
//
//    std::string fullpath;
//    if (source[0] != '/') {
//        char cwd[4096] = {0};
//        getcwd(cwd, sizeof cwd - 1);
//        fullpath += "/";
//        fullpath += source;
//    } else {
//        fullpath = source;
//    }
//
//    snprintf(reqbuf, sizeof reqbuf, reqfmt, fullpath.c_str(), entries);
//    return request(reqbuf, s);
//}
//
//
//string new_rules_request(const char *ruletype, int rulesize)
//{
//    string strategy;
//    const char *reqfmt = "http://localhost:8080/newrules?ruletype=%s&rulesize=%d&content";
//    char reqbuf[4096] = {0};
//
//    snprintf(reqbuf, sizeof reqbuf, reqfmt, ruletype, rulesize);
//    int ret = request(reqbuf, strategy);
//    if (ret != 0) {
//        fprintf(stderr, "request strategy content error\n");
//        return string();
//    }
//    return strategy;
//}
//#endif
//
//vector<int> parse_sequence(const char *seq)
//{
//    const char *s = seq;
//    vector<int> ivec;
//    int i = 0;
//
//    while (*s) {
//        if (isdigit(*s)) {
//            i = i*10 + (*s-'0');
//        } else if (*s == ',') {
//            ivec.push_back(i);
//            i = 0;
//        } else {
//            return vector<int>();
//        }
//        ++s;
//    }
//    ivec.push_back(i);
//    return ivec;
//}
//
//int get_mean_devivation(double *times, int time_len, double &mean, double &devivation)
//{
//    double sum = 0, sdev = 0;
//
//    std::for_each(times, times+time_len, [&sum](double d)
//                                    {sum += d;}
//                );
//    mean = sum / time_len;
//    std::for_each(times, times+time_len, [&sdev, &mean](double d)
//                                            {sdev += (d-mean)*(d-mean);}
//                );
//    devivation = sqrt(sdev/(time_len-1));
//    return 0;
//}
//
////int multithread_test(const wchar_t *strategy_file, const wchar_t *test_file, int num_of_instance, int iterations);

#ifdef WIN32
int _tmain(int argc, _TCHAR* argv[])
#else
int main(int argc, char **argv)
#endif
{
#ifdef __GNUC__
	char currentPath[1024];
	if (getcwd(currentPath, sizeof(currentPath)) != NULL) {
		std::string currentDir = currentPath;
		size_t lastSlash = currentDir.find_last_of('/');
		if (lastSlash != std::string::npos) {
			std::string dirName = currentDir.substr(lastSlash + 1);
			if (dirName == "bin") {
				// 只有在bin目录时才切换目录和设置日志目录
				if (chdir("../") == 0) {
					// 设置日志目录防止在bin目录创建log文件夹
					char logDir[1024];
					if (getcwd(logDir, sizeof(logDir)) != NULL) {
						dlplog_cd(logDir);
					}
				}
			}
		}
	}
#endif

	dlplog_init("ldfcr.log");

#if 1
#if defined(__GNUC__) || defined(__APPLE__)
	::testing::AddGlobalTestEnvironment(new SimpleEnvironment);
	GetTestinfo();

	// === 初始化ldfcr并检测库信息 ===
	if (ldfcr_InitStartup()) {
		// 等待一小段时间确保所有动态库都加载完毕
		usleep(100000); // 100ms

						// === 显示程序启动时的完整库信息（开头） ===
		printf("\n=== Baseline Libraries (Program Startup) ===\n");
		g_baselineLibVersion = new libaryVersionInfo();
		g_baselineLibVersion->GetVersionInfoByOrder();
		g_baselineLibVersion->PrintVersionInfo();
	}
#elif WIN32
	::testing::AddGlobalTestEnvironment(new Environment);
	GetTestinfo();

	// === 初始化ldfcr并检测库信息 ===
	if (ldfcr_InitStartup()) {
		// 等待一小段时间确保所有动态库都加载完毕
		Sleep(100); // 100ms

		// === 显示程序启动时的完整库信息（开头） ===
		printf("\n=== Baseline Libraries (Program Startup) ===\n");
		g_baselinePEinfo = new PEinfo();
		g_baselinePEinfo->GetVersionInfoByOrder();  // 收集组件信息
		g_baselinePEinfo->PrintVersionInfo();       // 输出组件信息
	}
#endif

	g_log_handle = dlplog_open_v2("ldfcr", "ldfcr_gtest");
	testing::InitGoogleTest(&argc, argv);

	RUN_ALL_TESTS();

	GetTestinfo();
#ifdef WIN32
	// === 显示程序结束时的基础库信息（结尾，基于实际加载的模块） ===
	printf("\n=== Baseline Libraries (Program End) ===\n");
	if (g_baselinePEinfo != nullptr) {
		g_baselinePEinfo->PrintVersionInfo();  // 输出之前收集的信息

		// === 可选：显示所有实际加载的模块（用于调试和分析） ===
		// 取消下面的注释可以看到程序实际加载的所有模块
		/*
		printf("\n=== All Actually Loaded Modules (Debug Info) ===\n");
		g_baselinePEinfo->GetRealLoadedModules();
		g_baselinePEinfo->PrintRealLoadedModules();
		*/

		delete g_baselinePEinfo;
		g_baselinePEinfo = nullptr;
	}
#elif defined(__GNUC__) || defined(__APPLE__)
	// === 显示程序结束时的基础库信息（结尾，应该和开头一致） ===
	printf("\n=== Baseline Libraries (Program End) ===\n");
	if (g_baselineLibVersion != nullptr) {
		g_baselineLibVersion->PrintVersionInfo();
		delete g_baselineLibVersion;
		g_baselineLibVersion = nullptr;
	}
#endif
	return 0;

#else

	int threads = 4;
	int iterations = 10000;
	if (argc > 1)
		threads = _wtoi(argv[1]);
	if (argc > 2)
		iterations = _wtoi(argv[2]);
	ldfcr_InitStartup();
	multithread_test(L"test/data/strategy.data", L"test/text1.txt", threads, iterations);
	//multithread_test(L"test/data/strategy.data", L"test/text1.txt", 1, iterations);
	//multithread_test("test/data/strategy.data", "test/text1.txt", 1, 10000);

	ldfcr_StopRelease();
#endif
}