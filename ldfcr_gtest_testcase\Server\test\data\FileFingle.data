{"respondRule": [{"sendMail": false, "enableCancel": 0, "filters": [{"type": "deviceType", "relation": "either", "object": ["terminal-agent"]}, {"type": "lossType", "relation": "either", "object": ["8", "9", "26", "25", "2", "3", "4", "14", "15", "16", "11", "1", "18", "5", "17", "7", "23", "24", "13", "0", "20", "27", "29"]}], "alarmInfo": {"alarmLimit": 3, "msgFormType": 0, "deleted": 0, "configMethod": 1, "name": "违规响应", "msgFormPosition": 0, "id": 1, "msgFormClose": 30}, "outSendApply": 0, "stopOutgoing": 0, "takeScreenshot": false, "stopOutgoingEx": 0, "createTime": 1688538427000, "name": "敏感响应触发", "id": 3, "warnContent": ""}], "checkRule": [{"exceptWord": "", "createTime": 1688710471000, "ruleType": "6", "name": "文件指纹规则测试", "id": 4, "type": 6, "fingerPrint": "12547160720239080376"}], "strategy": [{"severity": "1", "respondRule": "3", "createTime": 1688710844974, "checkRule": "4", "name": "文件指纹自动化测试规则", "dripScan": false, "id": 3, "classification": [{"checkExpr": "4", "name": "文件指纹规则测试", "exceptRule": "", "id": 3}]}], "businessType": {"opTypes": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 20], "type": 99}}