cmake_minimum_required (VERSION 2.8)
project(ldfcr_gtest)

add_definitions("-DUSING_INSTANCE")
add_definitions(-std=c++11)

add_definitions(-DUSE_UTF8_ENGINE)

#set(CMAKE_RUNTIME_OUTPUT_DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR})

# set(CMAKE_RUNTIME_OUTPUT_DIRECTORY ${CMAKE_SOURCE_DIR})
# message("out exe: " ${CMAKE_RUNTIME_OUTPUT_DIRECTORY})


    # 尝试检测 CentOS
    execute_process(
        COMMAND cat /etc/os-release
        OUTPUT_VARIABLE OS_RELEASE
        ERROR_QUIET
    )

add_executable(ldfcr_gtest
    main.cpp
    test_class.h
    PubFunc.h
    test_cases.cpp
    )
    
add_executable(ldfcr_gtest_advance
    main.cpp  
    test_class.h
    PubFunc.h
    test_cases_advance.cpp
	)

IF(${CMAKE_SYSTEM_PROCESSOR} MATCHES "aarch64")
	message(STATUS "Linux aarch64")
    find_library(DlpULog_LIBRARY DlpULog HINTS ${CMAKE_CURRENT_SOURCE_DIR}/../../../__devLibs_ldfcr/aarch64/libs_x64)
    target_link_libraries(ldfcr_gtest PUBLIC ${DlpULog_LIBRARY})
    target_link_libraries(ldfcr_gtest_advance PUBLIC ${DlpULog_LIBRARY})

    find_library(ldfcr_LIBRARY ldfcr HINTS ${CMAKE_CURRENT_SOURCE_DIR}/../../../__devLibs_ldfcr/aarch64/libs_x64)
    target_link_libraries(ldfcr_gtest PUBLIC ${ldfcr_LIBRARY})
    target_link_libraries(ldfcr_gtest_advance PUBLIC ${ldfcr_LIBRARY})

    find_library(trcrt_LIBRARY trcrt HINTS ${CMAKE_CURRENT_SOURCE_DIR}/../../../__devLibs_ldfcr/aarch64/libs_x64)
    target_link_libraries(ldfcr_gtest PUBLIC ${trcrt_LIBRARY})
    target_link_libraries(ldfcr_gtest_advance PUBLIC ${trcrt_LIBRARY})
	
	find_library(z_LIBRARY z HINTS ${CMAKE_CURRENT_SOURCE_DIR}/../../../__devLibs_ldfcr/aarch64/lib3rds_x64)
    target_link_libraries(ldfcr_gtest PUBLIC ${z_LIBRARY})
    target_link_libraries(ldfcr_gtest_advance PUBLIC ${z_LIBRARY})

	find_library(gtest_LIBRARY gtest_arm HINTS ${CMAKE_CURRENT_SOURCE_DIR}/../../../__devLibs_ldfcr/aarch64/libs_x64)
    target_link_libraries(ldfcr_gtest PUBLIC ${gtest_LIBRARY})
    target_link_libraries(ldfcr_gtest_advance PUBLIC ${gtest_LIBRARY})
	
	SET(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -std=c++11 -pthread")
ELSE()
    if (APPLE)
        find_library(DlpULog_LIBRARY DlpULog HINTS ${CMAKE_CURRENT_SOURCE_DIR}/../../../__devLibs_ldfcr/libs_x64)
        target_link_libraries(ldfcr_gtest PUBLIC ${DlpULog_LIBRARY})
        target_link_libraries(ldfcr_gtest_advance PUBLIC ${DlpULog_LIBRARY})

        find_library(ldfcr_LIBRARY ldfcr HINTS ${CMAKE_CURRENT_SOURCE_DIR}/../../../__devLibs_ldfcr/libs_x64)
        target_link_libraries(ldfcr_gtest PUBLIC ${ldfcr_LIBRARY})
        target_link_libraries(ldfcr_gtest_advance PUBLIC ${ldfcr_LIBRARY})

        find_library(trcrt_LIBRARY trcrt HINTS ${CMAKE_CURRENT_SOURCE_DIR}/../../../__devLibs_ldfcr/libs_x64)
        target_link_libraries(ldfcr_gtest PUBLIC ${trcrt_LIBRARY})
        target_link_libraries(ldfcr_gtest_advance PUBLIC ${trcrt_LIBRARY})
		
		find_library(gtest_LIBRARY gtest_mac HINTS ${CMAKE_CURRENT_SOURCE_DIR}/../../../__devLibs_ldfcr/libs_x64)
        target_link_libraries(ldfcr_gtest PUBLIC ${gtest_LIBRARY})
        target_link_libraries(ldfcr_gtest_advance PUBLIC ${gtest_LIBRARY})
    elseif(UNIX)
        if(CMAKE_SIZEOF_VOID_P EQUAL 8)
            message(STATUS "Linux 64bit")
            find_library(DlpULog_LIBRARY DlpULog HINTS ${CMAKE_CURRENT_SOURCE_DIR}/../../../__devLibs_ldfcr/libs_x64)
            target_link_libraries(ldfcr_gtest PUBLIC ${DlpULog_LIBRARY})
            target_link_libraries(ldfcr_gtest_advance PUBLIC ${DlpULog_LIBRARY})

            find_library(ldfcr_LIBRARY ldfcr HINTS ${CMAKE_CURRENT_SOURCE_DIR}/../../../__devLibs_ldfcr/libs_x64)
            target_link_libraries(ldfcr_gtest PUBLIC ${ldfcr_LIBRARY})
            target_link_libraries(ldfcr_gtest_advance PUBLIC ${ldfcr_LIBRARY})

            find_library(trcrt_LIBRARY trcrt HINTS ${CMAKE_CURRENT_SOURCE_DIR}/../../../__devLibs_ldfcr/libs_x64)
            target_link_libraries(ldfcr_gtest PUBLIC ${trcrt_LIBRARY})
            target_link_libraries(ldfcr_gtest_advance PUBLIC ${trcrt_LIBRARY})
			
			#find_library(gtest_LIBRARY gtest_ubuntu2204 HINTS ${CMAKE_CURRENT_SOURCE_DIR}/../../../__devLibs_ldfcr/libs_x64) #ubuntu需要使用此静态库
			find_library(gtest_LIBRARY gtest_linux HINTS ${CMAKE_CURRENT_SOURCE_DIR}/../../../__devLibs_ldfcr/libs_x64)
            target_link_libraries(ldfcr_gtest PUBLIC ${gtest_LIBRARY})
            target_link_libraries(ldfcr_gtest_advance PUBLIC ${gtest_LIBRARY})

			SET(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -std=c++11 -pthread")
            target_link_libraries(ldfcr_gtest PUBLIC rt)
            target_link_libraries(ldfcr_gtest_advance PUBLIC rt)
        else(CMAKE_SIZEOF_VOID_P EQUAL 8)

            message(STATUS "Linux 32bit")
            find_library(DlpULog_LIBRARY DlpULog HINTS ${CMAKE_CURRENT_SOURCE_DIR}/../../../__devLibs_ldfcr/libs_x86)
            target_link_libraries(ldfcr_gtest PUBLIC ${DlpULog_LIBRARY})
            target_link_libraries(ldfcr_gtest_advance PUBLIC ${DlpULog_LIBRARY})

            find_library(ldfcr_LIBRARY ldfcr HINTS ${CMAKE_CURRENT_SOURCE_DIR}/../../../__devLibs_ldfcr/libs_x86)
            target_link_libraries(ldfcr_gtest PUBLIC ${ldfcr_LIBRARY})
            target_link_libraries(ldfcr_gtest_advance PUBLIC ${ldfcr_LIBRARY})

            find_library(trcrt_LIBRARY trcrt HINTS ${CMAKE_CURRENT_SOURCE_DIR}/../../../__devLibs_ldfcr/libs_x86)
            target_link_libraries(ldfcr_gtest PUBLIC ${trcrt_LIBRARY})
            target_link_libraries(ldfcr_gtest_advance PUBLIC ${trcrt_LIBRARY})
			
		    #if (OS_RELEASE MATCHES "ID=centos")
			if (OS_RELEASE MATCHES "")
			message(STATUS "Detected OS is CentOS")
			find_library(gtest_LIBRARY gtest_linux HINTS ${CMAKE_CURRENT_SOURCE_DIR}/../../../__devLibs_ldfcr/libs_x86)		
		    endif()

		    if (OS_RELEASE MATCHES "ID=ubuntu")
			message(STATUS "Detected OS is Ubuntu")
			find_library(gtest_LIBRARY gtest_ubuntu1704 HINTS ${CMAKE_CURRENT_SOURCE_DIR}/../../../__devLibs_ldfcr/libs_x86) #ubuntu需要使用此静态库
		    endif()
		
            target_link_libraries(ldfcr_gtest PUBLIC ${gtest_LIBRARY})
            target_link_libraries(ldfcr_gtest_advance PUBLIC ${gtest_LIBRARY})

			SET(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -std=c++11 -pthread")
            target_link_libraries(ldfcr_gtest PUBLIC rt)
            target_link_libraries(ldfcr_gtest_advance PUBLIC rt)
        endif(CMAKE_SIZEOF_VOID_P EQUAL 8)
    endif()
ENDIF()

# link_directories(${PROJECT_SOURCE_DIR}/../libs_4linux_x64)

target_include_directories(${PROJECT_NAME} PRIVATE
    ${CMAKE_CURRENT_SOURCE_DIR}/../../../04_publicComponents/googletest/googletest/include
    ${CMAKE_CURRENT_SOURCE_DIR}/../../../__devLibs_ldfcr/inc
)

target_include_directories(ldfcr_gtest_advance PRIVATE
    ${CMAKE_CURRENT_SOURCE_DIR}/../../../04_publicComponents/googletest/googletest/include
    ${CMAKE_CURRENT_SOURCE_DIR}/../../../__devLibs_ldfcr/inc
)
# SET(CMAKE_BUILD_WITH_INSTALL_RPATH TRUE) 
# SET(CMAKE_INSTALL_RPATH "\${ORIGIN}/../lib")

# target_link_libraries(ldfcr_gtest PUBLIC pthread)

target_link_libraries(ldfcr_gtest PUBLIC m)
target_link_libraries(ldfcr_gtest_advance PUBLIC m)

set_target_properties(${PROJECT_NAME} PROPERTIES LINK_FLAGS "-Wl,-rpath,./,-rpath,./libs_x64/,-rpath,./lib/dlpcomm,-rpath,../lib/dlpcomm")
set_target_properties(ldfcr_gtest_advance PROPERTIES LINK_FLAGS "-Wl,-rpath,./libs_x64,-rpath,./,-rpath,./lib/dlpcomm,-rpath,../lib/dlpcomm")


