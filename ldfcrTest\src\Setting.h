#pragma once
#include <stdio.h>
#include <string>

// ini参数传递
typedef struct iniElement
{
	bool block_pwderror;
	int optype;
	int outTime;
	int circulate;
	int thread_count;
	int nearbyLen;
	int TagRespond;
	int scanAll;
	std::string modeSelect;
	std::string FuncSelect;
	std::string CheckType;
	std::string bocrSelect;
	std::string bocr_embedSelect;
	std::string filextSelect;
	std::string advInitParam;

}INISetting;