#include "mainwindow.h"
#include "ui_mainwindow.h"
#include "../trcrt/trcrt.h"
#include <QMessageBox>

MainWindow::MainWindow(QWidget *parent) :
    QMainWindow(parent),
    ui(new Ui::MainWindow)
{
    m_pILDFcr = nullptr;
    m_bDoingDirFcr = FALSE;
    ui->setupUi(this);

    connect(this,SIGNAL(done_detect_file(double,bool)),this,SLOT(recv_sign_done_detect_file(double,bool)));
    connect(this,SIGNAL(done_detect_dir(double)),this,SLOT(recv_sign_done_detect_dir(double)));
    connect(this,SIGNAL(textedit_detecting_append(QString)),ui->textEdit_detecting,SLOT(append(QString)));
    connect(this,SIGNAL(lineedit_totalcount_set(QString)),ui->lineEdit_dir_totallCount,SLOT(setText(QString)));
    connect(this,SIGNAL(lineedit_senscount_set(QString)),ui->lineEdit_dir_sensFilesCounts,SLOT(setText(QString)));


    Init();
}

MainWindow::~MainWindow()
{
    m_bDoingDirFcr = FALSE;
    if( nullptr != m_pILDFcr )
    {
        m_pILDFcr->Release();
        m_pILDFcr = nullptr;
    }
    ldfcr_StopRelease();
    delete ui;
}

bool MainWindow::Init()
{
    this->m_iFcrCount = 1;
    
    /////////////////////////////////////////////////////////////////
    //初始化内容识别组件
    
    trcrt::str::SafeString  strategy_sstr;
    trcrt::str::tr_str_initString(&strategy_sstr);
    
    /////////////////////////////////////////////////////////////////
    //获取策略文件的路径
    char csAppDir[MAX_PATH];
    trcrt::path::tr_path_get_app_path(csAppDir,MAX_PATH);
    int  iLen = ::strlen(csAppDir);
//    while( 0 < iLen )
//    {
//        if( 0 != csAppDir[iLen-1]){
//            csAppDir[iLen-1] = 0;
//            break;
//        }
    
//        iLen--;
//    }
    
    this->m_strStrategyFilePath = csAppDir;    //构造策略文件的路径
    this->m_strStrategyFilePath += "/Strategy.data";
    
    this->m_strFilePath4FCR = csAppDir;
    this->m_strFilePath4FCR += "/SampleFcr.txt";
    
    //读取策略信息（从文件中读出一个字符串）
    //注意：原始文件中的策略信息utf8编码的
    do{
        struct stat st;
        int ret = stat(m_strStrategyFilePath.toLatin1().data(), &st);
        if( (0 != ret) || (S_IFREG != (S_IFREG & st.st_mode)))
        {
            QMessageBox::warning(this,"加载失败","策略文件不存在，无法加载策略信息！");
            break;
        }
    
       //加载策略文件
        int iStrategyLen = st.st_size;
        char* strategy_utf8str = new char[iStrategyLen+1];
        FILE* fp = fopen(m_strStrategyFilePath.toLatin1().data(), "rb");
        if( nullptr == fp )
        {
            QMessageBox::warning(this,"加载失败","打开策略文件失败，无法加载策略信息！");
            break;
        }
            
        int iReadLen = fread(strategy_utf8str, sizeof(char), iStrategyLen, fp);
        if( iReadLen != iStrategyLen ){
            break;
        }
    
        strategy_utf8str[iStrategyLen] = 0; //设置字符串结尾符
    
        //创建

       strategy_sstr->set_fromUTF8(
           trcrt::str::IString::UTF8,
           strategy_utf8str, iStrategyLen
           );

    
        fclose(fp);
        fp = nullptr;
            
    
    }while(false);
        
    /////////////////////////////////////////////////////////////////
    //设置策略
    if( nullptr != strategy_sstr.get() )
    {
        this->m_strStrategy = strategy_sstr->getUtf8String(nullptr);
    }
    
    if( ldfcr_InitStartup() )
    {
        ldfcr_CreateInstance(&m_pILDFcr);
    }else
    {
        QMessageBox::warning(this,"加载失败","文件内容识别组件初始化失败！");
        return FALSE;
    }
    
    if( nullptr == m_pILDFcr )
    {
        QMessageBox::warning(this,"加载失败","创建识别接口失败！");
        return FALSE;
    }
    
    //设置策略
    BOOL bRet 
        = m_pILDFcr->updateStrategy(this->m_strStrategy.toLatin1().data());
    if( !bRet )
    {
        QMessageBox::warning(this,"加载失败","策略更新失败！");
        return FALSE;
    }
    
    ui->textEdit_StrategyFilPath->setText(m_strStrategyFilePath);
    ui->textEdit_DetectedFilePath->setText(m_strFilePath4FCR);
    ui->lineEdit_file_count->setText("1");

//    this->UpdateData(FALSE);   mfc的刷新函数
    return TRUE;  // 除非将焦点设置到控件，否则返回 TRUE
}

BOOL MainWindow::dlp_checkSingleFile(const char* file_path)
{
    if( nullptr == m_pILDFcr ) return false;

    FCRPARAM fcrParam;

    fcrParam.use_all_rule = TRUE;   //启用所有规则
    fcrParam.target_class_code = 3; //达到或超过代码3，即停止
    fcrParam.output_detail = TRUE;  //要求返回时输出策略的细节信息

    IFCRResult*  result_p = nullptr;
    BOOL bIsSensitive = m_pILDFcr->executeFCR(
        file_path,
        &fcrParam,
        (void**)&result_p
        );
    if( nullptr != result_p )
    {
        result_p->moveFirstPart();

        result_p->Release();
        result_p = nullptr;
    }

    return bIsSensitive;
}

