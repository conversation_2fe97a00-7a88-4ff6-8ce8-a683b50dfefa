// Setting_Dialog.cpp : 实现文件
//

#include "stdafx.h"
#include "ldfcrTester.h"
#include "Setting_Dialog.h"
#include "afxdialogex.h"
#include "publicFunction.h"
#include "codeT.h"
#include "../INC/DlpULog.h"
#include "IniSetting.h"

extern LOG_HANDLE g_log_handle;

// Setting_Dialog 对话框

IMPLEMENT_DYNAMIC(Setting_Dialog, CDialog)

Setting_Dialog::Setting_Dialog(CWnd* pParent /*=NULL*/)
	: CDialog(IDD_SETTING, pParent)
	, m_outTimeJug(0)
	, m_password(_T(""))
	, m_iCount(0)
	, m_csNormalKey(_T(""))
	, m_intOpType(0)
	, m_cs_json_type(_T(""))
	, m_senContext_count(0)
{

}

Setting_Dialog::~Setting_Dialog()
{
}

void Setting_Dialog::DoDataExchange(CDataExchange* pDX)
{
	CDialog::DoDataExchange(pDX);
	DDX_Control(pDX, IDC_EDIT2, eidttim);
	DDX_Text(pDX, IDC_EDIT2, m_outTimeJug);
	DDX_Control(pDX, IDC_EDIT5, m_edit_password);
	DDX_Text(pDX, IDC_EDIT5, m_password);
	DDX_Text(pDX, IDC_EDIT7, m_iCount);
	DDX_Control(pDX, IDC_EDIT7, m_editCount);
	DDX_Text(pDX, IDC_EDIT1, m_csNormalKey);
	DDX_Control(pDX, IDC_EDIT1, m_editKey);
	DDX_Control(pDX, IDC_OUTPUT, m_edit);
	DDX_Control(pDX, IDC_COMBO_OPTYPE, m_OpType);
	DDX_Text(pDX, IDC_COMBO_OPTYPE, m_intOpType);
	DDX_Control(pDX, IDC_EDIT6, m_json_type);
	DDX_Text(pDX, IDC_EDIT6, m_cs_json_type);
	DDX_Control(pDX, IDC_EDIT8, m_edit_senContext_count);
	DDX_Text(pDX, IDC_EDIT8, m_senContext_count);
}


BEGIN_MESSAGE_MAP(Setting_Dialog, CDialog)
	ON_BN_CLICKED(IDC_FILEFP, &Setting_Dialog::OnBnClickedFilefp)
	ON_BN_CLICKED(IDC_SVM, &Setting_Dialog::OnBnClickedSvm)
	ON_BN_CLICKED(IDC_FILEDB, &Setting_Dialog::OnBnClickedFiledb)
	ON_BN_CLICKED(IDC_OCR_EMBED, &Setting_Dialog::OnBnClickedOcrEmbed)
	ON_BN_CLICKED(IDC_OCR, &Setting_Dialog::OnBnClickedOcr)
	ON_BN_CLICKED(IDC_CHECK_PWD, &Setting_Dialog::OnBnClickedCheckPwd)
	ON_BN_CLICKED(IDC_UPDATE, &Setting_Dialog::OnBnClickedUpdate)
	ON_BN_CLICKED(IDC_BTN_MIYAO, &Setting_Dialog::OnBnClickedBtnMiyao)
	ON_BN_CLICKED(IDC_BUTTON1, &Setting_Dialog::OnBnClickedButton1)
	ON_BN_CLICKED(IDC_OCR_TYPE, &Setting_Dialog::OnBnClickedOcrType)
	ON_BN_CLICKED(IDC_TAG_CHECK, &Setting_Dialog::OnBnClickedTagCheck)
	ON_BN_CLICKED(IDC_CHECK_ALL, &Setting_Dialog::OnBnClickedCheckAll)
	ON_BN_CLICKED(IDC_OCR_Y, &Setting_Dialog::OnBnClickedOcrY)
	ON_BN_CLICKED(IDC_OCR_Q, &Setting_Dialog::OnBnClickedOcrQ)
	ON_BN_CLICKED(IDC_TIME_OUT, &Setting_Dialog::OnBnClickedTimeOut)
END_MESSAGE_MAP()


BOOL Setting_Dialog::OnInitDialog()
{
	CDialog::OnInitDialog();

	// TODO:  在此添加额外的初始化


	// 获取
	int ini_filefp = IniSetting::getInstance().GetFileFp();
	int ini_svm = IniSetting::getInstance().GetSVM();
	int ini_filedb = IniSetting::getInstance().GetFileDB();
	int ini_ocr_type = IniSetting::getInstance().GetOCRType();
	int ini_ocr_embed = IniSetting::getInstance().GetOCREmbed();
	int ini_ocr = IniSetting::getInstance().GetOCR();
	string strenFM_OCR = IniSetting::getInstance().GetOCRenFM();
	if (strcmp(strenFM_OCR.c_str(), "0x00") != 0)
	{
		((CButton*)GetDlgItem(IDC_OCR_Y))->SetCheck(1);
	}
	string strenFM_OCR_IN_CHILD = IniSetting::getInstance().GetOCRenFMInChild();
	if (strcmp(strenFM_OCR_IN_CHILD.c_str(), "0x00") != 0)
	{
		((CButton*)GetDlgItem(IDC_OCR_Q))->SetCheck(1);
	}
	int ini_pwd_error = IniSetting::getInstance().GetPwdStop();

	int ini_tag_check = IniSetting::getInstance().GetCheckTag();

	int ini_check_all = IniSetting::getInstance().GetCheckAll();

	int ini_time_out_b = IniSetting::getInstance().GetOutTime();

	m_intOpType = IniSetting::getInstance().GetOptype();
	m_outTimeJug = IniSetting::getInstance().GetOutTimes();
	m_iCount = IniSetting::getInstance().GetCirCulate();
	m_senContext_count = IniSetting::getInstance().GetContextLength();
	m_password = IniSetting::getInstance().GetPassWord();
	m_csNormalKey = IniSetting::getInstance().GetADVKey();;
	m_cs_json_type = IniSetting::getInstance().GetOCRJSONtype();


	//界面赋值
	((CButton *)GetDlgItem(IDC_FILEFP))->SetCheck(ini_filefp);
	((CButton *)GetDlgItem(IDC_SVM))->SetCheck(ini_svm);
	((CButton *)GetDlgItem(IDC_FILEDB))->SetCheck(ini_filedb);
	((CButton *)GetDlgItem(IDC_OCR_TYPE))->SetCheck(ini_ocr_type);
	((CButton *)GetDlgItem(IDC_OCR_EMBED))->SetCheck(ini_ocr_embed);
	((CButton *)GetDlgItem(IDC_OCR))->SetCheck(ini_ocr);
	((CButton *)GetDlgItem(IDC_CHECK_PWD))->SetCheck(ini_pwd_error);
	((CButton *)GetDlgItem(IDC_TAG_CHECK))->SetCheck(ini_tag_check);
	((CButton *)GetDlgItem(IDC_CHECK_ALL))->SetCheck(ini_check_all);
	((CButton*)GetDlgItem(IDC_TIME_OUT))->SetCheck(ini_time_out_b);

	m_OpType.ResetContent();
	//下拉框选择操作类型
	CString options[] = {
		_T("0-FTP传输"),
		_T("1-邮件内容传输"),
		_T("2-移动存储复制"),
		_T("3-移动存储剪切"),
		_T("4-移动存储另存为"),
		_T("5-文件打印"),
		_T("6-网页打印"),
		_T("7-网络共享"),
		_T("8-IM发送消息"),
		_T("9-IM发送文件"),
		_T("10-云传输"),
		_T("11-网络论坛发帖"),
		_T("12-telnet传输"),
		_T("13-光盘刻录"),
		_T("14-网页浏览"),
		_T("15-网页传输"),
		_T("16-网页粘贴"),
		_T("17-本地共享"),
		_T("18-邮件附件传输"),
		_T("19-全盘扫描"),
		_T("20-蓝牙文件传输"),
		_T("21-批量加解密"),
		_T("22-智能加密"),
		_T("23-网盘上传文件"),
		_T("24-网盘下载文件"),
		_T("25-IM下载文件"),
		_T("26-IM接收消息"),
		_T("27-adb管控"),
		_T("28-敏感自检策略"),
		_T("29-MTP管控"),
		_T("30-光盘刻录下载文件"),
		_T("31-网页下载文件"),
		_T("32-远程桌面外发文件"),
		_T("119-全盘扫描-策略变更通知"),
		_T("120-敏感自检扫描-策略变更通知"),
		_T("253-审批组件调用敏感检测接口时，检测类型值"),
		_T("254-敏感文件操作控制"),
		_T("255-智能加密扫描")
	};

	for (int i = 0; i < 38; ++i)
	{
		m_OpType.InsertString(i, options[i]);
	}

	m_OpType.SetCurSel(m_intOpType);

	

	this->UpdateData(FALSE);
	m_iIndex = 0;
	return TRUE;  // return TRUE unless you set the focus to a control
				  // 异常: OCX 属性页应返回 FALSE
}
// Setting_Dialog 消息处理程序

void Setting_Dialog::OnBnClickedFilefp()
{
	// TODO: 在此添加控件通知处理程序代码
	//FILEFP
	int state = ((CButton *)GetDlgItem(IDC_FILEFP))->GetCheck();
	checkPut(state, "FILEFP");
	string str;
	switch (IniSetting::getInstance().returnCheckPoint())
	{
	case pCheck:
		str = "m_Check_bfilefp";
		break;
	case pEncrypt:
		str = "m_Encrypt_bfilefp";
		break;
	case pEncryptTag:
		str = "m_EncryptTag_bfilefp";
		break;
	case Global:
		str = "m_Global_bfilefp";
		break;
	default:
		break;
	}
	IniSetting::getInstance().recordDigit2INI("LDFCR_MODULE", str.c_str(), state);
}



void Setting_Dialog::OnBnClickedSvm()
{
	// TODO: 在此添加控件通知处理程序代码
	//SVM
	int state = ((CButton*)GetDlgItem(IDC_SVM))->GetCheck();
	checkPut(state, "SVM");
	string str;
	switch (IniSetting::getInstance().returnCheckPoint())
	{
	case pCheck:
		str = "m_Check_bsvm";
		break;
	case pEncrypt:
		str = "m_Encrypt_bsvm";
		break;
	case pEncryptTag:
		str = "m_EncryptTag_bsvm";
		break;
	case Global:
		str = "m_Global_bsvm";
		break;
	default:
		break;
	}
	IniSetting::getInstance().recordDigit2INI("LDFCR_MODULE", str.c_str(), state);
}


void Setting_Dialog::OnBnClickedFiledb()
{
	// TODO: 在此添加控件通知处理程序代码
	//FILEDB
	int state = ((CButton*)GetDlgItem(IDC_FILEDB))->GetCheck();
	checkPut(state, "FILEDB");
	string str;
	switch (IniSetting::getInstance().returnCheckPoint())
	{
	case pCheck:
		str = "m_Check_bfiledb";
		break;
	case pEncrypt:
		str = "m_Encrypt_bfiledb";
		break;
	case pEncryptTag:
		str = "m_EncryptTag_bfiledb";
		break;
	case Global:
		str = "m_Global_bfiledb";
		break;
	default:
		break;
	}
	IniSetting::getInstance().recordDigit2INI("LDFCR_MODULE", str.c_str(), state);
}


void Setting_Dialog::OnBnClickedOcrEmbed()
{
	// TODO: 在此添加控件通知处理程序代码
	//OCR_EMBED
	int state = ((CButton*)GetDlgItem(IDC_OCR_EMBED))->GetCheck();
	checkPut(state, "OCR_EMBED");
	string str;
	switch (IniSetting::getInstance().returnCheckPoint())
	{
	case pCheck:
		str = "m_Check_bocr_embed";
		break;
	case pEncrypt:
		str = "m_Encrypt_bocr_embed";
		break;
	case pEncryptTag:
		str = "m_EncryptTag_bocr_embed";
		break;
	case Global:
		str = "m_Global_bocr_embed";
		break;
	default:
		break;
	}
	IniSetting::getInstance().recordDigit2INI("LDFCR_MODULE", str.c_str(), state);
}


void Setting_Dialog::OnBnClickedOcrType()
{
	// TODO: 在此添加控件通知处理程序代码
	//OCR_TYPE
	int state = ((CButton*)GetDlgItem(IDC_OCR_TYPE))->GetCheck();
	checkPut(state, "OCR_TYPE");
	string str;
	switch (IniSetting::getInstance().returnCheckPoint())
	{
	case pCheck:
		str = "m_Check_bocr_type";
		break;
	case pEncrypt:
		str = "m_Encrypt_bocr_type";
		break;
	case pEncryptTag:
		str = "m_EncryptTag_bocr_type";
		break;
	case Global:
		str = "m_Global_bocr_type";
		break;
	default:
		break;
	}
	IniSetting::getInstance().recordDigit2INI("LDFCR_MODULE", str.c_str(), state);
}


void Setting_Dialog::OnBnClickedOcr()
{
	// TODO: 在此添加控件通知处理程序代码
	//OCR
	int state = ((CButton*)GetDlgItem(IDC_OCR))->GetCheck();
	checkPut(state, "OCR");
	string str;
	switch (IniSetting::getInstance().returnCheckPoint())
	{
	case pCheck:
		str = "m_Check_bocr";
		break;
	case pEncrypt:
		str = "m_Encrypt_bocr";
		break;
	case pEncryptTag:
		str = "m_EncryptTag_bocr";
		break;
	case Global:
		str = "m_Global_bocr";
		break;
	default:
		break;
	}
	IniSetting::getInstance().recordDigit2INI("LDFCR_MODULE", str.c_str(), state);
}


void Setting_Dialog::OnBnClickedCheckPwd()
{
	// TODO: 在此添加控件通知处理程序代码
	//PWD
	int state = ((CButton*)GetDlgItem(IDC_CHECK_PWD))->GetCheck();
	checkPut(state, "密码错误阻断");
	string str;
	switch (IniSetting::getInstance().returnCheckPoint())
	{
	case pCheck:
		str = "Check_pwdError";
		break;
	case pEncrypt:
		str = "Encrypt_pwdError";
		break;
	case pEncryptTag:
		str = "EncryptTag_pwdError";
		break;
	case Global:
		str = "Global_pwdError";
		break;
	default:
		break;
	}
	IniSetting::getInstance().recordDigit2INI("DETECT_OPTYPE", str.c_str(), state);
}


void Setting_Dialog::OnBnClickedUpdate()
{
	// TODO: 在此添加控件通知处理程序代码
	// 更新超时阻断毫秒 带密码文件密码 操作类型 检测类型

	this->UpdateData(TRUE);

	iOpType = m_OpType.GetCurSel();
	m_OpType.GetLBText(iOpType, m_strOptype);

	std::string strPwd = wstringToString(m_password.GetString());

	std::string str = "更新配置: 操作类型: " + to_string(iOpType) + " 阻断时间: " + to_string(m_outTimeJug) + " 检测次数: " + to_string(m_iCount) + " 文件密码: " + strPwd + "敏感匹配上下文返回字数： " + to_string(m_senContext_count) + "\r\n";
	outPutFuc(str.c_str());

	switch (IniSetting::getInstance().returnCheckPoint())
	{
	case pCheck:
		IniSetting::getInstance().recordDigit2INI("DETECT_OPTYPE", "Check_optype", iOpType);//写入操作类型
		IniSetting::getInstance().recordDigit2INI("DETECT_OPTYPE", "Check_outTimeJug", m_outTimeJug);//写入阻断时间
		IniSetting::getInstance().recordDigit2INI("DETECT_OPTYPE", "Check_circulate", m_iCount);//写入检测次数
		IniSetting::getInstance().recordDigit2INI("DETECT_OPTYPE", "Check_contextCount", m_senContext_count);//写入敏感匹配上下文返回字数
		IniSetting::getInstance().recordStr2INI("DETECT_OPTYPE", "Check_password", strPwd.c_str());//写入文件密码
		break;
	case pEncrypt:
		IniSetting::getInstance().recordDigit2INI("DETECT_OPTYPE", "Encrypt_optype", iOpType);//写入操作类型
		IniSetting::getInstance().recordDigit2INI("DETECT_OPTYPE", "Encrypt_outTimeJug", m_outTimeJug);//写入阻断时间
		IniSetting::getInstance().recordDigit2INI("DETECT_OPTYPE", "Encrypt_circulate", m_iCount);//写入检测次数
		IniSetting::getInstance().recordDigit2INI("DETECT_OPTYPE", "Encrypt_contextCount", m_senContext_count);//写入敏感匹配上下文返回字数
		IniSetting::getInstance().recordStr2INI("DETECT_OPTYPE", "Encrypt_password", strPwd.c_str());//写入文件密码
		break;
	case pEncryptTag:
		IniSetting::getInstance().recordDigit2INI("DETECT_OPTYPE", "EncryptTag_optype", iOpType);//写入操作类型
		IniSetting::getInstance().recordDigit2INI("DETECT_OPTYPE", "EncryptTag_outTimeJug", m_outTimeJug);//写入阻断时间
		IniSetting::getInstance().recordDigit2INI("DETECT_OPTYPE", "EncryptTag_circulate", m_iCount);//写入检测次数
		IniSetting::getInstance().recordDigit2INI("DETECT_OPTYPE", "EncryptTag_contextCount", m_senContext_count);//写入敏感匹配上下文返回字数
		IniSetting::getInstance().recordStr2INI("DETECT_OPTYPE", "EncryptTag_password", strPwd.c_str());//写入文件密码
		break;
	case Global:
		IniSetting::getInstance().recordDigit2INI("DETECT_OPTYPE", "Global_optype", iOpType);//写入操作类型
		IniSetting::getInstance().recordDigit2INI("DETECT_OPTYPE", "Global_outTimeJug", m_outTimeJug);//写入阻断时间
		IniSetting::getInstance().recordDigit2INI("DETECT_OPTYPE", "Global_circulate", m_iCount);//写入检测次数
		IniSetting::getInstance().recordDigit2INI("DETECT_OPTYPE", "Global_contextCount", m_senContext_count);//写入敏感匹配上下文返回字数
		IniSetting::getInstance().recordStr2INI("DETECT_OPTYPE", "Global_password", strPwd.c_str());//写入文件密码
		break;
	default:
		break;
	}
}


// haimiejia 
void Setting_Dialog::OnBnClickedBtnMiyao()
{
	// TODO: 在此添加控件通知处理程序代码
	this->UpdateData(TRUE);
	//高级算法密钥
	std::string strNormalKey = wstringToString(m_csNormalKey.GetString());

	std::string strPut = "保存高级密钥: " + strNormalKey;
	outPutFuc(strPut.c_str());
	string str;
	switch (IniSetting::getInstance().returnCheckPoint())
	{
	case pCheck:
		str = "Check_advKey";
		break;
	case pEncrypt:
		str = "Encrypt_advKey";
		break;
	case pEncryptTag:
		str = "EncryptTag_advKey";
		break;
	case Global:
		str = "Global_advKey";
		break;
	default:
		break;
	}
	IniSetting::getInstance().recordCStr2INI("DETECT_OPTYPE", str.c_str(), m_csNormalKey);
}

bool Setting_Dialog::outPutFuc(const char * utf)
{
	if (0 != strlen(utf))
	{
		m_iIndex++;
		std::string strRes = Utf8ToGbk(utf);
		std::string stdIndex = std::to_string(m_iIndex);
		stdIndex = stdIndex + "." + strRes;
		CString cstrRes = CA2T(stdIndex.c_str());
		m_edit.SetSel(-1);
		m_edit.ReplaceSel(cstrRes);
		return true;
	}
	return false;
}

void Setting_Dialog::checkPut(int v_index, const char * c)
{
	std::string sName = c;
	if (v_index == 1)
	{
		std::string str = "勾选" + sName + "\r\n";
		outPutFuc(str.c_str());
	}
	else
	{
		std::string str = "取消勾选" + sName + "\r\n";
		outPutFuc(str.c_str());
	}
}



void Setting_Dialog::OnBnClickedButton1()
{
	// TODO: 在此添加控件通知处理程序代码
	this->UpdateData(TRUE);
	//OCR_TYPE_SELECT_JSON
	std::string strJsonType = wstringToString(m_cs_json_type.GetString());

	std::string strPut = "ocr识别参数: " + strJsonType;
	outPutFuc(strPut.c_str());
	string str;
	switch (IniSetting::getInstance().returnCheckPoint())
	{
	case pCheck:
		str = "m_Check_OCR_TYPE_JSON";
		break;
	case pEncrypt:
		str = "m_Encrypt_OCR_TYPE_JSON";
		break;
	case pEncryptTag:
		str = "m_EncryptTag_OCR_TYPE_JSON";
		break;
	case Global:
		str = "m_Global_OCR_TYPE_JSON";
		break;
	default:
		break;
	}
	IniSetting::getInstance().recordCStr2INI("FILTER_MODULE", str.c_str(), m_cs_json_type);
}




void Setting_Dialog::OnBnClickedTagCheck()
{
	// TODO: 在此添加控件通知处理程序代码
	//TAG
	int state = ((CButton*)GetDlgItem(IDC_TAG_CHECK))->GetCheck();
	checkPut(state, "ONLYRETURNTAG");
	string str;
	switch (IniSetting::getInstance().returnCheckPoint())
	{
	case pCheck:
		str = "Check_btag";
		break;
	case pEncrypt:
		str = "Encrypt_btag";
		break;
	case pEncryptTag:
		str = "EncryptTag_btag";
		break;
	case Global:
		str = "Global_btag";
		break;
	default:
		break;
	}
	IniSetting::getInstance().recordDigit2INI("DETECT_OPTYPE", str.c_str(), state);
}


void Setting_Dialog::OnBnClickedCheckAll()
{
	// TODO: 在此添加控件通知处理程序代码
	//CHECKALL
	int state = ((CButton*)GetDlgItem(IDC_CHECK_ALL))->GetCheck();
	checkPut(state, "CHECK ALL include children");
	string str;
	switch (IniSetting::getInstance().returnCheckPoint())
	{
	case pCheck:
		str = "Check_bcheckALL";
		break;
	case pEncrypt:
		str = "Encrypt_bcheckALL";
		break;
	case pEncryptTag:
		str = "EncryptTag_bcheckALL";
		break;
	case Global:
		str = "Global_bcheckALL";
		break;
	default:
		break;
	}
	IniSetting::getInstance().recordDigit2INI("DETECT_OPTYPE", str.c_str(), state);
}


void Setting_Dialog::OnBnClickedOcrY()
{
	// TODO: 在此添加控件通知处理程序代码
	//OCR_EN
	int state = ((CButton*)GetDlgItem(IDC_OCR_Y))->GetCheck();
	std::string strRes = "0x00";
	if (state == 1)
	{
		strRes = "0x80";
	}
	checkPut(state, "OCR模块，禁用原生图片文件");
	string str;
	switch (IniSetting::getInstance().returnCheckPoint())
	{
	case pCheck:
		str = "m_Check_enFM_OCR";
		break;
	case pEncrypt:
		str = "m_Encrypt_enFM_OCR";
		break;
	case pEncryptTag:
		str = "m_EncryptTag_enFM_OCR";
		break;
	case Global:
		str = "m_Global_enFM_OCR";
		break;
	default:
		break;
	}
	IniSetting::getInstance().recordStr2INI("FILTER_MODULE", str.c_str(), strRes.c_str());
}


void Setting_Dialog::OnBnClickedOcrQ()
{
	// TODO: 在此添加控件通知处理程序代码
	//OCR_IN_CHILD
	int state = ((CButton*)GetDlgItem(IDC_OCR_Q))->GetCheck();
	std::string strRes = "0x00";
	if (state == 1)
	{
		strRes = "0x100";
	}
	checkPut(state, "OCR模块，禁用嵌套图片文件");
	string str;
	switch (IniSetting::getInstance().returnCheckPoint())
	{
	case pCheck:
		str = "m_Check_enFM_OCR_IN_CHILD";
		break;
	case pEncrypt:
		str = "m_Encrypt_enFM_OCR_IN_CHILD";
		break;
	case pEncryptTag:
		str = "m_EncryptTag_enFM_OCR_IN_CHILD";
		break;
	case Global:
		str = "m_Global_enFM_OCR_IN_CHILD";
		break;
	default:
		break;
	}
	IniSetting::getInstance().recordStr2INI("FILTER_MODULE", str.c_str(), strRes.c_str());
}


void Setting_Dialog::OnBnClickedTimeOut()
{
	// TODO: 在此添加控件通知处理程序代码
	// TIMEOUT
	int state = ((CButton*)GetDlgItem(IDC_TIME_OUT))->GetCheck();
	checkPut(state, "超时阻断");
	string str;
	switch (IniSetting::getInstance().returnCheckPoint())
	{
	case pCheck:
		str = "Check_time_out";
		break;
	case pEncrypt:
		str = "Encrypt_time_out";
		break;
	case pEncryptTag:
		str = "EncryptTag_time_out";
		break;
	case Global:
		str = "Global_time_out";
		break;
	default:
		break;
	}
	IniSetting::getInstance().recordDigit2INI("DETECT_OPTYPE", str.c_str(), state);
}
