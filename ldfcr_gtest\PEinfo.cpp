#pragma once
#include "PEinfo.h"
#include "ldfcr.h"

PEinfo::PEinfo()
{
	m_pe = nullptr;
	PEinitLib(&m_pe);
	m_p.clear();
	m_dllVec.clear();
}


PEinfo::~PEinfo()
{
	PEfreeLib(m_pe);
}

/**
 * 获取预定义的组件顺序列表
 *
 * 此函数返回当前部署环境中实际存在的核心组件的优先级顺序。
 * 根据实际环境调整，只包含当前部署中确实需要检测的组件，
 * 避免输出大量不存在的组件提示信息。
 *
 * 包含的组件类型：
 * - 核心引擎组件: ldfcr, DlpPolicyEngine等
 * - 规则引擎组件: KWRuleEngine, RegexRuleEngine, SVMRuleEngine等
 * - 文档过滤器组件: TrCadFilter, TrPdfFilter, TrOLEFilter等（按需加载）
 * - OCR相关组件: DlpOCR, DlpSCR, TrOCRFilter等（按需加载）
 * - 基础库组件: trcrt, lua, memstream等
 *
 * 注意：某些组件（如Tr系列过滤器）是按需动态加载的，
 * 在程序启动时可能显示"no version number has been established"，
 * 这是正常现象，表示该组件尚未被加载到当前目录。
 *
 * @return vector<string> 包含26个组件名称的有序列表
 */
const vector<string> PEinfo::getOrderedLibs() {
	// 完整的组件列表，包含所有可能的组件（与libaryVersionInfo保持一致）
	static const vector<string> orderedLibs = {
		"ldfcr", "DlpPolicyEngine", "KWRuleEngine", "RegexRuleEngine",
		"FilePropEngine", "FileFpRuleEngine", "SVMRuleEngine", "FpDbRuleEngine",
		"TrCadFilter", "TrCompressFilter", "TrOLEFilter", "TrOOXMLFilter",
		"TrPdfFilter", "TrRtfFilter", "TrTextExtractor", "TrTxtFilter",
		"TrArchive", "TrODFFilter", "DlpOCR", "DlpSCR", "TrOCRFilter",
		"DlpULog", "trcrt", "lua", "pcre", "svm", "jieba", "xcrbnfa", "memstream"
	};
	return orderedLibs;
}



void PEinfo::GetPEinfo()
{
	vector<wstring> wstrfileVec;
	vector<string> strfileVec;
	/*GetLdfcrRunDll();*/

	wstring wstrCurPath = GetCurModuleFile();
	getCurFiles(wstrCurPath, wstrfileVec);
	for (size_t i = 0; i < wstrfileVec.size(); i++)
	{
		//if (!judgeFileIsWorkPath(wstrfileVec[i].c_str()))
		//{
		//	continue;
		//}
		InsertPEInfo(wstrfileVec[i].c_str(), strfileVec);
	}

	//pdf输出结果
	printf("\n");
	printfVec(strfileVec);
}

wstring PEinfo::GetCurModuleFile()
{
	wchar_t cstrCurPath[MAX_PATH];
	GetModuleFileName(NULL, cstrCurPath, MAX_PATH);
	wstring wstrCurPath = cstrCurPath;
	int cstrpos = wstrCurPath.find_last_of(L"\\");
	wstrCurPath = wstrCurPath.substr(0, cstrpos);
	return wstrCurPath;
}

void PEinfo::getCurFiles(wstring & v_wstrPath, vector<wstring>& v_vecFiles)
{
	// 文件句柄
	long long hFile = 0;
	// 文件信息
	struct _wfinddata_t fileinfo;

	if ((hFile = _wfindfirst(m_p.assign(v_wstrPath).append(L"\\*").c_str(), &fileinfo)) != -1) {
		do {
			if ((wcscmp(fileinfo.name, L".") == 0) || (wcscmp(fileinfo.name, L"..") == 0))  //过滤掉本代表本目录的.和上一级目录的.
			{
				continue;
			}
			// 保存文件的全路径
			if (JudgeFileLogic(fileinfo.name))
			{
				v_vecFiles.push_back(m_p.assign(v_wstrPath).append(L"\\").append(fileinfo.name));
			}

		} while (_wfindnext(hFile, &fileinfo) == 0);  //寻找下一个，成功返回0，否则-1

		_findclose(hFile);
	}
}

/**
 * 不使用配置文件过滤的文件遍历方法
 *
 * 与getCurFiles()的区别：不调用JudgeFileLogic()进行配置文件过滤，
 * 直接收集所有.dll和.exe文件，让后续的isTargetDLL()方法进行智能过滤。
 */
void PEinfo::getCurFilesWithoutFilter(wstring & v_wstrPath, vector<wstring>& v_vecFiles)
{
	// 文件句柄
	long long hFile = 0;
	// 文件信息
	struct _wfinddata_t fileinfo;

	if ((hFile = _wfindfirst(m_p.assign(v_wstrPath).append(L"\\*").c_str(), &fileinfo)) != -1) {
		do {
			if ((wcscmp(fileinfo.name, L".") == 0) || (wcscmp(fileinfo.name, L"..") == 0))  //过滤掉本代表本目录的.和上一级目录的.
			{
				continue;
			}

			// 检查是否为DLL或EXE文件（不使用配置文件过滤）
			wstring fileName = fileinfo.name;
			if (fileName.length() > 4) {
				wstring ext = fileName.substr(fileName.length() - 4);
				std::transform(ext.begin(), ext.end(), ext.begin(), ::towlower);
				if (ext == L".dll" || ext == L".exe") {
					v_vecFiles.push_back(m_p.assign(v_wstrPath).append(L"\\").append(fileinfo.name));
				}
			}

		} while (_wfindnext(hFile, &fileinfo) == 0);  //寻找下一个，成功返回0，否则-1

		_findclose(hFile);
	}
}

/**
 * 检查是否为目标DLL（基于文件名模式匹配，不依赖配置文件）
 *
 * 此方法根据预定义的组件列表，智能判断文件是否为我们关心的组件。
 * 相比JudgeFileLogic()的优势：
 * 1. 不依赖配置文件，避免配置遗漏
 * 2. 支持模糊匹配，能够识别版本号变化的文件
 * 3. 与Linux/Mac版本的逻辑保持一致
 */
bool PEinfo::isTargetDLL(const wstring& filePath)
{
	// 提取文件名（不含路径）
	size_t lastSlash = filePath.find_last_of(L'\\');
	wstring fileName = (lastSlash != wstring::npos) ? filePath.substr(lastSlash + 1) : filePath;

	// 转换为小写以便比较
	wstring lowerFileName = fileName;
	std::transform(lowerFileName.begin(), lowerFileName.end(), lowerFileName.begin(), ::towlower);

	// 移除扩展名
	size_t dotPos = lowerFileName.find_last_of(L'.');
	if (dotPos != wstring::npos) {
		lowerFileName = lowerFileName.substr(0, dotPos);
	}

	// 获取预定义的组件列表
	const vector<string>& orderedLibs = getOrderedLibs();

	// 检查是否匹配任何目标组件
	for (const auto& libName : orderedLibs) {
		wstring wLibName = StringToWstring(libName);
		std::transform(wLibName.begin(), wLibName.end(), wLibName.begin(), ::towlower);

		// 精确匹配或包含匹配
		if (lowerFileName == wLibName || lowerFileName.find(wLibName) != wstring::npos) {
			return true;
		}
	}

	return false;
}

bool PEinfo::JudgeFileLogic(const wchar_t * v_wstrFilePath)
{
	wstring wstrFileName = v_wstrFilePath;
	string strSrcIni = ".\\PETargetFile.ini";
	char LP[1024];
	for (int i = 0; i < 100; i++)
	{
		string name = "ProjectName";
		name.append(to_string(i));
		GetPrivateProfileStringA("project", name.c_str(), "NULL", LP, 512, strSrcIni.c_str());
		string strCm = WstringToString(wstrFileName);
		if (strcmp(LP, strCm.c_str()) == 0)
		{
			return true;
		}
	}
	return false;
}

void PEinfo::InsertPEInfo(const wchar_t * v_wstrFilePath, vector<string>& v_vecFiles)
{
	char chFileName[23] = { 0 };
	char chFileVersion[15] = { 0 };
	char chFileUtime[20] = { 0 };
	char chFileGuid[41] = { 0 };

	if (!m_pe->CheckFile(v_wstrFilePath))
	{
		wcout << v_wstrFilePath << " check error" << endl;
	}
	char * chFilePE = new char[128];

	m_pe->getFilePath(chFilePE);
	string fileName = srcFileName(chFilePE);
	snprintf(chFileName, sizeof(chFileName), "%-23s", fileName.c_str());
	string strFileName = chFileName;

	m_pe->getFileSoftVersion(chFilePE);
	snprintf(chFileVersion, sizeof(chFileVersion), "%-15s", chFilePE);
	string strGoalVersion = chFileVersion;

	m_pe->getFileCreateTime(chFilePE);
	snprintf(chFileUtime, sizeof(chFileUtime), "%-15s", chFilePE);
	string strGoalUtime = chFileUtime;

	m_pe->getFileGuid(chFilePE);
	snprintf(chFileGuid, sizeof(chFileGuid), "%40s", chFilePE);
	string strGuid = chFileGuid;

	string PERes = strFileName + strGoalVersion + strGoalUtime + strGuid;
	v_vecFiles.push_back(PERes);
}

void PEinfo::printfVec(vector<string>& v_vecFiles)
{
	vector<string>::iterator it = v_vecFiles.begin();
	for (; it != v_vecFiles.end(); ++it)
	{
		cout << *it << endl;
	}
}

void PEinfo::GetLdfcrRunDll()
{
	// 直接调用InsertModules，不需要重复初始化ldfcr
	// 因为在main.cpp中已经调用了ldfcr_InitStartup()
	printf("=== Getting loaded modules via EnumProcessModules ===\n");
	InsertModules();
}

void PEinfo::InsertModules()
{
	DWORD dwpid = GetCurrentProcessId();
	//提升进程权限
	//打开令牌
	HANDLE hToken;//创建令牌句柄
	if (!OpenProcessToken(GetCurrentProcess(), TOKEN_ALL_ACCESS | TOKEN_QUERY, &hToken))//打开令牌句柄，设置令牌权限
	{
		printf("OpenProcessToken Failed.");
		return;
	}
	TOKEN_PRIVILEGES tkp;
	LookupPrivilegeValue(NULL, SE_SHUTDOWN_NAME, &tkp.Privileges[0].Luid);//查看令牌权限
	tkp.PrivilegeCount = 1;
	tkp.Privileges[0].Attributes = SE_PRIVILEGE_ENABLED;
	AdjustTokenPrivileges(hToken, FALSE, &tkp, 0, (PTOKEN_PRIVILEGES)NULL, 0);
	if (GetLastError() != ERROR_SUCCESS)
	{
		printf("AdjustTokenPrivileges Failed.");
		return;
	}


	HMODULE hMods[1024];
	HANDLE hProcess;
	DWORD cbNeeded;
	unsigned int i;

	// Print the process identifier.

	/*printf("\nProcess ID: %u\n", dwpid);*/

	// Get a handle to the process.

	hProcess = OpenProcess(PROCESS_QUERY_INFORMATION |
		PROCESS_VM_READ,
		FALSE, dwpid);
	if (NULL == hProcess)
	{
		printf("hProcess is nullptr\n");
		return;
	}

	// Get a list of all the modules in this process.

	if (EnumProcessModules(hProcess, hMods, sizeof(hMods), &cbNeeded))
	{
		for (i = 0; i < (cbNeeded / sizeof(HMODULE)); i++)
		{
			wchar_t szModName[MAX_PATH];

			// Get the full path to the module's file.

			if (GetModuleFileNameEx(hProcess, hMods[i], szModName,
				sizeof(szModName) / sizeof(TCHAR)))
			{
				// Print the module name and handle value.

				m_dllVec.push_back(szModName);
			}
		}
	}

	// Release the handle to the process.

	CloseHandle(hProcess);

	return;
}

bool PEinfo::judgeFileIsWorkPath(const wchar_t * v_wchFilePath)
{
	if (m_dllVec.empty())
	{
		cout << "dll vec get error" << endl;
		return false;
	}
	for (size_t i = 0; i < m_dllVec.size(); ++i)
	{
		if (wcscmp(v_wchFilePath, m_dllVec[i].c_str()) == 0)
		{
			return true;
		}
	}
	wcout << v_wchFilePath << " not found in the running path" << endl;
	return false;
}

std::string PEinfo::WstringToString(const wstring v_wstr)
{
	// wstring转string
	unsigned len = v_wstr.size() * 4;
	setlocale(LC_CTYPE, "");
	char *p = new char[len];
	wcstombs(p, v_wstr.c_str(), len);
	std::string str1(p);
	delete[] p;
	return str1;
}

std::wstring PEinfo::StringToWstring(const string v_str)
{
	// string转wstring的正确实现
	if (v_str.empty()) return L"";

	int len = MultiByteToWideChar(CP_UTF8, 0, v_str.c_str(), -1, NULL, 0);
	if (len == 0) return L"";

	wchar_t* wstr = new wchar_t[len];
	MultiByteToWideChar(CP_UTF8, 0, v_str.c_str(), -1, wstr, len);

	std::wstring result(wstr);
	delete[] wstr;
	return result;
}

std::string PEinfo::srcFileName(const std::string v_wstrPath)
{
	char chPath_buffer[_MAX_PATH];
	char chDrive[_MAX_DRIVE];
	char chDir[_MAX_DIR];
	char chFname[_MAX_FNAME];
	char chExt[_MAX_EXT];
	char chPath[_MAX_EXT];
	//wcscpy(chPath_buffer, __argv[0]);
	_splitpath(v_wstrPath.c_str(), chDrive, chDir, chFname, chExt);
	// Note: _wsplitpath is deprecated; consider using _splitpath_s instead
	std::string strRes = chFname;
	strRes.append(chExt);
	return strRes;
}

/**
 * 收集组件版本信息（类似Linux/Mac的GetVersionInfoByOrder）
 *
 * 此函数在ldfcr_InitStartup()后调用，此时所有动态库都已加载完毕。
 * 使用进程模块枚举获取实际加载的组件，不依赖配置文件过滤。
 * 收集当前目录中所有组件的详细信息，存储到内部容器中，
 * 后续可以多次调用PrintVersionInfo()输出相同的信息。
 */
void PEinfo::GetVersionInfoByOrder()
{
	vector<wstring> wstrfileVec;
	vector<string> strfileVec;
	vector<string> strPathVec;

	// === 方法1：获取实际加载的模块（推荐，类似Linux/Mac） ===
	printf("=== DEBUG: Getting loaded modules ===\n");

	// 获取实际加载的模块列表
	GetLdfcrRunDll();  // 填充 m_dllVec

	printf("Found %d loaded modules:\n", (int)m_dllVec.size());
	for (size_t i = 0; i < m_dllVec.size(); i++) {
		string modulePath = WstringToString(m_dllVec[i]);
		printf("  %d: %s\n", (int)i, modulePath.c_str());

		// 检查是否为目标DLL（不使用配置文件过滤）
		if (isTargetDLL(m_dllVec[i])) {
			wstrfileVec.push_back(m_dllVec[i]);
		}
	}

	// === 方法2：文件系统扫描（作为补充） ===
	printf("=== DEBUG: File system scan ===\n");

	// 扫描当前目录
	wstring wstrCurPath = GetCurModuleFile();
	vector<wstring> fsFileVec;
	getCurFilesWithoutFilter(wstrCurPath, fsFileVec);

	// 扩展扫描：检查是否有lib子目录（类似Linux的结构）
	wstring libPath = wstrCurPath + L"\\lib";
	vector<wstring> libFileVec;
	getCurFilesWithoutFilter(libPath, libFileVec);

	// 将lib目录中的文件添加到主列表
	for (const auto& libFile : libFileVec) {
		if (isTargetDLL(libFile)) {
			fsFileVec.push_back(libFile);
		}
	}

	// 扩展扫描：检查lib\dlpcomm子目录
	wstring dlpcommPath = wstrCurPath + L"\\lib\\dlpcomm";
	vector<wstring> dlpcommFileVec;
	getCurFilesWithoutFilter(dlpcommPath, dlpcommFileVec);

	// 将lib\dlpcomm目录中的文件添加到主列表
	for (const auto& dlpcommFile : dlpcommFileVec) {
		if (isTargetDLL(dlpcommFile)) {
			fsFileVec.push_back(dlpcommFile);
		}
	}

	// 合并文件系统扫描结果（去重）
	for (const auto& fsFile : fsFileVec) {
		bool exists = false;
		for (const auto& existingFile : wstrfileVec) {
			if (existingFile == fsFile) {
				exists = true;
				break;
			}
		}
		if (!exists) {
			wstrfileVec.push_back(fsFile);
		}
	}

	printf("Total unique target files found: %d\n", (int)wstrfileVec.size());
	for (size_t i = 0; i < wstrfileVec.size(); i++) {
		string fileName = WstringToString(wstrfileVec[i]);
		printf("  %d: %s\n", (int)i, fileName.c_str());
	}
	printf("=== END DEBUG ===\n");

	// 收集所有DLL信息和路径
	for (size_t i = 0; i < wstrfileVec.size(); i++)
	{
		InsertPEInfo(wstrfileVec[i].c_str(), strfileVec);
		// 存储完整路径
		string fullPath = WstringToString(wstrfileVec[i]);
		strPathVec.push_back(fullPath);
	}

	// 按照预定义顺序收集组件信息到内部容器
	const vector<string>& orderedLibs = getOrderedLibs();

	// 清空之前的收集结果
	m_collectedComponentInfos.clear();
	m_collectedComponentPaths.clear();
	m_collectedComponentNames.clear();

	for (const auto& libName : orderedLibs) {
		bool found = false;

		// 查找匹配的组件
		for (int i = 0; i < strfileVec.size(); ++i) {
			string fileName = srcFileName(strPathVec[i]);
			// 移除.dll和.exe扩展名
			if (fileName.length() > 4) {
				if (fileName.substr(fileName.length() - 4) == ".dll" ||
					fileName.substr(fileName.length() - 4) == ".exe") {
					fileName = fileName.substr(0, fileName.length() - 4);
				}
			}

			// 检查是否匹配当前要查找的库
			if (fileName == libName ||
				fileName.find(libName) != string::npos) {

				// 存储到内部容器
				m_collectedComponentInfos.push_back(strfileVec[i]);
				m_collectedComponentPaths.push_back(strPathVec[i]);
				m_collectedComponentNames.push_back(libName);

				found = true;
				break;
			}
		}

		// 如果没有找到匹配的组件，也要记录
		if (!found) {
			m_collectedComponentInfos.push_back("");  // 空信息表示未找到
			m_collectedComponentPaths.push_back("");
			m_collectedComponentNames.push_back(libName);
		}
	}
}

/**
 * 输出已收集的版本信息（类似Linux/Mac的PrintVersionInfo）
 *
 * 输出之前通过GetVersionInfoByOrder()收集的组件信息。
 * 可以多次调用，每次输出相同的信息，避免重复扫描。
 */
void PEinfo::PrintVersionInfo()
{
	int sequenceNumber = 1;

	for (size_t i = 0; i < m_collectedComponentNames.size(); ++i) {
		if (!m_collectedComponentInfos[i].empty()) {
			// 输出找到的组件信息
			printf("%d. %s\n", sequenceNumber, m_collectedComponentInfos[i].c_str());
			printf("   Location: %s\n", m_collectedComponentPaths[i].c_str());
		} else {
			// 输出未找到的组件提示
			printf("%d. %s no version number has been established \n", sequenceNumber, m_collectedComponentNames[i].c_str());
		}
		sequenceNumber++;
	}
}



/**
 * 按预定义顺序输出组件信息
 *
 * 此函数用于在程序启动和结束时输出基线库信息，支持动态对比。
 * 与原GetPEinfo()方法的区别：
 * 1. 按getOrderedLibs()定义的优先级顺序输出，而非发现顺序
 * 2. 为每个组件添加序号（1. 2. 3. ...）
 * 3. 在每个组件下一行显示完整路径（Location: 完整路径）
 * 4. 对于不存在的组件，输出"组件名 no version number has been established"提示
 *
 * 输出格式：
 * 1. 组件名.dll    版本号    时间    GUID
 *    Location: 完整路径
 * 2. 下一个组件...
 *
 * 用途：用于程序头部和尾部的基线库信息对比，便于检测运行过程中
 *       动态加载的组件变化。
 */
void PEinfo::GetPEinfoOrdered()
{
	vector<wstring> wstrfileVec;
	vector<string> strfileVec;
	vector<string> strPathVec;  // 存储完整路径

	wstring wstrCurPath = GetCurModuleFile();
	getCurFiles(wstrCurPath, wstrfileVec);

	// 收集所有DLL信息和路径
	for (size_t i = 0; i < wstrfileVec.size(); i++)
	{
		InsertPEInfo(wstrfileVec[i].c_str(), strfileVec);
		// 存储完整路径
		string fullPath = WstringToString(wstrfileVec[i]);
		strPathVec.push_back(fullPath);
	}



	// 按照预定义顺序输出
	const vector<string>& orderedLibs = getOrderedLibs();
	int sequenceNumber = 1;

	for (const auto& libName : orderedLibs) {
		bool found = false;

		// 查找匹配的组件
		for (int i = 0; i < strfileVec.size(); ++i) {
			string fileName = srcFileName(strPathVec[i]);
			// 移除.dll和.exe扩展名
			if (fileName.length() > 4) {
				if (fileName.substr(fileName.length() - 4) == ".dll" ||
					fileName.substr(fileName.length() - 4) == ".exe") {
					fileName = fileName.substr(0, fileName.length() - 4);
				}
			}

			// 检查是否匹配当前要查找的库
			if (fileName == libName ||
				fileName.find(libName) != string::npos) {

				// 输出序号和组件信息（添加换行）
				printf("%d. %s\n", sequenceNumber, strfileVec[i].c_str());

				// 下一行输出完整路径
				printf("   Location: %s\n", strPathVec[i].c_str());

				sequenceNumber++;
				found = true;
				break;  // 找到匹配项后跳出内层循环
			}
		}

		// 如果没有找到匹配的组件，输出提示信息（与Linux保持一致）
		if (!found) {
			printf("%d. %s no version number has been established \n", sequenceNumber, libName.c_str());
			sequenceNumber++;
		}
	}
}
