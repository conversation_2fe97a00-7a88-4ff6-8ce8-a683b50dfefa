// Mode_P_Dialog.cpp : 实现文件
//

#include "stdafx.h"
#include "ldfcrTester.h"
#include "Mode_P_Dialog.h"
#include "afxdialogex.h"
#include <fstream>
#include <ctime>
#include "trcrt.h"
#include "../INC/DlpULog.h"
#include "viewStgy.h"
#include "InPutCheckTextDlg.h"
#include "StrategyDlg.h"
#include "PublicFunction.h"
#include "thread/ThreadPool.h"
#include "ui/XFolderDialog.h"
#include "IniSetting.h"
#include "filesystem.hpp"

#include "rapidjson/document.h"
#include "rapidjson/error/en.h"

using namespace std;
using namespace rapidjson;
extern LOG_HANDLE g_log_handle;

#define MAX_FILE_COUNT          1024

// Mode_P_Dialog 对话框

IMPLEMENT_DYNAMIC(Mode_P_Dialog, CDialogEx)

Mode_P_Dialog::Mode_P_Dialog(CWnd* pParent /*=NULL*/)
	: CDialogEx()
	, m_strStrategyFilePath(_T(""))
	, m_strFilePath4FCR(_T(""))
	, m_intOpType(0)
	, m_iCount(0) //
	, m_outTimeJug(0)
	, m_str_time_count(_T(""))
	, m_thread_num(0)
	, m_strFolderPath4Fcr(_T(""))
	, m_iFileCount(0)
	, m_iSensFileCount(0)
	, m_cstrMergeCstrategy(_T(""))
	, m_iCountFile(0)
{
	m_pILDFcr = NULL;
	m_pCheckILDFcr = NULL;
	m_pEncryptILDFcr = NULL;
	m_pEncryptTagILDFcr = NULL;
	m_bDoingDirFcr = FALSE;
}

Mode_P_Dialog::~Mode_P_Dialog()
{
	if (iniConfig)
	{
		delete iniConfig;
		iniConfig = nullptr;
	}
	ldfcr_StopRelease();
}

void Mode_P_Dialog::DoDataExchange(CDataExchange* pDX)
{
	CDialogEx::DoDataExchange(pDX);
	DDX_Control(pDX, IDC_EDIT1, m_CStrategyEdit);
	DDX_Text(pDX, IDC_EDIT1, m_strStrategyFilePath);
	DDX_Control(pDX, IDC_EDIT14, m_CFcrEdit);
	DDX_Text(pDX, IDC_EDIT14, m_strFilePath4FCR);
	DDX_Text(pDX, IDC_EDIT13, m_str_time_count);
	DDX_Text(pDX, IDC_EDIT17, m_thread_num);
	DDX_Control(pDX, IDC_EDIT16, m_CFolderPath4FcrEdit);
	DDX_Text(pDX, IDC_EDIT16, m_strFolderPath4Fcr);
	DDX_Text(pDX, IDC_EDIT11, m_iFileCount);
	DDX_Text(pDX, IDC_EDIT12, m_iSensFileCount);
	DDX_Control(pDX, IDC_COMBO_SELECT, m_ComboSelectIdName);
	DDX_Control(pDX, IDC_OUTPUT, m_edit);
	DDX_Control(pDX, IDC_EDIT2, m_mergeEdit);
	DDX_Text(pDX, IDC_EDIT2, m_cstrMergeCstrategy);
	DDX_Text(pDX, IDC_EDIT_COUNT, m_iCountFile);
	DDX_Control(pDX, IDC_COMBO3, m_combox_detect_file);
	DDX_Control(pDX, IDC_EDIT3, m_cstrFileProp_Edit);
}


BEGIN_MESSAGE_MAP(Mode_P_Dialog, CDialogEx)
	ON_BN_CLICKED(IDC_OPEN_STRATEGY, &Mode_P_Dialog::OnBnClickedOpenStrategy)
	ON_BN_CLICKED(IDC_UPDATE_STRATEGY, &Mode_P_Dialog::OnBnClickedUpdateStrategy)
	ON_BN_CLICKED(IDC_VIEW_STRATEGY, &Mode_P_Dialog::OnBnClickedViewStrategy)
	ON_BN_CLICKED(IDC_EXEC_TCR, &Mode_P_Dialog::OnBnClickedExecTcr)
	ON_BN_CLICKED(IDC_OPEN_FILE, &Mode_P_Dialog::OnBnClickedOpenFile)
	ON_BN_CLICKED(IDC_BTN_FCR, &Mode_P_Dialog::OnBnClickedBtnFcr)
	ON_BN_CLICKED(IDC_CHECK9, &Mode_P_Dialog::OnBnClickedCheck9)
	ON_BN_CLICKED(IDC_BTN_TCR, &Mode_P_Dialog::OnBnClickedBtnTcr)
	ON_BN_CLICKED(IDC_OPEN_FOLDER, &Mode_P_Dialog::OnBnClickedOpenFolder)
	ON_BN_CLICKED(IDC_BTN_EXEC_FCR_FOLDER, &Mode_P_Dialog::OnBnClickedBtnExecFcrFolder)
	ON_BN_CLICKED(IDC_BTN_EXEC_TCR_FOLDER, &Mode_P_Dialog::OnBnClickedBtnExecTcrFolder)
	ON_BN_CLICKED(IDC_SET_OUT_TIME, &Mode_P_Dialog::OnBnClickedSetOutTime)
	ON_BN_CLICKED(IDC_SET_WITH_PWD, &Mode_P_Dialog::OnBnClickedSetWithPwd)
	ON_BN_CLICKED(IDC_ADV_BUTTON_SET, &Mode_P_Dialog::OnBnClickedAdvButtonSet)
	ON_BN_CLICKED(IDC_GET_CPU, &Mode_P_Dialog::OnBnClickedGetCpu)
	ON_BN_CLICKED(IDC_STOP_BUTTON, &Mode_P_Dialog::OnBnClickedStopButton)
	ON_BN_CLICKED(IDC_BTN_OCR_TYPE, &Mode_P_Dialog::OnBnClickedBtnOcrType)
	ON_BN_CLICKED(IDC_BTN2_OPEN, &Mode_P_Dialog::OnBnClickedBtn2Open)
	ON_BN_CLICKED(IDC_BTN_VIEW, &Mode_P_Dialog::OnBnClickedBtnView)
	ON_BN_CLICKED(IDC_BTN_CLEAR_PT, &Mode_P_Dialog::OnBnClickedBtnClearPt)
	ON_BN_CLICKED(IDC_BRN_CLEAR_LX, &Mode_P_Dialog::OnBnClickedBrnClearLx)
	ON_BN_CLICKED(IDC_RADIO1, &Mode_P_Dialog::OnBnClickedRadio1)
	ON_BN_CLICKED(IDC_RADIO2, &Mode_P_Dialog::OnBnClickedRadio2)
	ON_BN_CLICKED(IDC_RADIO1, &Mode_P_Dialog::OnBnClickedRadio1)
	ON_BN_CLICKED(IDC_RADIO3, &Mode_P_Dialog::OnBnClickedRadio3)
	ON_EN_CHANGE(IDC_EDIT14, &Mode_P_Dialog::OnEnChangeEdit14)
	ON_BN_CLICKED(IDC_BTN_COM_FCR, &Mode_P_Dialog::OnBnClickedBtnComFcr)
	ON_BN_CLICKED(IDC_BTN_COM_TCR, &Mode_P_Dialog::OnBnClickedBtnComTcr)
	ON_BN_CLICKED(IDC_GET_LEAK_STYLE, &Mode_P_Dialog::OnBnClickedGetLeakStyle)
	ON_BN_CLICKED(IDC_GET_ERROR, &Mode_P_Dialog::OnBnClickedGetError)
	ON_BN_CLICKED(IDC_RADIO4, &Mode_P_Dialog::OnBnClickedRadio4)
	ON_BN_CLICKED(IDC_BTN_STATEGY_SELECT, &Mode_P_Dialog::OnBnClickedBtnStategySelect)
	ON_BN_CLICKED(IDC_BTN_FILEPROP, &Mode_P_Dialog::OnBnClickedBtnFileprop)
END_MESSAGE_MAP()


// Mode_P_Dialog 消息处理程序

/*
* 控件：策略选择
* 功能：可选择后缀为(.data)的文件，选择路径成功后，保存策略的路径至ini文件中，并显示到示例编辑框里。
*/
void Mode_P_Dialog::OnBnClickedOpenStrategy()
{
	// TODO: 在此添加控件通知处理程序代码
	CString strInitialDir;
	m_CStrategyEdit.GetWindowText(strInitialDir); // 获取 IDC_STRATEGY_FILEPATH 的路径
	CString initialPath;

	if (!strInitialDir.IsEmpty())
	{
		// 编辑框中的路径不为空，将其作为初始路径
		initialPath = strInitialDir;
	}
	else
	{
		// 编辑框中的路径为空，将.exe文件所在的目录作为初始路径
		char exeDir[MAX_PATH];
		trcrt::app::tr_app_get_app_directory(exeDir, MAX_PATH);
		initialPath = CString(exeDir);
		strInitialDir = initialPath;//1024
	}

	static WCHAR T;
	//CString strFile = _T("R:\\");
	CFileDialog dlgFile(TRUE, _T("data"), NULL, OFN_HIDEREADONLY, _T("Data Files (*.*)|*.*||"), NULL);
	dlgFile.m_ofn.lpstrInitialDir = initialPath;//1023

	if (dlgFile.DoModal())
	{
		strInitialDir = dlgFile.GetPathName();
		m_strStrategyFilePath = strInitialDir;
	}

	if (strInitialDir.IsEmpty())
	{
		return;
	}

	this->UpdateData(FALSE);

	std::string strStrategyPath;
	switch (GetCheckType())
	{
	case Check_Common_P:
		strStrategyPath = "Check_strategy_common";
		break;
	case Check_Encrypt_P:
		strStrategyPath = "Encrypt_strategy_common";
		break;
	case Check_Tag_P:
		strStrategyPath = "EncryptTag_strategy_common";
		break;
	case Check_G:
		strStrategyPath = "Global_strategy_common";
		break;
	default:
		break;
	}

	// 记录当前使用的策略路径
	BOOL blR = IniSetting::getInstance().recordPATH2INI(strStrategyPath.c_str(), m_strStrategyFilePath);
	if (blR < 0)
	{
		dlplog_error(g_log_handle, "Setting ini record failed");
	}
}

void Mode_P_Dialog::OnBnClickedUpdateStrategy()
{

	std::string str;

	int CurrentCheckType = GetCheckType();

	switch (CurrentCheckType)
	{
	case Check_Common_P:
		str = "当前更新外发检测策略\r\n";
		outPutFuc(str.c_str());
		break;
	case Check_Encrypt_P:
		str = "当前更新智能加密策略\r\n";
		outPutFuc(str.c_str());
		break;
	case Check_Tag_P:
		str = "当前更新加密+加标签策略\r\n";
		outPutFuc(str.c_str());
		break;
	case Check_G:
		str = "当前更新全局检测策略\r\n";
		outPutFuc(str.c_str());
		break;
	default:
		break;
	}
	this->UpdateData(TRUE);
	v_idStrategy.clear();
	if (CurrentCheckType != Check_G)
	{
		m_strStrategyMerge.clear();
		m_strStrategyMerge2.clear();
		bool bStrategy1 = true;  // 策略1更新状态
		bool bStrategy2 = true;  // 策略2更新状态

		do {
			struct _stat st;
			int ret = _wstat(m_strStrategyFilePath.operator LPCTSTR(), &st);
			if ((0 != ret) || (_S_IFREG != (_S_IFREG & st.st_mode)))
			{
				bStrategy1 = false;
				str = "普通策略加载失败\r\n";
				outPutFuc(str.c_str());
				break;
			}

			//加载策略文件
			int iStrategyLen = st.st_size;
			char* strategy_utf8str = new char[iStrategyLen + 1];
			FILE* fp = _wfopen(m_strStrategyFilePath.operator LPCTSTR(), L"rb");
			if (NULL == fp)
			{
				bStrategy1 = false;
				str = "打开策略文件失败，无法加载策略信息！";
				outPutFuc(str.c_str());
				break;
			}

			int iReadLen = fread(strategy_utf8str, sizeof(char), iStrategyLen, fp);
			if (iReadLen != iStrategyLen)
			{
				bStrategy1 = false;
				str = "读取策略文件出错，返回";
				outPutFuc(str.c_str());
				break;
			}

			strategy_utf8str[iStrategyLen] = 0; //设置字符串结尾符

			m_strStrategyMerge = strategy_utf8str;

			fclose(fp);
			fp = NULL;

		} while (false);

		do {
			struct _stat st;
			int ret = _wstat(m_cstrMergeCstrategy.operator LPCTSTR(), &st);
			if ((0 != ret) || (_S_IFREG != (_S_IFREG & st.st_mode)))
			{
				bStrategy2 = false;
				str = "零星策略加载失败\r\n";
				outPutFuc(str.c_str());
				break;
			}

			//加载策略文件
			int iStrategyLen = st.st_size;
			char* strategy_utf8str = new char[iStrategyLen + 1];
			FILE* fp = _wfopen(m_cstrMergeCstrategy.operator LPCTSTR(), L"rb");
			if (NULL == fp)
			{
				bStrategy2 = false;
				str = "打开策略文件失败，无法加载策略信息！";
				outPutFuc(str.c_str());
				break;
			}

			int iReadLen = fread(strategy_utf8str, sizeof(char), iStrategyLen, fp);
			if (iReadLen != iStrategyLen)
			{
				bStrategy2 = false;
				str = "读取策略文件出错，返回";
				outPutFuc(str.c_str());
				break;
			}

			strategy_utf8str[iStrategyLen] = 0; //设置字符串结尾符

			m_strStrategyMerge2 = strategy_utf8str;

			fclose(fp);
			fp = NULL;

		} while (false);

		//设置策略
		BOOL bRet;
		//char s[256] = { 0 };
		bRet = m_pILDFcr->mergeUpdateStrategy(m_strStrategyMerge.c_str(), m_strStrategyMerge2.c_str()); //8.17
		if (!bRet)
		{
			str = "策略更新失败\r\n";
			outPutFuc(str.c_str());
			return;
		}
		else
		{
			if (!bStrategy1)
			{
				str = "普通策略更新失败,零星策略更新成功\r\n";
				outPutFuc(str.c_str());
			}
			else if (!bStrategy2)
			{
				str = "普通策略更新成功,零星策略更新失败\r\n";
				outPutFuc(str.c_str());
			}
			else
			{
				str = "策略更新成功\r\n";
				outPutFuc(str.c_str());
			}
		}

		/*
		*  获取策略ID模块
		*/

		//清空idNameMap
		idNameMap.clear();

		//获取编辑框中的文件路径
		CString filePath;

		// 记录当前使用的策略路径
		if (bStrategy1)
		{
			std::string strStrategyPath;
			switch (GetCheckType())
			{
			case Check_Common_P:
				strStrategyPath = "Check_strategy_common";
				break;
			case Check_Encrypt_P:
				strStrategyPath = "Encrypt_strategy_common";
				break;
			case Check_Tag_P:
				strStrategyPath = "EncryptTag_strategy_common";
				break;
			case Check_G:
				strStrategyPath = "Global_strategy_common";
				break;
			default:
				break;
			}
			BOOL blR = IniSetting::getInstance().recordPATH2INI(strStrategyPath.c_str(), m_strStrategyFilePath);
			if (blR < 0)
			{
				dlplog_error(g_log_handle, "Setting ini record failed");
			}

			GetDlgItemText(IDC_EDIT1, filePath);

			//提取策略ID和NAME
			ExtractIdWithName(filePath, idNameMap);
		}

		if (bStrategy2)
		{
			std::string strStrategyDrip;
			switch (GetCheckType())
			{
			case Check_Common_P:
				strStrategyDrip = "Check_strategy_drip";
				break;
			case Check_Encrypt_P:
				strStrategyDrip = "Encrypt_strategy_drip";
				break;
			case Check_Tag_P:
				strStrategyDrip = "EncryptTag_strategy_drip";
				break;
			case Check_G:
				break;
			default:
				break;
			}
			BOOL blR = IniSetting::getInstance().recordPATH2INI(strStrategyDrip.c_str(), m_cstrMergeCstrategy);
			if (blR < 0)
			{
				dlplog_error(g_log_handle, "Setting ini record failed");
			}

			GetDlgItemText(IDC_EDIT2, filePath);

			//提取策略ID和NAME
			ExtractIdWithName(filePath, idNameMap);
		}

		updateStrategyComBox();
	}
	else
	{
		const wchar_t* v_csStrategy = NULL; //
		do {
			struct _stat st;
			int ret = _wstat(m_strStrategyFilePath.operator LPCTSTR(), &st);
			if ((0 != ret) || (_S_IFREG != (_S_IFREG & st.st_mode)))
			{

				str = "全局策略加载失败\r\n";
				outPutFuc(str.c_str());
				break;
			}

			//加载策略文件
			int iStrategyLen = st.st_size;
			char* strategy_utf8str = new char[iStrategyLen + 1];
			FILE* fp = _wfopen(m_strStrategyFilePath.operator LPCTSTR(), L"rb");
			if (NULL == fp)
			{
				str = "打开策略文件失败，无法加载策略信息！";
				outPutFuc(str.c_str());
				break;
			}

			int iReadLen = fread(strategy_utf8str, sizeof(char), iStrategyLen, fp);
			if (iReadLen != iStrategyLen)
			{
				str = "读取策略文件出错，返回";
				outPutFuc(str.c_str());
				break;
			}

			strategy_utf8str[iStrategyLen] = 0; //设置字符串结尾符

			m_strStrategyMerge = strategy_utf8str;

			fclose(fp);
			fp = NULL;

		} while (false);

		//设置策略
		BOOL bRet;
		//char s[256] = { 0 };
		//设置策略

		bRet = ldfcr_UpdateStrategy(m_strStrategyMerge.c_str()); //8.17

		if (!bRet)
		{
			std::string str = "策略更新失败[" + wstringToString(m_strStrategyFilePath.GetString()) + "]\r\n";
			outPutFuc(str.c_str());
			return;
		}
		else
		{
			//CStringA stra((TCHAR*)(LPCTSTR)(this->m_strStrategy)); 2023 8 17
			//std::string str((char *)(LPCSTR)(stra));
			std::string str = "策略更新成功[" + wstringToString(m_strStrategyFilePath.GetString()) + "]\r\n";
			outPutFuc(str.c_str());
			dlplog_debug(g_log_handle, "[%s]new strategy : %s", __FUNCTION__, m_strStrategyMerge.c_str());
		}

		/*
		*  获取策略ID模块
		*/

		//清空idNameMap
		idNameMap.clear();

		//获取编辑框中的文件路径
		CString filePath;
		GetDlgItemText(IDC_EDIT1, filePath);

		//提取策略ID和NAME
		ExtractIdWithName(filePath, idNameMap);

		updateStrategyComBox();

		// 记录当前使用的策略路径
		std::string strStrategyPath = "Global_strategy_common";
		BOOL blR = IniSetting::getInstance().recordPATH2INI(strStrategyPath.c_str(), m_strStrategyFilePath);
		if (blR < 0)
		{
			dlplog_error(g_log_handle, "Setting ini record failed");
		}
	}

}

/*
* 控件：查看策略
* 功能：选择策略路径后，点击控件可查看当前策略的内容
*/
void Mode_P_Dialog::OnBnClickedViewStrategy()
{
	this->UpdateData(TRUE);//更新策略
	CWnd* pWnd = GetDlgItem(IDC_VIEW_STGY);

	CString strMfc;

	// TODO: 在此添加控件通知处理程序代码
	CFile file(m_strStrategyFilePath, CFile::modeRead);

	int len = file.GetLength();//获取file文件中内容的长度；

	char* data = new char[len + 1];//定义一个存放数据的指针；

	memset(data, 0, len + 1);//   将已开辟内存空间 data的,长度为len+1首 个字节的值设为值 0

	file.Read(data, len);//读取文件内容并赋值给data;
	/*utf8转GBK*/
	string _str;
	_str = Utf8ToGbk(data);
	strMfc = _str.c_str();
	//创建非模态 注意内存泄漏，创建delete
	viewStgy* dlgView = new viewStgy(strMfc);
	BOOL bRet = dlgView->Create(IDD_DIALOG_VIEW);
	if (bRet)
	{
		dlgView->ShowWindow(SW_SHOW);
	}
}


void Mode_P_Dialog::OnBnClickedExecTcr()
{
	// TODO: 在此添加控件通知处理程序代码

	InPutCheckTextDlg dlg;

	if (IDOK != dlg.DoModal())
	{
		return;
	}

	iniConfig->GetIniConfig(IniSetting::getInstance()); // settingini配置文件生效

	string strTestContext = GbkToUtf8(wstringToString(dlg.m_strCheckText.GetString()).c_str());  //2023 8 17 弃用W接口，将输入框内的文本从cstring转换为string

	iniConfig->SetIniConfig(); // 敏感识别参数配置封装

	SetCheckType(Operate_Single, Check_Text_Dlg);
	dlp_tcrInterface("检测文本", strTestContext);

}

void Mode_P_Dialog::OnBnClickedOpenFile()
{
	// TODO: 在此添加控件通知处理程序代码
	CString strInitialDir;
	m_CFcrEdit.GetWindowText(strInitialDir);
	CString initialPath;

	if (!strInitialDir.IsEmpty())
	{
		// 如果编辑框中的路径不为空，将其作为初始路径
		initialPath = strInitialDir;
	}
	else
	{
		// 如果编辑框中的路径为空，将.exe文件所在的目录作为初始路径
		char exeDir[MAX_PATH];
		trcrt::app::tr_app_get_app_directory(exeDir, MAX_PATH);
		initialPath = CString(exeDir);
	}

	//CString strFile = _T("R:\\");
	CFileDialog   dlgFile(TRUE, _T("txt"), NULL, OFN_HIDEREADONLY, _T("All Files (*.*)|*.*||"), NULL);
	//dlgFile.m_ofn.lpstrInitialDir = _T("C:\\Users\\<USER>\\Desktop\\log(3)\\"); //指定文件夹	1025
	dlgFile.m_ofn.lpstrInitialDir = initialPath;//1025
	if (dlgFile.DoModal())
	{
		strInitialDir = dlgFile.GetPathName();
	}

	if (strInitialDir.IsEmpty())
	{
		return;
	}

	m_strFilePath4FCR = strInitialDir;
	m_strFilePath4FCRMerge = wstringToString(m_strFilePath4FCR.GetString());
	std::string str = "选择待测试文件:[" + m_strFilePath4FCRMerge + "]\r\n";
	outPutFuc(str.c_str());
	this->UpdateData(FALSE);
}

void Mode_P_Dialog::ModifyInterfaceValues()
{
	// 获取
	this->m_strStrategyFilePath = IniSetting::getInstance().GetComStrategyPath();  // 普通策略
	this->m_cstrMergeCstrategy = IniSetting::getInstance().GetDripStrategyPath();  // 零星策略
	this->m_strFilePath4FCR = IniSetting::getInstance().GetTestPath();  // 检测文件
	this->m_strFolderPath4Fcr = IniSetting::getInstance().GetFolderPath();  // 检测文件夹
	this->m_thread_num = IniSetting::getInstance().GetThreadCount();  // 
	//const char* outTimeJsonRuleName = ini.GetValue("ResponseRuleName", "passwordJsonName", NULL);
	//this->m_cstrOutTimeJsonRuleName = outTimeJsonRuleName;
	//const char* passwordJsonRuleName = ini.GetValue("ResponseRuleName", "outTimeJsonName", NULL);
	//this->m_cstrPasswordJsonRuleName = passwordJsonRuleName;
	this->UpdateData(FALSE);
	
}


void Mode_P_Dialog::OnBnClickedBtnFcr()
{
	this->UpdateData(TRUE);
	iniConfig->GetIniConfig(IniSetting::getInstance()); // settingini配置文件生效
	checkCount = iniConfig->GetCheckCount();
	if (0 >= this->checkCount)
	{
		return;
	}
	iniConfig->SetIniConfig(); // 敏感识别参数配置封装

	m_pILDFcr->SetFilterGlobalParamer(iniConfig->GetPassWordJson().c_str()); //2023 Q1

	wstring strCheckFile = m_strFilePath4FCR.GetBuffer();

	// 封装策略选取部分
	StrategySelect();

	SetCheckType(Operate_Single, Check_File);

	std::thread threadCheck(std::bind(&Mode_P_Dialog::dlp_checkSingleFile, this, strCheckFile.c_str()));
	GetDlgItem(IDC_BTN_FCR)->EnableWindow(FALSE);
	std::string strTestPath;
	switch (GetCheckType())
	{
	case Check_Common_P:
		strTestPath = "Check_test_path";
		break;
	case Check_Encrypt_P:
		strTestPath = "Encrypt_test_path";
		break;
	case Check_Tag_P:
		strTestPath = "EncryptTag_test_path";
		break;
	case Check_G:
		strTestPath = "Global_test_path";
		break;
	default:
		break;
	}
	BOOL blR = IniSetting::getInstance().recordPATH2INI(strTestPath.c_str(), m_strFilePath4FCR);
	if (blR < 0)
	{
		dlplog_error(g_log_handle, "Setting ini record failed");
	}
	threadCheck.detach();
}


void Mode_P_Dialog::OnBnClickedCheck9()
{
	// TODO: 在此添加控件通知处理程序代码
	int state = ((CButton*)GetDlgItem(IDC_CHECK9))->GetCheck();
	if (state == 1)
	{
		m_pwd_error = true;
	}
	else if (state == 0)
	{
		m_pwd_error = false;
	}
}


void Mode_P_Dialog::OnBnClickedBtnTcr()
{
	// TODO: 在此添加控件通知处理程序代码
	this->UpdateData(TRUE);
	iniConfig->GetIniConfig(IniSetting::getInstance()); // settingini配置文件生效
	checkCount = iniConfig->GetCheckCount();
	//init moudle
	if (0 >= this->checkCount)
	{
		GetDlgItem(IDC_BTN_TCR)->EnableWindow(TRUE);
		return;
	}

	iniConfig->SetIniConfig(); // 敏感识别参数配置封装

	m_pILDFcr->SetFilterGlobalParamer(iniConfig->GetPassWordJson().c_str()); //2023 Q1

	wstring strCheckFile = m_strFilePath4FCR.GetBuffer();

	// 封装策略选取部分
	StrategySelect();

	SetCheckType(Operate_Single, Check_Text);

	std::thread threadCheck(std::bind(&Mode_P_Dialog::dlp_checkSingleText, this, strCheckFile.c_str()));
	GetDlgItem(IDC_BTN_TCR)->EnableWindow(FALSE);
	// 记录当前测试文件路径
	std::string strTestPath;
	switch (GetCheckType())
	{
	case Check_Common_P:
		strTestPath = "Check_test_path";
		break;
	case Check_Encrypt_P:
		strTestPath = "Encrypt_test_path";
		break;
	case Check_Tag_P:
		strTestPath = "EncryptTag_test_path";
		break;
	case Check_G:
		strTestPath = "Global_test_path";
		break;
	default:
		break;
	}
	BOOL blR = IniSetting::getInstance().recordPATH2INI(strTestPath.c_str(), m_strFilePath4FCR);
	if (blR < 0)
	{
		dlplog_error(g_log_handle, "Setting ini record failed");
	}
	threadCheck.detach();
}


void Mode_P_Dialog::OnBnClickedOpenFolder()
{
	// TODO: 在此添加控件通知处理程序代码
	CString strInitialDir;
	m_CFolderPath4FcrEdit.GetWindowText(strInitialDir);
	CString initialPath;

	// 编辑框路径不为空

	if (!strInitialDir.IsEmpty())
	{
		initialPath = strInitialDir;
	}
	// 路径为空或不存在，使用.exe文件所在目录
	else
	{
		char exeDir[MAX_PATH];
		trcrt::app::tr_app_get_app_directory(exeDir, MAX_PATH);
		initialPath = CString(exeDir);
		strInitialDir = initialPath;
	}

	//CString  initFolder = L"";
	CXFolderDialog dlg(strInitialDir);
	dlg.m_ofn.lpstrInitialDir = initialPath;
	dlg.SetOsVersion((CXFolderDialog::XFILEDIALOG_OS_VERSION)0);
	CXFolderDialog::XFILEDIALOG_OS_VERSION eVer = dlg.GetOsVersion();

	dlg.EnableRegistry(TRUE);

	dlg.SetViewMode(0);
	//dlg.SetTitle(_T("Select Folder"));	// defaults to "Select Folder"
	if (dlg.DoModal() == IDOK)//打开对话框
	{
		strInitialDir = dlg.GetPath();
		if (strInitialDir.IsEmpty() || !PathFileExists(strInitialDir))
		{
			std::string str = "无效目录";
			outPutFuc(str.c_str());
			return;
		}
		m_strFolderPath4Fcr = strInitialDir;
		std::string str = "选择待测试目录:[" + wstringToString(m_strFolderPath4Fcr.GetString()) + "]\r\n";
		outPutFuc(str.c_str());
		this->UpdateData(FALSE);
	}
}

void Mode_P_Dialog::OnBnClickedBtnThreadCheckFcrFolder()
{
	std::string str = "文件检测目录开始\r\n";
	outPutFuc(str.c_str());
	m_filename_listF.clear();

	m_file_count_sens = 0;

	//线程启动
	int thread_max = m_thread_num;
	if (thread_max < 1 || thread_max > 48)
	{
		MessageBox(L"线程开辟范围建议1到48");
		this->SetDlgItemText(IDC_BTN_EXEC_FCR_FOLDER, L"开始检测文件");
		return;
	}
	dlplog_debug(g_log_handle, "threads FCR start , current threads init [%d]", thread_max);
	ThreadPool pool(thread_max);
	pool.init();


	WCHAR module_dir[MAX_PATH];
	::GetModuleFileNameW(NULL, module_dir, sizeof(module_dir) / sizeof(WCHAR));
	while ('\\' != module_dir[wcslen(module_dir) - 1])
	{
		module_dir[wcslen(module_dir) - 1] = '\0';
	}
	wcscat(module_dir, L"files.log");

	WCHAR log_dir[MAX_PATH];
	wcscpy(log_dir, module_dir);
	FILE* fp = _wfopen(log_dir, L"wt");
	//	int _err = GetLastError();
	if (NULL != fp)
	{
		_wsetlocale(0, L"chs");
	}

	ergodicFolder(m_strFolderPath4Fcr.operator LPCTSTR(), m_filename_listF);  // 遍历获取文件夹列表

	// =======================================2023 11 2 多线程模式============================================

	auto startT = std::chrono::duration_cast<std::chrono::microseconds>(std::chrono::system_clock::now().time_since_epoch()).count();

	this->m_iFileCount = 0;
	int size = m_filename_listF.size() + 1;  //防止爆栈

	m_iCountFile = m_filename_listF.size();

	dlplog_debug(g_log_handle, "size is [%d]\n", size - 1);

	iniConfig->GetIniConfig(IniSetting::getInstance()); // settingini配置文件生效

	iniConfig->SetIniConfig(); // 敏感识别参数配置封装

	int thread_index = 0;
	std::vector<std::future<void>> m_vecFuture;

	SetCheckType(Operate_Dir, Check_File);

	while (size != 0)
	{
		auto ret = pool.submit(std::bind(&Mode_P_Dialog::ldfcrTesterThreadFCR, this, thread_index));
		thread_index++;
		size--;
		m_vecFuture.push_back(std::move(ret));
	}

	if (NULL != fp)
	{
		fclose(fp);
	}

	for (auto& ret : m_vecFuture) {
		ret.get();
	}
	this->m_iSensFileCount = m_file_count_sens;
	pool.shutdown();
	auto endT = std::chrono::duration_cast<std::chrono::microseconds>(std::chrono::system_clock::now().time_since_epoch()).count();
	double tim = (double)(endT - startT) / 1000000;
	this->m_str_time_count.Format(_T("%.3f"), tim);
	this->UpdateData(FALSE);

	str = "文件检测目录结束\r\n";
	outPutFuc(str.c_str());
	GetDlgItem(IDC_BTN_EXEC_FCR_FOLDER)->EnableWindow(TRUE);
	std::string strThread;
	std::string strCheckFolder;
	switch (GetCheckType())
	{
	case Check_Common_P:
		strThread = "Check_thread_count";
		strCheckFolder = "Check_folder_path";
		break;
	case Check_Encrypt_P:
		strThread = "Encrypt_thread_count";
		strCheckFolder = "Encrypt_folder_path";
		break;
	case Check_Tag_P:
		strThread = "EncryptTag_thread_count";
		strCheckFolder = "EncryptTag_folder_path";
		break;
	case Check_G:
		strThread = "Global_thread_count";
		strCheckFolder = "Global_folder_path";
		break;
	default:
		break;
	}
	BOOL blR;
	// 记录当前配置的启用线程数
	blR = IniSetting::getInstance().recordDigit2INI("Thread", strThread.c_str(), m_thread_num);
	if (blR < 0)
	{
		dlplog_error(g_log_handle, "Setting ini record failed");
	}

	// 记录当前测试文件夹路径
	blR = IniSetting::getInstance().recordPATH2INI(strCheckFolder.c_str(), m_strFolderPath4Fcr);
	if (blR < 0)
	{
		dlplog_error(g_log_handle, "Setting ini record failed");
	}
}

void Mode_P_Dialog::OnBnClickedBtnExecFcrFolder()
{
	// TODO: 在此添加控件通知处理程序代码

	this->UpdateData(TRUE);

	std::thread threadCheck(std::bind(&Mode_P_Dialog::OnBnClickedBtnThreadCheckFcrFolder, this));
	GetDlgItem(IDC_BTN_EXEC_FCR_FOLDER)->EnableWindow(FALSE);
	threadCheck.detach();
}

void Mode_P_Dialog::ldfcrTesterThreadFCR(int thread_index)
{
	ghc::filesystem::path file_path;
	std::unique_lock<std::mutex> lock_chk(m_mutex, std::defer_lock);

	lock_chk.lock();

	if (m_filename_listF.empty())
	{
		//dlplog_debug(g_log_handle, "Task list empty so return");
		lock_chk.unlock();
		return;
	}
	else
	{
		file_path = m_filename_listF.back();
		m_filename_listF.pop_back();
		lock_chk.unlock();
	}

	std::string strFilename = wstringToString(file_path.c_str());

	//dlplog_debug(g_log_handle, "current file name is [%s] ,index is [%d] , m_filename_list.size is [%d] ", strFilename.c_str(), thread_index, m_filename_list.size());
	//检测文件
	if (!dlp_checkSingleFile(file_path.c_str()))
	{
		dlplog_debug(g_log_handle, "current file is [%s] , it s insensitive", strFilename.c_str());
	}
	else
	{
		dlplog_debug(g_log_handle, "current file is [%s] , it s sensitive", strFilename.c_str());
		m_file_count_sens++;
	}
	return;
}

void Mode_P_Dialog::ldfcrTesterThreadTCR(int thread_index)
{
	ghc::filesystem::path file_path;
	std::unique_lock<std::mutex> lock_chk(m_mutex, std::defer_lock);

	lock_chk.lock();

	if (m_filename_listT.empty())
	{
		//dlplog_debug(g_log_handle, "Task list empty so return");
		lock_chk.unlock();
		return;
	}
	else
	{
		file_path = m_filename_listT.back();
		m_filename_listT.pop_back();
		lock_chk.unlock();
	}
	std::string strFilename = wstringToString(file_path.c_str());

	//dlplog_debug(g_log_handle, "current file name is [%s] ,index is [%d] , m_filename_list.size is [%d] ", strFilename.c_str(), thread_index, m_filename_list.size());
	//检测文本
	if (!dlp_checkSingleText(file_path.c_str()))
	{
		dlplog_debug(g_log_handle, "current file is [%s] , it s insensitive", strFilename.c_str());
	}
	else
	{
		dlplog_debug(g_log_handle, "current file is [%s] , it s sensitive", strFilename.c_str());
		m_file_count_sens++;
	}
	return;
}

BOOL Mode_P_Dialog::dlp_checkSingleFile(const wchar_t* file_path)
{
	string strFilepath = GbkToUtf8(wstringToString(file_path).c_str());

	IFCRResult* result_p = NULL;
	IFCRDripResult* drip_result_p = NULL;

	BOOL bIsSensitive;

	DWORD dwTick;

	if (operateType == Operate_Single)
	{
		dwTick = GetTickCount();
	}
	else if (operateType == Operate_Dir)
	{
		this->checkCount = 1;
	}
	for (int i = 0; i < checkCount; i++)
	{
		if (GetCheckType() != Check_G)
		{
			bIsSensitive = m_pILDFcr->executeFCR(
				strFilepath.c_str(),
				iniConfig->Getfcrparm(),
				(void**)&result_p,
				(void**)&drip_result_p
				//NULL
			);
		}
		else
		{
			bIsSensitive = ldfcr_ExecuteFCR(
				strFilepath.c_str(),
				iniConfig->Getfcrparm(),
				(void**)&result_p,
				(void**)&drip_result_p
				//NULL
			);
		}


		bool MatchTypeF = true;
		dlp_Log(MatchTypeF, strFilepath, result_p, drip_result_p);
		if (NULL != result_p)
		{
			result_p->Release();
			result_p = NULL;
		}
		if (NULL != drip_result_p)
		{
			drip_result_p->Release();
			drip_result_p = NULL;
		}

		if (operateType == Operate_Single && CheckType == Check_File)
		{
			if (!bIsSensitive)
			{
				std::string str = "文件检测不敏感，检测文件:[" + strFilepath + "]检测策略:[" + strStrategyName + "]\r\n";
				outPutFuc(str.c_str());
				GetDlgItem(IDC_BTN_FCR)->EnableWindow(TRUE);

				dwTick = GetTickCount() - dwTick;

				m_str_time_count.Format(L"%8.3f", (double)dwTick / 1000);

				this->UpdateData(FALSE);

				return FALSE;
			}

			std::string str = "文件检测敏感，检测文件:[" + strFilepath + "]检测策略:[" + strStrategyName + "]\r\n";
			outPutFuc(str.c_str());
		}
		if (operateType == Operate_Dir && CheckType == Check_File)
		{
			if (iniConfig->Getfcrparm()->interrupt != 1)
			{
				this->m_iFileCount++;
			}
			if (!bIsSensitive)
			{
				return FALSE;
			}
			std::string str = "文件检测敏感，检测文件:[" + strFilepath + "]检测策略:[" + strStrategyName + "]\r\n";
			outPutFuc(str.c_str());
		}
	}

	if (operateType == Operate_Single)  // 961
	{
		dwTick = GetTickCount() - dwTick;

		m_str_time_count.Format(L"%8.3f", (double)dwTick / 1000);

		this->UpdateData(FALSE);

		GetDlgItem(IDC_BTN_FCR)->EnableWindow(TRUE);

	}

	return TRUE;
}

BOOL Mode_P_Dialog::dlp_checkSingleText(const wchar_t* file_path)
{
	//读取文本内容
	std::string strTextContent = " ";
	std::string filepath = wstringToString(file_path);

	bool bCheckRes;

	DWORD dwTick;
	if (operateType == Operate_Single)
	{
		dwTick = GetTickCount();
	}
	else if (operateType == Operate_Dir)
	{
		this->checkCount = 1;
	}
	bool bRes = ReadTextContent(filepath.c_str(), strTextContent);
	for (int i = 0; i < checkCount; i++)
	{
		if (!bRes)
		{
			strTextContent = " ";
		}
		else
		{
			bCheckRes = dlp_tcrInterface(filepath, strTextContent);
		}
	}

	if (operateType == Operate_Single)
	{
		dwTick = GetTickCount() - dwTick;

		m_str_time_count.Format(L"%8.3f", (double)dwTick / 1000);

		this->UpdateData(FALSE);

		GetDlgItem(IDC_BTN_TCR)->EnableWindow(TRUE);
	}
	return bCheckRes;
}

BOOL Mode_P_Dialog::dlp_tcrInterface(std::string v_strFile, std::string& v_strTextContext)
{
	IFCRResult* result_p = NULL;
	IFCRDripResult* drip_result_p = NULL;

	BOOL bIsSensitive;

	if (GetCheckType() != Check_G)
	{
		bIsSensitive = m_pILDFcr->executeTCR(
			v_strTextContext.c_str(), -1,
			iniConfig->Getfcrparm(),
			(void**)&result_p,
			(void**)&drip_result_p
		);
	}
	else
	{
		bIsSensitive = ldfcr_ExecuteTCR(
			v_strTextContext.c_str(), -1,
			iniConfig->Getfcrparm(),
			(void**)&result_p,
			(void**)&drip_result_p
		);
	}

	bool MatchTypeF = false;
	dlp_Log(MatchTypeF, v_strFile, result_p, drip_result_p);
	if (NULL != result_p)
	{
		result_p->Release();
		result_p = NULL;
	}
	if (NULL != drip_result_p)
	{
		drip_result_p->Release();
		drip_result_p = NULL;
	}

	if (operateType == Operate_Single && CheckType == Check_Text)
	{
		if (!bIsSensitive)
		{
			std::string str = "文件检测不敏感，检测文件:[" + v_strFile + "]检测策略:[" + strStrategyName + "]\r\n";
			outPutFuc(str.c_str());
			GetDlgItem(IDC_BTN_TCR)->EnableWindow(TRUE);

			this->UpdateData(FALSE);

			return FALSE;
		}

		std::string str = "文件检测敏感，检测文件:[" + v_strFile + "]检测策略:[" + strStrategyName + "]\r\n";
		outPutFuc(str.c_str());
	}
	if (operateType == Operate_Dir && CheckType == Check_Text)
	{
		if (iniConfig->Getfcrparm()->interrupt != 1)
		{
			this->m_iFileCount++;
		}
		if (!bIsSensitive)
		{
			return FALSE;
		}
		std::string str = "文件检测敏感，检测文件:[" + v_strFile + "]检测策略:[" + strStrategyName + "]\r\n";
		outPutFuc(str.c_str());
	}
	if (operateType == Operate_Single && CheckType == Check_Text_Dlg)
	{
		if (!bIsSensitive)
		{
			std::string str = "文本检测不敏感，检测文本:[" + v_strTextContext + "]检测策略:[" + strStrategyName + "]\r\n";
			outPutFuc(str.c_str());
			return FALSE;
		}
		std::string str = "文本检测敏感，检测文本:[" + v_strTextContext + "]检测策略:[" + strStrategyName + "]\r\n";
		outPutFuc(str.c_str());
		return TRUE;
	}

}

BOOL Mode_P_Dialog::dlp_Log(bool MatchTypeF, std::string v_strFile, IFCRResult* result_p, IFCRDripResult* drip_result_p)
{
	if (NULL != result_p)
	{
		result_p->moveFirstPart();
		if (MatchTypeF)
		{
 			string FileType = result_p->getFileExt();
			const char* strIds = result_p->GetAdvancedStrategyIdsFCR();
			const char* strFileProp = result_p->GetAdvancedFilePropFCR();
			dlplog_info(g_log_handle, "[%s] match file prop is: %s", __FUNCTION__, strFileProp);
			dlplog_info(g_log_handle, "[%s] match adv id is: %s", __FUNCTION__, strIds);
			if (!FileType.empty())
			{
				dlplog_info(g_log_handle, "[%s] match file type is: %s", __FUNCTION__, FileType.c_str());
			}
		}
		ICRPart* icrpaet = nullptr;
		while ((icrpaet = result_p->moveNextPart()) != NULL)
		{
			int i3 = icrpaet->getClassCode();
			string gbTextTrimed = icrpaet->getTextTrimed();
			string gbStrategyMatched = icrpaet->getStrategyMatched();
			unsigned int uiMatchType = icrpaet->getStrategyMatchedType();
			dlplog_info(g_log_handle, "[%s] 当前被检测文本: %s", __FUNCTION__, v_strFile.c_str());
			dlplog_info(g_log_handle, "[%s] gbStrategyMatched: %s", __FUNCTION__, gbStrategyMatched.c_str());
			dlplog_info(g_log_handle, "[%s] gbClassCode: %d", __FUNCTION__, i3);
			dlplog_info(g_log_handle, "[%s] gbTextTrimed: %s", __FUNCTION__, gbTextTrimed.c_str());
			dlplog_info(g_log_handle, "[%s] strategy Match Type is: %u", __FUNCTION__, uiMatchType);
			if (MatchTypeF)
			{
				string curFileType = icrpaet->getFileExt();
				string curSenFile = icrpaet->getSubFilePath();
				dlplog_info(g_log_handle, "[%s] current match file type is: %s", __FUNCTION__, curFileType.c_str());
				dlplog_info(g_log_handle, "[%s] current Sentive file type is: %s", __FUNCTION__, curSenFile.c_str());
			}
			dlplog_info(g_log_handle, "***********************************************************************************************");
		}

	}
	if (NULL != drip_result_p)
	{
		drip_result_p->moveFirstIncident();
		ICRIncident* incident = NULL;
		while ((incident = drip_result_p->moveNextIncident()) != NULL)
		{
			if (strlen(incident->getStrategyName()) <= 0)
			{
				return FALSE;
			}
			incident->moveFirstMatch();
			ICRMatch* match = NULL;
			dlplog_info(g_log_handle, ("零星检测"));
			string gbStrategyMatched = incident->getStrategyMatched();
			string gbStrategyName = incident->getStrategyName();
			int iClassCode = incident->getClassCode();
			dlplog_info(g_log_handle, "[%s] 文本名: %s", __FUNCTION__, v_strFile.c_str());
			dlplog_info(g_log_handle, "[%s] 匹配策略: %s", __FUNCTION__, gbStrategyMatched.c_str());
			dlplog_info(g_log_handle, "[%s] 匹配策略名称: %s", __FUNCTION__, gbStrategyName.c_str());
			dlplog_info(g_log_handle, "[%s] 分类码: %d", __FUNCTION__, iClassCode);
			while ((match = incident->moveNextMatch()) != NULL)
			{
				if (strlen(match->getRuleName()) <= 0)
				{
					return FALSE;
				}
				uint64 i3 = match->getRuleId();
				string RuleName = match->getRuleName();
				string FileName = match->getFilename();
				string FilePath = match->getFilePath();
				int iOPType = match->getOPType();
				time_t timestamp = match->getTimestamp();
				string gbTextTrimed = match->getTextTrimed();
				string gbFCRInfo = match->getFCRInfo();

				dlplog_info(g_log_handle, "[%s] 匹配规则ID: %llu 规则名称: %s", __FUNCTION__, i3, RuleName.c_str());
				dlplog_info(g_log_handle, "[%s] 匹配文件名: %s", __FUNCTION__, FileName.c_str());
				dlplog_info(g_log_handle, "[%s] 匹配文件路径: %s", __FUNCTION__, FilePath.c_str());
				dlplog_info(g_log_handle, "[%s] 匹配操作类型: %d", __FUNCTION__, iOPType);
				dlplog_info(g_log_handle, "[%s] 匹配时间戳: %ld", __FUNCTION__, timestamp);
				dlplog_info(g_log_handle, "[%s] 匹配文本片段: %s", __FUNCTION__, gbTextTrimed.c_str());
				dlplog_info(g_log_handle, "[%s] 匹配检测时传入的相关信息(json字符串): %s", __FUNCTION__, gbFCRInfo.c_str());
				dlplog_info(g_log_handle, "***********************************************************************************************");
			}
		}


	}

	return TRUE;
}

void Mode_P_Dialog::SetCheckType(int operateType, int checkType)
{
	this->operateType = operateType;
	this->CheckType = checkType;
}

void Mode_P_Dialog::OnBnClickedBtnThreadCheckTcrFolder()
{
	m_filename_listT.clear();
	std::string str = "文本检测目录开始\r\n";
	outPutFuc(str.c_str());

	m_file_count_sens = 0;

	int thread_max = m_thread_num;
	if (thread_max < 1 || thread_max > 48)
	{
		MessageBox(L"领导说了线程要开辟1到48");
		return;
	}
	dlplog_debug(g_log_handle, "threads TCR start , current threads init [%d]", thread_max);
	ThreadPool pool(thread_max);
	pool.init();

	WCHAR module_dir[MAX_PATH];
	::GetModuleFileNameW(NULL, module_dir, sizeof(module_dir) / sizeof(WCHAR));
	while ('\\' != module_dir[wcslen(module_dir) - 1])
	{
		module_dir[wcslen(module_dir) - 1] = '\0';
	}
	wcscat(module_dir, L"files.log");

	WCHAR log_dir[MAX_PATH];
	wcscpy(log_dir, module_dir);
	FILE* fp = _wfopen(log_dir, L"wt");
	if (NULL != fp)
	{
		_wsetlocale(0, L"chs");
	}

	this->UpdateData(TRUE);

	ergodicFolder(m_strFolderPath4Fcr.operator LPCTSTR(), m_filename_listT);

	// =======================================2023 11 7 多线程模式============================================

	auto startT = std::chrono::duration_cast<std::chrono::microseconds>(std::chrono::system_clock::now().time_since_epoch()).count();

	this->m_iFileCount = 0;
	int size = m_filename_listT.size() + 1;  //防止爆栈
	dlplog_debug(g_log_handle, "size is [%d]\n", size - 1);

	m_iCountFile = m_filename_listT.size();

	iniConfig->GetIniConfig(IniSetting::getInstance()); // settingini配置文件生效

	iniConfig->SetIniConfig(); // 敏感识别参数配置封装

	int thread_index = 0;

	std::vector<std::future<void>> m_vecFuture;

	SetCheckType(Operate_Dir, Check_Text);

	while (size != 0)
	{
		auto ret = pool.submit(std::bind(&Mode_P_Dialog::ldfcrTesterThreadTCR, this, thread_index));
		thread_index++;
		size--;
		m_vecFuture.push_back(std::move(ret));
	}

	if (NULL != fp)
	{
		fclose(fp);
	}

	for (auto& ret : m_vecFuture) {
		ret.get();
	}
	this->m_iSensFileCount = m_file_count_sens;
	pool.shutdown();
	auto endT = std::chrono::duration_cast<std::chrono::microseconds>(std::chrono::system_clock::now().time_since_epoch()).count();
	double tim = (double)(endT - startT) / 1000000;
	this->m_str_time_count.Format(_T("%.3f"), tim);
	this->UpdateData(FALSE);

	GetDlgItem(IDC_BTN_EXEC_TCR_FOLDER)->EnableWindow(TRUE);
	str = "文本检测目录结束\r\n";
	outPutFuc(str.c_str());
	std::string strThread;
	std::string strCheckFolder;
	switch (GetCheckType())
	{
	case Check_Common_P:
		strThread = "Check_thread_count";
		strCheckFolder = "Check_folder_path";
		break;
	case Check_Encrypt_P:
		strThread = "Encrypt_thread_count";
		strCheckFolder = "Encrypt_folder_path";
		break;
	case Check_Tag_P:
		strThread = "EncryptTag_thread_count";
		strCheckFolder = "EncryptTag_folder_path";
		break;
	case Check_G:
		strThread = "Global_thread_count";
		strCheckFolder = "Global_folder_path";
		break;
	default:
		break;
	}
	BOOL blR;
	// 记录当前配置的启用线程数
	blR = IniSetting::getInstance().recordDigit2INI("Thread", strThread.c_str(), m_thread_num);
	if (blR < 0)
	{
		dlplog_error(g_log_handle, "Setting ini record failed");
	}

	// 记录当前测试文件夹路径
	blR = IniSetting::getInstance().recordPATH2INI(strCheckFolder.c_str(), m_strFolderPath4Fcr);
	if (blR < 0)
	{
		dlplog_error(g_log_handle, "Setting ini record failed");
	}
}

void Mode_P_Dialog::OnBnClickedBtnExecTcrFolder()
{
	// TODO: 在此添加控件通知处理程序代码
	this->UpdateData(TRUE);
	std::thread threadTcr(std::bind(&Mode_P_Dialog::OnBnClickedBtnThreadCheckTcrFolder, this));
	GetDlgItem(IDC_BTN_EXEC_TCR_FOLDER)->EnableWindow(FALSE);
	threadTcr.detach();
}

std::string Mode_P_Dialog::GetIniPath()
{
	return this->m_IniPath;
}



BOOL Mode_P_Dialog::OnInitDialog()
{
	CDialogEx::OnInitDialog();
	// TODO:  在此添加额外的初始化
	v_idStrategy.clear();
	m_setFile.clear();
	strCheckType = "[CheckP] ";
	iniConfig = new(std::nothrow) ConfigLink();

	ModifyInterfaceValues();

	if (ldfcr_InitStartup())
	{
		ldfcr_CreateInstance((void**)&m_pCheckILDFcr, DlpTT_TerCheck); //24.5.17
		ldfcr_CreateInstance((void**)&m_pEncryptILDFcr, DlpTT_TerEncrypt); //25.6.10
		ldfcr_CreateInstance((void**)&m_pEncryptTagILDFcr, DlpTT_TerEncryptTag); //25.6.10
	}
	else
	{
		const char* str = ("文件内容识别组件初始化失败！");
		outPutFuc(str);
		return FALSE;
	}
	m_iIndex = 0;
	const char* str = ("初始化完成，默认启用常规检测指针，未加载策略\r\n");
	outPutFuc(str);
	strStrategyName.clear();

	// radio 
	m_radioSelect = 0;
	((CButton*)GetDlgItem(IDC_RADIO1))->SetCheck(TRUE);
	m_pILDFcr = m_pCheckILDFcr;
	m_iCheckType = Check_Common_P;
	m_setFile.insert(m_strFilePath4FCR);
	return TRUE;  // return TRUE unless you set the focus to a control
	// 异常: OCX 属性页应返回 FALSE
}

// 设置超时响应规则
void Mode_P_Dialog::OnBnClickedSetOutTime()
{
	// TODO: 在此添加控件通知处理程序代码
	this->UpdateData(TRUE);
	std::string strPut;
	char workDir[MAX_PATH];
	trcrt::app::tr_app_get_work_directory(workDir, MAX_PATH);
	std::string strJsonPath;
	switch (GetCheckType())
	{
	case Check_Common_P:
		strJsonPath = std::string(workDir) + "/TimeOutRule_CommonP.json";
		break;
	case Check_Encrypt_P:
		strJsonPath = std::string(workDir) + "/TimeOutRule_EncryptP.json";
		break;
	case Check_Tag_P:
		strJsonPath = std::string(workDir) + "/TimeOutRule_TagP.json";
		break;
	case Check_G:
		strJsonPath = std::string(workDir) + "/TimeOutRule_Global.json";
		break;
	default:
		break;
	}

	FILE* JsonFile = fopen(strJsonPath.c_str(), "rb");
	if (!JsonFile)
	{
		strPut = "json文件不存在，自动创建";
		outPutFuc(strPut.c_str());
		JsonFile = fopen(strJsonPath.c_str(), "wb");
		if (!JsonFile)
		{
			strPut = "无法创建超时响应规则文件";
			outPutFuc(strPut.c_str());
			return;
		}
		const char* strJsonContent = R"({
	  "strategy":[
		{
		  "id":1,
		  "severity":"1",
		  "name":"123123",
		  "classification":[
			{
			  "id":2,
			  "rulesExpr":"123123",
			  "rules":[
				{
				  "id":2,
				  "ruletype":224,
				  "name":"超时强制阻断",
				  "keyWords":[]
				}
			  ]
			}
		  ],
		  "respond":[
			{
			  "id":1000,
			  "alarmInfo":{
				"msgFormType":0,
				"msgFormPosition":1,
				"msgFormClose":5,
				"alarmLimit":2,
				"showSensContent":0
			  },
			  "stopOutgoing":1,
			  "handler":"",
			  "matchRespRule":1,
			  "takeScreenshot":0,
			  "sendMail":0,
			  "outSendApply":1,
			  "stopOutgoingEx":7,
			  "lossType":"8,9,26,25,2,3,4,14,15,16,11,1,18,5,17,7,23,24,13,0,20,27"
			}
		  ]
		}
	  ]
	})";
		fwrite(strJsonContent, sizeof(char), strlen(strJsonContent), JsonFile);
		fclose(JsonFile);
	}
	else {
		//规则文件存在
		fseek(JsonFile, 0, SEEK_END);
		size_t fileSize = ftell(JsonFile);
		fseek(JsonFile, 0, SEEK_SET);
		std::string strLogContent(fileSize, '\0');
		fread(&strLogContent[0], sizeof(char), fileSize, JsonFile);
		fclose(JsonFile);
		// 如果文件内容为空
		if (strLogContent.empty()) {
			strPut = "读取规则文件失败，文件为空！";
			outPutFuc(strPut.c_str());
			return;
		}
		// 检查 JSON 数据是否有效（ID 为 224）
		if (FindRespRuleId(strLogContent, 224)) {
			std::string strRuleName = strJsonPath;
			BOOL result;
			if (GetCheckType() != Check_G)
			{
				result = m_pILDFcr->AddInlineRespond(strLogContent.c_str(), strRuleName.c_str(), 224);
			}
			else
			{
				result = ldfcr_AddInlineRespond(strLogContent.c_str(), strRuleName.c_str(), 224);
			}
			if (result) {
				std::string str = "设置自定义响应规则成功:[" + strRuleName + "]";
				outPutFuc(str.c_str());
			}
			else {
				std::string str = "设置自定义响应规则失败:[" + strRuleName + "]";
				outPutFuc(str.c_str());
			}
		}
	}
	//CString cstrText;
	//m_strTimeOutRulesName.GetWindowTextW(cstrText);
	//// 记录当前edit控件值
	//BOOL blR = IniSetting::getInstance().recordCStr2INI("ResponseRuleName", "outTimeJsonName", cstrText);
	//if (blR < 0)
	//{
	//	dlplog_error(g_log_handle, "Setting ini record failed");
	//}
}

// 设置带密码响应规则
void Mode_P_Dialog::OnBnClickedSetWithPwd()
{
	// TODO: 在此添加控件通知处理程序代码
	this->UpdateData(TRUE);
	std::string strPut;
	char workDir[MAX_PATH];
	trcrt::app::tr_app_get_work_directory(workDir, MAX_PATH);
	std::string strJsonPath;
	switch (GetCheckType())
	{
	case Check_Common_P:
		strJsonPath = std::string(workDir) + "/PassWordRule_CommonP.json";
		break;
	case Check_Encrypt_P:
		strJsonPath = std::string(workDir) + "/PassWordRule_EncryptP.json";
		break;
	case Check_Tag_P:
		strJsonPath = std::string(workDir) + "/PassWordRule_TagP.json";
		break;
	case Check_G:
		strJsonPath = std::string(workDir) + "/PassWordRule_Global.json";
		break;
	default:
		break;
	}

	FILE* JsonFile = fopen(strJsonPath.c_str(), "rb");
	if (!JsonFile)
	{
		strPut = "json文件不存在，自动创建";
		outPutFuc(strPut.c_str());
		JsonFile = fopen(strJsonPath.c_str(), "wb");
		if (!JsonFile)
		{
			strPut = "无法创建带密码响应规则文件";
			outPutFuc(strPut.c_str());
			return;
		}
		const char* strJsonContent = R"({
		  "strategy":[
			{
			  "id":1,
			  "severity":"1",
			  "name":"123123",
			  "classification":[
				{
				  "id":2,
				  "rulesExpr":"123123",
				  "rules":[
					{
					  "id":2,
					  "ruletype":225,
					  "name":"带密码强制阻断",
					  "keyWords":[]
					}
				  ]
				}
			  ],
			  "respond":[
				{
				  "id":1000,
				  "alarmInfo":{
					"msgFormType":0,
					"msgFormPosition":1,
					"msgFormClose":5,
					"alarmLimit":2,
					"showSensContent":0
				  },
				  "stopOutgoing":1,
				  "handler":"",
				  "matchRespRule":1,
				  "takeScreenshot":0,
				  "sendMail":0,
				  "outSendApply":1,
				  "stopOutgoingEx":7,
				  "lossType":"8,9,26,25,2,3,4,14,15,16,11,1,18,5,17,7,23,24,13,0,20,27"
				}
			  ]
			}
		  ]
		})";
		fwrite(strJsonContent, sizeof(char), strlen(strJsonContent), JsonFile);
		fclose(JsonFile);
	}
	else {
		//规则文件存在
		fseek(JsonFile, 0, SEEK_END);
		size_t fileSize = ftell(JsonFile);
		fseek(JsonFile, 0, SEEK_SET);
		std::string strLogContent(fileSize, '\0');
		fread(&strLogContent[0], sizeof(char), fileSize, JsonFile);
		fclose(JsonFile);
		// 如果文件内容为空
		if (strLogContent.empty()) {
			strPut = "读取规则文件失败，文件为空！";
			outPutFuc(strPut.c_str());
			return;
		}
		// 检查 JSON 数据是否有效（ID 为 225）
		if (FindRespRuleId(strLogContent, 225)) {
			std::string strRuleName = strJsonPath;
			BOOL result;
			if (GetCheckType() != Check_G)
			{
				result = m_pILDFcr->AddInlineRespond(strLogContent.c_str(), strRuleName.c_str(), 225);
			}
			else
			{
				result = ldfcr_AddInlineRespond(strLogContent.c_str(), strRuleName.c_str(), 225);
			}
			if (result) {
				std::string str = "设置自定义响应规则成功:[" + strRuleName + "]\r\n";
				outPutFuc(str.c_str());
			}
			else {
				std::string str = "设置自定义响应规则失败:[" + strRuleName + "]\r\n";
				outPutFuc(str.c_str());
			}
		}
	}
	//CString cstrText;
	//m_strPassWordRuleName.GetWindowTextW(cstrText);
	//// 记录当前edit控件值
	//BOOL blR = IniSetting::getInstance().recordCStr2INI("ResponseRuleName", "passwordJsonName", cstrText);
	//if (blR < 0)
	//{
	//	dlplog_error(g_log_handle, "Setting ini record failed");
	//}
}

// 查找响应规则ruletype
BOOL Mode_P_Dialog::FindRespRuleId(const string& strLogContent, int ruleId)
{
	// 解析JSON内容
	rapidjson::Document document;
	document.Parse(strLogContent.c_str());

	// 查找指定ID的规则并进行自定义规则名
	if (document.HasMember("strategy") && document["strategy"].IsArray())
	{
		const rapidjson::Value& strategy = document["strategy"];
		for (rapidjson::SizeType i = 0; i < strategy.Size(); i++)
		{
			const rapidjson::Value& rule = strategy[i];
			if (rule.HasMember("classification") && rule["classification"].IsArray())
			{
				const rapidjson::Value& classificationArray = rule["classification"];
				for (rapidjson::SizeType j = 0; j < classificationArray.Size(); j++)
				{
					const rapidjson::Value& classification = classificationArray[j];
					if (classification.HasMember("rules") && classification["rules"].IsArray())
					{
						const rapidjson::Value& rulesArray = classification["rules"];
						for (rapidjson::SizeType k = 0; k < rulesArray.Size(); k++)
						{
							const rapidjson::Value& rule = rulesArray[k];
							if (rule.HasMember("ruletype") && rule["ruletype"].IsInt() && rule["ruletype"].GetInt() == ruleId)
							{
								return TRUE;
							}
							else
							{
								MessageBox(L"非对应响应规则Json！");
								return FALSE;
							}
						}
					}
				}
			}
		}
	}
	return TRUE;
}



void Mode_P_Dialog::OnBnClickedAdvButtonSet()
{ }
//{
//	// TODO: 在此添加控件通知处理程序代码
//	this->UpdateData(TRUE);
//
//	// 获取 setting 页面中设置的高级算法密钥
//	string strIniPath = GetIniPath();
//	SI_Error rc = ini.LoadFile(strIniPath.c_str());
//	const char* key = ini.GetValue("KEY", "NormalKey", NULL);
//	if (NULL == key)
//	{
//		std::string str = "设置失败，没有设置高级算法主密钥";
//		outPutFuc(str.c_str());
//		return;
//	}
//	if ((strlen(key) < 0) || (strlen(key) > 128))
//	{
//		std::string str = "设置失败，数值超过范围，清输入0~128内的主密钥加密串";
//		outPutFuc(str.c_str());
//		return;
//	}
//	//设置密钥，default是临时密钥加密串，Normal是主密钥加密串 
//	BOOL AdvInit;
//	if (GetCheckType() != Check_G)
//	{
//		AdvInit = m_pILDFcr->SetAdvInitParam(key, key);//231010
//	}
//	else
//	{
//		AdvInit = ldfcr_SetAdvInitParam(key);
//	}
//
//	if (AdvInit <= 0)
//	{
//		std::string str = key;
//		str = "设置高级算法秘钥失败:[" + str + "]\r\n";
//		outPutFuc(str.c_str());
//		return;
//	}
//	std::string str = key;
//	str = "设置高级算法秘钥成功:[" + str + "]\r\n";
//	outPutFuc(str.c_str());
//}

bool Mode_P_Dialog::outPutFuc(const char* utf)
{
	if (0 != strlen(utf))
	{
		std::string strRes = Utf8ToGbk(utf);
		std::string stdIndex = GetCurrentTie();
		stdIndex = stdIndex + " " + strCheckType + strRes;
		CString cstrRes = CA2T(stdIndex.c_str());
		m_edit.SetSel(-1);
		m_edit.ReplaceSel(cstrRes);
		return true;
	}
	return false;
}


void Mode_P_Dialog::OnBnClickedGetCpu()
{
	SYSTEM_INFO sysInfo;
	GetSystemInfo(&sysInfo);
	m_thread_num = sysInfo.dwNumberOfProcessors;
	this->UpdateData(FALSE);
}

void Mode_P_Dialog::OnBnClickedStopButton()
{
	// TODO: 在此添加控件通知处理程序代码
	std::string str;
	if (!iniConfig->Getfcrparm())
	{
		str = "fcrParam 初始化失败，终止失败\r\n";
		outPutFuc(str.c_str());
		return;
	}
	str = "检测终止成功\r\n";
	iniConfig->Getfcrparm()->interrupt = 1;
	outPutFuc(str.c_str());
}


void Mode_P_Dialog::OnBnClickedBtnOcrType()
{ }
//{
//	// TODO: 在此添加控件通知处理程序代码
//	// TODO: 在此添加控件通知处理程序代码
//	this->UpdateData(TRUE);
//
//	// 获取 setting 页面中设置的高级算法密钥
//	string strIniPath = GetIniPath();
//	SI_Error rc = ini.LoadFile(strIniPath.c_str());
//	const char* jsonTyoe = ini.GetValue("CONTENT", "jsonType", NULL);
//	if (NULL == jsonTyoe)
//	{
//		std::string str = "设置失败，没有设置OCR文本提取类型";
//		outPutFuc(str.c_str());
//		return;
//	}
//	if ((strlen(jsonTyoe) < 0) || (strlen(jsonTyoe) > 128))
//	{
//		std::string str = "设置失败，json有误，启用内置默认json";
//		outPutFuc(str.c_str());
//		return;
//	}
//	//设置密钥，default是临时密钥加密串，Normal是主密钥加密串 
//	BOOL bRes = m_pILDFcr->SetFilterGlobalParamer(jsonTyoe);//231010
//
//	if (bRes <= 0)
//	{
//		std::string str = jsonTyoe;
//		str = "设置OCR文本提取类型失败:[" + str + "]\r\n";
//		outPutFuc(str.c_str());
//		return;
//	}
//	std::string str = jsonTyoe;
//	str = "设置OCR文本提取类型成功:[" + str + "]\r\n";
//	outPutFuc(str.c_str());
//
//}


void Mode_P_Dialog::OnBnClickedBtn2Open()
{
	// TODO: 在此添加控件通知处理程序代码
	CString strInitialDir;
	m_CStrategyEdit.GetWindowText(strInitialDir); // 获取 IDC_STRATEGY_FILEPATH 的路径
	CString initialPath;

	if (!strInitialDir.IsEmpty())
	{
		// 编辑框中的路径不为空，将其作为初始路径
		initialPath = strInitialDir;
	}
	else
	{
		// 编辑框中的路径为空，将.exe文件所在的目录作为初始路径
		char exeDir[MAX_PATH];
		trcrt::app::tr_app_get_app_directory(exeDir, MAX_PATH);
		initialPath = CString(exeDir);
		strInitialDir = initialPath;//1024
	}

	static WCHAR T;
	//CString strFile = _T("R:\\");
	CFileDialog dlgFile(TRUE, _T("data"), NULL, OFN_HIDEREADONLY, _T("Data Files (*.*)|*.*||"), NULL);
	dlgFile.m_ofn.lpstrInitialDir = initialPath;//1023

	if (dlgFile.DoModal())
	{
		strInitialDir = dlgFile.GetPathName();
		m_cstrMergeCstrategy = strInitialDir;
	}

	if (strInitialDir.IsEmpty())
	{
		return;
	}

	this->UpdateData(FALSE);
	std::string strStrategyDrip;
	switch (GetCheckType())
	{
	case Check_Common_P:
		strStrategyDrip = "Check_strategy_drip";
		break;
	case Check_Encrypt_P:
		strStrategyDrip = "Encrypt_strategy_drip";
		break;
	case Check_Tag_P:
		strStrategyDrip = "EncryptTag_strategy_drip";
		break;
	case Check_G:
		break;
	default:
		break;
	}
	// 记录当前使用的策略路径
	BOOL blR = IniSetting::getInstance().recordPATH2INI(strStrategyDrip.c_str(), m_cstrMergeCstrategy);
	if (blR < 0)
	{
		dlplog_error(g_log_handle, "Setting ini record failed");
	}
}


void Mode_P_Dialog::OnBnClickedBtnView()
{
	this->UpdateData(TRUE);//更新策略
	CWnd* pWnd = GetDlgItem(IDC_BTN_VIEW);

	CString strMfc;

	// TODO: 在此添加控件通知处理程序代码
	CFile file(m_cstrMergeCstrategy, CFile::modeRead);

	int len = file.GetLength();//获取file文件中内容的长度；

	char* data = new char[len + 1];//定义一个存放数据的指针；

	memset(data, 0, len + 1);//   将已开辟内存空间 data的,长度为len+1首 个字节的值设为值 0

	file.Read(data, len);//读取文件内容并赋值给data;
	/*utf8转GBK*/
	string _str;
	_str = Utf8ToGbk(data);
	strMfc = _str.c_str();
	//创建非模态 注意内存泄漏，创建delete
	viewStgy* dlgView = new viewStgy(strMfc);
	BOOL bRet = dlgView->Create(IDD_DIALOG_VIEW);
	if (bRet)
	{
		dlgView->ShowWindow(SW_SHOW);
	}
}


void Mode_P_Dialog::OnBnClickedBtnClearPt()
{
	// TODO: 在此添加控件通知处理程序代码
	m_strStrategyFilePath.Empty();
	this->UpdateData(FALSE);
	std::string strStrategyCom;
	switch (GetCheckType())
	{
	case Check_Common_P:
		strStrategyCom = "Check_strategy_common";
		break;
	case Check_Encrypt_P:
		strStrategyCom = "Encrypt_strategy_common";
		break;
	case Check_Tag_P:
		strStrategyCom = "EncryptTag_strategy_common";
		break;
	case Check_G:
		strStrategyCom = "Global_strategy_common";
		break;
	default:
		break;
	}
	BOOL blR = IniSetting::getInstance().recordPATH2INI(strStrategyCom.c_str(), m_strStrategyFilePath);
	if (blR < 0)
	{
		dlplog_error(g_log_handle, "Setting ini record failed");
	}
}


void Mode_P_Dialog::OnBnClickedBrnClearLx()
{
	// TODO: 在此添加控件通知处理程序代码
	m_cstrMergeCstrategy.Empty();
	this->UpdateData(FALSE);
	std::string strStrategyDrip;
	switch (GetCheckType())
	{
	case Check_Common_P:
		strStrategyDrip = "Check_strategy_drip";
		break;
	case Check_Encrypt_P:
		strStrategyDrip = "Encrypt_strategy_drip";
		break;
	case Check_Tag_P:
		strStrategyDrip = "EncryptTag_strategy_drip";
		break;
	case Check_G:
		break;
	default:
		break;
	}
	BOOL blR = IniSetting::getInstance().recordPATH2INI(strStrategyDrip.c_str(), m_cstrMergeCstrategy);
	if (blR < 0)
	{
		dlplog_error(g_log_handle, "Setting ini record failed");
	}
}

void Mode_P_Dialog::OnBnClickedRadio1()
{
	// TODO: 在此添加控件通知处理程序代码
	this->UpdateData(TRUE);
	((CButton*)GetDlgItem(IDC_RADIO1))->SetCheck(TRUE);
	m_pILDFcr = m_pCheckILDFcr;
	m_iCheckType = Check_Common_P;
	strCheckType = "[CheckP] ";
	IniSetting::getInstance().switchSetting(Check_Common_P);
	ModifyInterfaceValues();
	std::string str = "切换到外发检测\r\n";
	outPutFuc(str.c_str());
}

void Mode_P_Dialog::OnBnClickedRadio2()
{
	// TODO: 在此添加控件通知处理程序代码
	this->UpdateData(TRUE);
	((CButton*)GetDlgItem(IDC_RADIO2))->SetCheck(TRUE);
	m_pILDFcr = m_pEncryptILDFcr;
	m_iCheckType = Check_Encrypt_P;
	strCheckType = "[Encrypt] ";
	IniSetting::getInstance().switchSetting(Check_Encrypt_P);
	ModifyInterfaceValues();
	std::string str = "切换到智能加密\r\n";
	outPutFuc(str.c_str());
}

void Mode_P_Dialog::OnBnClickedRadio4()
{
	// TODO: 在此添加控件通知处理程序代码
	this->UpdateData(TRUE);
	((CButton*)GetDlgItem(IDC_RADIO4))->SetCheck(TRUE);
	m_pILDFcr = m_pEncryptTagILDFcr;
	m_iCheckType = Check_Tag_P;
	strCheckType = "[EncryptTag] ";
	IniSetting::getInstance().switchSetting(Check_Tag_P);
	ModifyInterfaceValues();
	std::string str = "切换到加密+加标签\r\n";
	outPutFuc(str.c_str());
}

void Mode_P_Dialog::OnBnClickedRadio3()
{
	// TODO: 在此添加控件通知处理程序代码
	this->UpdateData(TRUE);
	((CButton*)GetDlgItem(IDC_RADIO3))->SetCheck(TRUE);
	m_iCheckType = Check_G;
	strCheckType = "[Global] ";
	IniSetting::getInstance().switchSetting(Check_G);
	ModifyInterfaceValues();
	std::string str = "切换到全盘检测\r\n";
	outPutFuc(str.c_str());
}

int Mode_P_Dialog::GetCheckType()
{
	return m_iCheckType;
}


void Mode_P_Dialog::OnEnChangeEdit14()
{
	// TODO:  如果该控件是 RICHEDIT 控件，它将不
	// 发送此通知，除非重写 CDialogEx::OnInitDialog()
	// 函数并调用 CRichEditCtrl().SetEventMask()，
	// 同时将 ENM_CHANGE 标志“或”运算到掩码中。

	// TODO:  在此添加控件通知处理程序代码
	this->UpdateData(TRUE);
	m_setFile.insert(m_strFilePath4FCR);
	m_combox_detect_file.ResetContent();
	for (auto it = m_setFile.begin(); it != m_setFile.end(); ++it)
	{
		m_combox_detect_file.AddString(*it);
	}
	m_combox_detect_file.SetCurSel(0);
}


void Mode_P_Dialog::OnBnClickedBtnComFcr()
{
	// TODO: 在此添加控件通知处理程序代码
	this->UpdateData(TRUE);
	iniConfig->GetIniConfig(IniSetting::getInstance()); // settingini配置文件生效
	checkCount = iniConfig->GetCheckCount();
	if (0 >= this->checkCount)
	{
		return;
	}
	iniConfig->SetIniConfig(); // 敏感识别参数配置封装

	m_pILDFcr->SetFilterGlobalParamer(iniConfig->GetPassWordJson().c_str()); //2023 Q1
	CString cstrFilePath;
	GetDlgItem(IDC_COMBO3)->GetWindowTextW(cstrFilePath);
	wstring strCheckFile = cstrFilePath.GetBuffer();

	// 封装策略选取部分
	StrategySelect();

	SetCheckType(Operate_Single, Check_File);

	std::thread threadCheck(std::bind(&Mode_P_Dialog::dlp_checkSingleFile, this, strCheckFile.c_str()));
	threadCheck.detach();
}


void Mode_P_Dialog::OnBnClickedBtnComTcr()
{
	// TODO: 在此添加控件通知处理程序代码
	this->UpdateData(TRUE);
	iniConfig->GetIniConfig(IniSetting::getInstance()); // settingini配置文件生效
	checkCount = iniConfig->GetCheckCount();
	//init moudle
	if (0 >= this->checkCount)
	{
		return;
	}

	iniConfig->SetIniConfig(); // 敏感识别参数配置封装

	m_pILDFcr->SetFilterGlobalParamer(iniConfig->GetPassWordJson().c_str()); //2023 Q1

	CString cstrFilePath;
	GetDlgItem(IDC_COMBO3)->GetWindowTextW(cstrFilePath);
	wstring strCheckFile = cstrFilePath.GetBuffer();
	// 封装策略选取部分
	StrategySelect();

	SetCheckType(Operate_Single, Check_Text);

	std::thread threadCheck(std::bind(&Mode_P_Dialog::dlp_checkSingleText, this, strCheckFile.c_str()));
	threadCheck.detach();
}


void Mode_P_Dialog::OnBnClickedGetLeakStyle()
{
	// TODO: 在此添加控件通知处理程序代码
}


void Mode_P_Dialog::OnBnClickedGetError()
{
	// TODO: 在此添加控件通知处理程序代码
}


void Mode_P_Dialog::OnBnClickedBtnStategySelect()
{
	// TODO: 在此添加控件通知处理程序代码
	if (idNameMap.empty())
	{
		std::string str = "策略没加载\r\n";
		outPutFuc(str.c_str());
		return;
	}
	StrategyDlg dlg;
	dlg.OnInitDlg(idNameMap);
	if (IDOK == dlg.DoModal())
	{
		v_idStrategy = dlg.GetStrategyBeChosed();
	}
	updateStrategyComBox();
	return;
}

void Mode_P_Dialog::updateStrategyComBox()
{
	this->UpdateData(TRUE);
	//清空下拉框
	m_ComboSelectIdName.ResetContent();
	if (v_idStrategy.empty())
	{
		CString idNameString = _T("all stategy");
		std::string str = "all stategy";
		int indexIdName = m_ComboSelectIdName.AddString(idNameString);
		m_ComboSelectIdName.SetItemData(indexIdName, 0);
		m_ComboSelectIdName.SetCurSel(0);
	}

	//将策略ID和NAME添加到下拉框
	for (const auto& it : v_idStrategy)
	{
		int indexIdName = m_ComboSelectIdName.AddString(it);
	}

	//设置默认第一个选项
	m_ComboSelectIdName.SetCurSel(0);
}

void Mode_P_Dialog::StrategySelect()
{
	int count = m_ComboSelectIdName.GetCount();
	CString cstrIdName;
	std::vector<std::string> m_vec;
	if(count == 0)
	{
		std::string str = "策略没加载\r\n";
		outPutFuc(str.c_str());
		return;
	}
	for (int i = 0; i < count; ++i)
	{
		m_ComboSelectIdName.GetLBText(i, cstrIdName);
		m_vec.push_back(wstringToString(cstrIdName.GetString()));
	}
	iniConfig->Getfcrparm()->strategyIdsCount = count;  //设置检测策略数量
	std::string strNpos;
	for (int i = 0; i < m_vec.size(); ++i)
	{
		size_t pos = m_vec[i].find_first_of(".");
		if (pos != std::string::npos)
		{
			strNpos = m_vec[i].substr(0, pos);
			uint64 num = std::stoull(strNpos);
			(iniConfig->Getfcrparm())->strategyIdsArray[i] = num;
		}
		else
		{
			if (count == 1)
			{
				iniConfig->Getfcrparm()->strategyIdsCount = 0;  //设置检测策略数量
				(iniConfig->Getfcrparm())->strategyIdsArray = 0;
			}
		}
	}
}


void Mode_P_Dialog::OnBnClickedBtnFileprop()
{
	// TODO: 在此添加控件通知处理程序代码
	UpdateData(TRUE);
	CString cstrEdit;
	std::string str;
	outPutFuc(str.c_str());
	m_cstrFileProp_Edit.GetWindowTextW(cstrEdit);
	if (cstrEdit.IsEmpty())
	{
		str = "填选文件属性内容为空";
		outPutFuc(str.c_str());
		return;
	}
	string strC = wstringToString(cstrEdit.GetString());
	iniConfig->Getfcrparm()->fcrInfo = strC.c_str();
	str = "fileProp 加载成功，文本为:[" + strC + "]";
	outPutFuc(str.c_str());
}
