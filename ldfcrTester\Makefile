#############################################################################
# Makefile for building: ldfcrTester
# Generated by qmake (3.1) (Qt 5.13.0)
# Project:  ldfcrTester.pro
# Template: app
# Command: /home/<USER>/Qt5.13.0/5.13.0/gcc_64/bin/qmake -o Makefile ldfcrTester.pro -spec linux-g++ CONFIG+=debug CONFIG+=qml_debug
#############################################################################

MAKEFILE      = Makefile

EQ            = =

####### Compiler, tools and options

CC            = gcc
CXX           = g++
DEFINES       = -DQT_DEPRECATED_WARNINGS -DQT_QML_DEBUG -DQT_WIDGETS_LIB -DQT_GUI_LIB -DQT_CORE_LIB
CFLAGS        = -pipe -g -Wall -W -D_REENTRANT -fPIC $(DEFINES)
CXXFLAGS      = -pipe -g -std=gnu++11 -Wall -W -D_REENTRANT -fPIC $(DEFINES)
INCPATH       = -I. -I/home/<USER>/build-ldfcrTester-Desktop_Qt_5_13_0_GCC_64bit-Release/libs_x64 -I/home/<USER>/build-ldfcrTester-Desktop_Qt_5_13_0_GCC_64bit-Release/libs_x64 -I/home/<USER>/build-ldfcrTester-Desktop_Qt_5_13_0_GCC_64bit-Release/libs_x64 -I/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include -I/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtWidgets -I/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtGui -I/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore -I. -isystem /usr/include/libdrm -I. -I/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/linux-g++
QMAKE         = /home/<USER>/Qt5.13.0/5.13.0/gcc_64/bin/qmake
DEL_FILE      = rm -f
CHK_DIR_EXISTS= test -d
MKDIR         = mkdir -p
COPY          = cp -f
COPY_FILE     = cp -f
COPY_DIR      = cp -f -R
INSTALL_FILE  = install -m 644 -p
INSTALL_PROGRAM = install -m 755 -p
INSTALL_DIR   = cp -f -R
QINSTALL      = /home/<USER>/Qt5.13.0/5.13.0/gcc_64/bin/qmake -install qinstall
QINSTALL_PROGRAM = /home/<USER>/Qt5.13.0/5.13.0/gcc_64/bin/qmake -install qinstall -exe
DEL_FILE      = rm -f
SYMLINK       = ln -f -s
DEL_DIR       = rmdir
MOVE          = mv -f
TAR           = tar -cf
COMPRESS      = gzip -9f
DISTNAME      = ldfcrTester1.0.0
DISTDIR = /home/<USER>/Desktop/ldfcr/DlpPolicyModule_new/DlpPolicyModule/ldfcrTester/.tmp/ldfcrTester1.0.0
LINK          = g++
LFLAGS        = -Wl,-rpath=/home/<USER>/build-ldfcrTester-Desktop_Qt_5_13_0_GCC_64bit-Release/libs_x64 -Wl,-Bsymbolic -Wl,-rpath,/home/<USER>/Qt5.13.0/5.13.0/gcc_64/lib
LIBS          = $(SUBLIBS) -L/home/<USER>/Desktop/ldfcr/DlpPolicyModule_new/DlpPolicyModule/ldfcrTester/../../../../../build-ldfcrTester-Desktop_Qt_5_13_0_GCC_64bit-Release/libs_x64/ -ltrcrt -lldfcr -lDlpULog /home/<USER>/Qt5.13.0/5.13.0/gcc_64/lib/libQt5Widgets.so /home/<USER>/Qt5.13.0/5.13.0/gcc_64/lib/libQt5Gui.so /home/<USER>/Qt5.13.0/5.13.0/gcc_64/lib/libQt5Core.so -lGL -lpthread   
AR            = ar cqs
RANLIB        = 
SED           = sed
STRIP         = strip

####### Output directory

OBJECTS_DIR   = ./

####### Files

SOURCES       = DirNode.cpp \
		main.cpp \
		mainwindow.cpp \
		slots.cpp moc_mainwindow.cpp
OBJECTS       = DirNode.o \
		main.o \
		mainwindow.o \
		slots.o \
		moc_mainwindow.o
DIST          = /home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/features/spec_pre.prf \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/common/unix.conf \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/common/linux.conf \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/common/sanitize.conf \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/common/gcc-base.conf \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/common/gcc-base-unix.conf \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/common/g++-base.conf \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/common/g++-unix.conf \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/qconfig.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_3danimation.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_3danimation_private.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_3dcore.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_3dcore_private.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_3dextras.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_3dextras_private.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_3dinput.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_3dinput_private.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_3dlogic.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_3dlogic_private.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_3dquick.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_3dquick_private.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_3dquickanimation.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_3dquickanimation_private.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_3dquickextras.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_3dquickextras_private.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_3dquickinput.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_3dquickinput_private.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_3dquickrender.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_3dquickrender_private.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_3dquickscene2d.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_3dquickscene2d_private.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_3drender.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_3drender_private.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_accessibility_support_private.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_bluetooth.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_bluetooth_private.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_bootstrap_private.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_concurrent.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_concurrent_private.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_core.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_core_private.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_dbus.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_dbus_private.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_designer.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_designer_private.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_designercomponents_private.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_devicediscovery_support_private.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_edid_support_private.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_egl_support_private.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_eglfs_kms_support_private.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_eglfsdeviceintegration_private.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_eventdispatcher_support_private.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_fb_support_private.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_fontdatabase_support_private.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_gamepad.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_gamepad_private.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_glx_support_private.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_gui.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_gui_private.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_help.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_help_private.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_input_support_private.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_kms_support_private.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_linuxaccessibility_support_private.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_location.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_location_private.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_multimedia.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_multimedia_private.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_multimediagsttools_private.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_multimediawidgets.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_multimediawidgets_private.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_network.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_network_private.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_nfc.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_nfc_private.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_opengl.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_opengl_private.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_openglextensions.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_openglextensions_private.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_packetprotocol_private.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_platformcompositor_support_private.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_positioning.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_positioning_private.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_positioningquick.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_positioningquick_private.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_printsupport.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_printsupport_private.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_qml.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_qml_private.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_qmldebug_private.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_qmldevtools_private.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_qmltest.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_qmltest_private.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_qtmultimediaquicktools_private.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_quick.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_quick_private.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_quickcontrols2.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_quickcontrols2_private.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_quickparticles_private.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_quickshapes_private.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_quicktemplates2.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_quicktemplates2_private.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_quickwidgets.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_quickwidgets_private.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_remoteobjects.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_remoteobjects_private.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_repparser.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_repparser_private.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_scxml.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_scxml_private.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_sensors.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_sensors_private.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_serialbus.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_serialbus_private.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_serialport.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_serialport_private.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_service_support_private.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_sql.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_sql_private.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_svg.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_svg_private.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_testlib.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_testlib_private.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_texttospeech.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_texttospeech_private.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_theme_support_private.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_uiplugin.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_uitools.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_uitools_private.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_vulkan_support_private.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_waylandclient.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_waylandclient_private.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_waylandcompositor.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_waylandcompositor_private.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_webchannel.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_webchannel_private.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_websockets.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_websockets_private.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_webview.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_webview_private.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_widgets.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_widgets_private.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_x11extras.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_x11extras_private.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_xcb_qpa_lib_private.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_xkbcommon_support_private.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_xml.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_xml_private.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_xmlpatterns.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_xmlpatterns_private.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/features/qt_functions.prf \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/features/qt_config.prf \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/linux-g++/qmake.conf \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/features/spec_post.prf \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/features/exclusive_builds.prf \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/features/toolchain.prf \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/features/default_pre.prf \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/features/resolve_config.prf \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/features/default_post.prf \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/features/qml_debug.prf \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/features/warn_on.prf \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/features/qt.prf \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/features/resources.prf \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/features/moc.prf \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/features/unix/opengl.prf \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/features/uic.prf \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/features/unix/thread.prf \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/features/qmake_use.prf \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/features/file_copies.prf \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/features/testcase_targets.prf \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/features/exceptions.prf \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/features/yacc.prf \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/features/lex.prf \
		ldfcrTester.pro DirNode.h \
		mainwindow.h DirNode.cpp \
		main.cpp \
		mainwindow.cpp \
		slots.cpp
QMAKE_TARGET  = ldfcrTester
DESTDIR       = 
TARGET        = ldfcrTester


first: all
####### Build rules

ldfcrTester: ui_mainwindow.h $(OBJECTS)  
	$(LINK) $(LFLAGS) -o $(TARGET) $(OBJECTS) $(OBJCOMP) $(LIBS)

Makefile: ldfcrTester.pro /home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/linux-g++/qmake.conf /home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/features/spec_pre.prf \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/common/unix.conf \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/common/linux.conf \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/common/sanitize.conf \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/common/gcc-base.conf \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/common/gcc-base-unix.conf \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/common/g++-base.conf \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/common/g++-unix.conf \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/qconfig.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_3danimation.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_3danimation_private.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_3dcore.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_3dcore_private.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_3dextras.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_3dextras_private.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_3dinput.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_3dinput_private.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_3dlogic.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_3dlogic_private.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_3dquick.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_3dquick_private.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_3dquickanimation.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_3dquickanimation_private.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_3dquickextras.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_3dquickextras_private.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_3dquickinput.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_3dquickinput_private.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_3dquickrender.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_3dquickrender_private.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_3dquickscene2d.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_3dquickscene2d_private.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_3drender.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_3drender_private.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_accessibility_support_private.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_bluetooth.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_bluetooth_private.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_bootstrap_private.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_concurrent.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_concurrent_private.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_core.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_core_private.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_dbus.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_dbus_private.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_designer.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_designer_private.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_designercomponents_private.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_devicediscovery_support_private.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_edid_support_private.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_egl_support_private.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_eglfs_kms_support_private.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_eglfsdeviceintegration_private.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_eventdispatcher_support_private.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_fb_support_private.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_fontdatabase_support_private.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_gamepad.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_gamepad_private.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_glx_support_private.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_gui.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_gui_private.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_help.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_help_private.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_input_support_private.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_kms_support_private.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_linuxaccessibility_support_private.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_location.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_location_private.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_multimedia.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_multimedia_private.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_multimediagsttools_private.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_multimediawidgets.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_multimediawidgets_private.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_network.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_network_private.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_nfc.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_nfc_private.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_opengl.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_opengl_private.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_openglextensions.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_openglextensions_private.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_packetprotocol_private.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_platformcompositor_support_private.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_positioning.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_positioning_private.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_positioningquick.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_positioningquick_private.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_printsupport.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_printsupport_private.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_qml.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_qml_private.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_qmldebug_private.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_qmldevtools_private.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_qmltest.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_qmltest_private.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_qtmultimediaquicktools_private.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_quick.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_quick_private.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_quickcontrols2.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_quickcontrols2_private.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_quickparticles_private.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_quickshapes_private.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_quicktemplates2.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_quicktemplates2_private.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_quickwidgets.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_quickwidgets_private.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_remoteobjects.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_remoteobjects_private.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_repparser.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_repparser_private.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_scxml.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_scxml_private.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_sensors.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_sensors_private.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_serialbus.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_serialbus_private.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_serialport.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_serialport_private.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_service_support_private.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_sql.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_sql_private.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_svg.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_svg_private.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_testlib.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_testlib_private.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_texttospeech.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_texttospeech_private.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_theme_support_private.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_uiplugin.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_uitools.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_uitools_private.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_vulkan_support_private.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_waylandclient.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_waylandclient_private.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_waylandcompositor.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_waylandcompositor_private.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_webchannel.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_webchannel_private.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_websockets.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_websockets_private.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_webview.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_webview_private.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_widgets.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_widgets_private.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_x11extras.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_x11extras_private.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_xcb_qpa_lib_private.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_xkbcommon_support_private.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_xml.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_xml_private.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_xmlpatterns.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_xmlpatterns_private.pri \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/features/qt_functions.prf \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/features/qt_config.prf \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/linux-g++/qmake.conf \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/features/spec_post.prf \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/features/exclusive_builds.prf \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/features/toolchain.prf \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/features/default_pre.prf \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/features/resolve_config.prf \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/features/default_post.prf \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/features/qml_debug.prf \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/features/warn_on.prf \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/features/qt.prf \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/features/resources.prf \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/features/moc.prf \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/features/unix/opengl.prf \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/features/uic.prf \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/features/unix/thread.prf \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/features/qmake_use.prf \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/features/file_copies.prf \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/features/testcase_targets.prf \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/features/exceptions.prf \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/features/yacc.prf \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/features/lex.prf \
		ldfcrTester.pro
	$(QMAKE) -o Makefile ldfcrTester.pro -spec linux-g++ CONFIG+=debug CONFIG+=qml_debug
/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/features/spec_pre.prf:
/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/common/unix.conf:
/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/common/linux.conf:
/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/common/sanitize.conf:
/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/common/gcc-base.conf:
/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/common/gcc-base-unix.conf:
/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/common/g++-base.conf:
/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/common/g++-unix.conf:
/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/qconfig.pri:
/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_3danimation.pri:
/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_3danimation_private.pri:
/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_3dcore.pri:
/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_3dcore_private.pri:
/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_3dextras.pri:
/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_3dextras_private.pri:
/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_3dinput.pri:
/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_3dinput_private.pri:
/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_3dlogic.pri:
/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_3dlogic_private.pri:
/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_3dquick.pri:
/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_3dquick_private.pri:
/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_3dquickanimation.pri:
/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_3dquickanimation_private.pri:
/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_3dquickextras.pri:
/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_3dquickextras_private.pri:
/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_3dquickinput.pri:
/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_3dquickinput_private.pri:
/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_3dquickrender.pri:
/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_3dquickrender_private.pri:
/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_3dquickscene2d.pri:
/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_3dquickscene2d_private.pri:
/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_3drender.pri:
/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_3drender_private.pri:
/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_accessibility_support_private.pri:
/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_bluetooth.pri:
/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_bluetooth_private.pri:
/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_bootstrap_private.pri:
/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_concurrent.pri:
/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_concurrent_private.pri:
/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_core.pri:
/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_core_private.pri:
/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_dbus.pri:
/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_dbus_private.pri:
/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_designer.pri:
/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_designer_private.pri:
/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_designercomponents_private.pri:
/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_devicediscovery_support_private.pri:
/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_edid_support_private.pri:
/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_egl_support_private.pri:
/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_eglfs_kms_support_private.pri:
/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_eglfsdeviceintegration_private.pri:
/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_eventdispatcher_support_private.pri:
/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_fb_support_private.pri:
/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_fontdatabase_support_private.pri:
/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_gamepad.pri:
/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_gamepad_private.pri:
/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_glx_support_private.pri:
/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_gui.pri:
/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_gui_private.pri:
/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_help.pri:
/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_help_private.pri:
/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_input_support_private.pri:
/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_kms_support_private.pri:
/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_linuxaccessibility_support_private.pri:
/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_location.pri:
/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_location_private.pri:
/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_multimedia.pri:
/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_multimedia_private.pri:
/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_multimediagsttools_private.pri:
/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_multimediawidgets.pri:
/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_multimediawidgets_private.pri:
/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_network.pri:
/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_network_private.pri:
/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_nfc.pri:
/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_nfc_private.pri:
/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_opengl.pri:
/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_opengl_private.pri:
/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_openglextensions.pri:
/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_openglextensions_private.pri:
/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_packetprotocol_private.pri:
/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_platformcompositor_support_private.pri:
/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_positioning.pri:
/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_positioning_private.pri:
/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_positioningquick.pri:
/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_positioningquick_private.pri:
/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_printsupport.pri:
/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_printsupport_private.pri:
/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_qml.pri:
/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_qml_private.pri:
/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_qmldebug_private.pri:
/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_qmldevtools_private.pri:
/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_qmltest.pri:
/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_qmltest_private.pri:
/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_qtmultimediaquicktools_private.pri:
/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_quick.pri:
/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_quick_private.pri:
/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_quickcontrols2.pri:
/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_quickcontrols2_private.pri:
/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_quickparticles_private.pri:
/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_quickshapes_private.pri:
/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_quicktemplates2.pri:
/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_quicktemplates2_private.pri:
/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_quickwidgets.pri:
/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_quickwidgets_private.pri:
/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_remoteobjects.pri:
/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_remoteobjects_private.pri:
/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_repparser.pri:
/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_repparser_private.pri:
/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_scxml.pri:
/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_scxml_private.pri:
/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_sensors.pri:
/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_sensors_private.pri:
/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_serialbus.pri:
/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_serialbus_private.pri:
/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_serialport.pri:
/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_serialport_private.pri:
/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_service_support_private.pri:
/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_sql.pri:
/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_sql_private.pri:
/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_svg.pri:
/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_svg_private.pri:
/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_testlib.pri:
/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_testlib_private.pri:
/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_texttospeech.pri:
/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_texttospeech_private.pri:
/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_theme_support_private.pri:
/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_uiplugin.pri:
/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_uitools.pri:
/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_uitools_private.pri:
/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_vulkan_support_private.pri:
/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_waylandclient.pri:
/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_waylandclient_private.pri:
/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_waylandcompositor.pri:
/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_waylandcompositor_private.pri:
/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_webchannel.pri:
/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_webchannel_private.pri:
/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_websockets.pri:
/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_websockets_private.pri:
/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_webview.pri:
/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_webview_private.pri:
/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_widgets.pri:
/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_widgets_private.pri:
/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_x11extras.pri:
/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_x11extras_private.pri:
/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_xcb_qpa_lib_private.pri:
/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_xkbcommon_support_private.pri:
/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_xml.pri:
/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_xml_private.pri:
/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_xmlpatterns.pri:
/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/modules/qt_lib_xmlpatterns_private.pri:
/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/features/qt_functions.prf:
/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/features/qt_config.prf:
/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/linux-g++/qmake.conf:
/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/features/spec_post.prf:
/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/features/exclusive_builds.prf:
/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/features/toolchain.prf:
/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/features/default_pre.prf:
/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/features/resolve_config.prf:
/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/features/default_post.prf:
/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/features/qml_debug.prf:
/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/features/warn_on.prf:
/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/features/qt.prf:
/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/features/resources.prf:
/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/features/moc.prf:
/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/features/unix/opengl.prf:
/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/features/uic.prf:
/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/features/unix/thread.prf:
/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/features/qmake_use.prf:
/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/features/file_copies.prf:
/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/features/testcase_targets.prf:
/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/features/exceptions.prf:
/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/features/yacc.prf:
/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/features/lex.prf:
ldfcrTester.pro:
qmake: FORCE
	@$(QMAKE) -o Makefile ldfcrTester.pro -spec linux-g++ CONFIG+=debug CONFIG+=qml_debug

qmake_all: FORCE


all: Makefile ldfcrTester

dist: distdir FORCE
	(cd `dirname $(DISTDIR)` && $(TAR) $(DISTNAME).tar $(DISTNAME) && $(COMPRESS) $(DISTNAME).tar) && $(MOVE) `dirname $(DISTDIR)`/$(DISTNAME).tar.gz . && $(DEL_FILE) -r $(DISTDIR)

distdir: FORCE
	@test -d $(DISTDIR) || mkdir -p $(DISTDIR)
	$(COPY_FILE) --parents $(DIST) $(DISTDIR)/
	$(COPY_FILE) --parents /home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/features/data/dummy.cpp $(DISTDIR)/
	$(COPY_FILE) --parents DirNode.h mainwindow.h $(DISTDIR)/
	$(COPY_FILE) --parents DirNode.cpp main.cpp mainwindow.cpp slots.cpp $(DISTDIR)/
	$(COPY_FILE) --parents mainwindow.ui $(DISTDIR)/


clean: compiler_clean 
	-$(DEL_FILE) $(OBJECTS)
	-$(DEL_FILE) *~ core *.core


distclean: clean 
	-$(DEL_FILE) $(TARGET) 
	-$(DEL_FILE) .qmake.stash
	-$(DEL_FILE) Makefile


####### Sub-libraries

mocclean: compiler_moc_header_clean compiler_moc_objc_header_clean compiler_moc_source_clean

mocables: compiler_moc_header_make_all compiler_moc_objc_header_make_all compiler_moc_source_make_all

check: first

benchmark: first

compiler_rcc_make_all:
compiler_rcc_clean:
compiler_moc_predefs_make_all: moc_predefs.h
compiler_moc_predefs_clean:
	-$(DEL_FILE) moc_predefs.h
moc_predefs.h: /home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/features/data/dummy.cpp
	g++ -pipe -g -std=gnu++11 -Wall -W -dM -E -o moc_predefs.h /home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/features/data/dummy.cpp

compiler_moc_header_make_all: moc_mainwindow.cpp
compiler_moc_header_clean:
	-$(DEL_FILE) moc_mainwindow.cpp
moc_mainwindow.cpp: mainwindow.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtWidgets/QMainWindow \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtWidgets/qmainwindow.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtWidgets/qtwidgetsglobal.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtGui/qtguiglobal.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qglobal.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qconfig-bootstrapped.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qconfig.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qtcore-config.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qsystemdetection.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qprocessordetection.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qcompilerdetection.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qtypeinfo.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qsysinfo.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qlogging.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qflags.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qatomic.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qbasicatomic.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qatomic_bootstrap.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qgenericatomic.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qatomic_cxx11.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qatomic_msvc.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qglobalstatic.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qmutex.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qnumeric.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qversiontagging.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtGui/qtgui-config.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtWidgets/qtwidgets-config.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtWidgets/qwidget.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtGui/qwindowdefs.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qobjectdefs.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qnamespace.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qobjectdefs_impl.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtGui/qwindowdefs_win.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qobject.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qstring.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qchar.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qbytearray.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qrefcount.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qarraydata.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qstringliteral.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qstringalgorithms.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qstringview.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qstringbuilder.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qlist.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qalgorithms.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qiterator.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qhashfunctions.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qpair.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qvector.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qpoint.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qbytearraylist.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qstringlist.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qregexp.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qstringmatcher.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qcoreevent.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qscopedpointer.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qmetatype.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qvarlengtharray.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qcontainerfwd.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qobject_impl.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qmargins.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtGui/qpaintdevice.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qrect.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qsize.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtGui/qpalette.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtGui/qcolor.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtGui/qrgb.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtGui/qrgba64.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtGui/qbrush.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtGui/qmatrix.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtGui/qpolygon.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtGui/qregion.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qdatastream.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qiodevice.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qline.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtGui/qtransform.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtGui/qpainterpath.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtGui/qimage.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtGui/qpixelformat.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtGui/qpixmap.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qsharedpointer.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qshareddata.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qhash.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qsharedpointer_impl.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtGui/qfont.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtGui/qfontmetrics.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtGui/qfontinfo.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtWidgets/qsizepolicy.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtGui/qcursor.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtGui/qkeysequence.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtGui/qevent.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qvariant.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qmap.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qdebug.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qtextstream.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qlocale.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qset.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qcontiguouscache.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qurl.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qurlquery.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qfile.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qfiledevice.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtGui/qvector2d.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtGui/qtouchdevice.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtWidgets/qtabwidget.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtGui/qicon.h \
		../ldfcr/ldfcr.h \
		../Pub/OS/4linux/winapi4linux.h \
		moc_predefs.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/bin/moc
	/home/<USER>/Qt5.13.0/5.13.0/gcc_64/bin/moc $(DEFINES) --include /home/<USER>/Desktop/ldfcr/DlpPolicyModule_new/DlpPolicyModule/ldfcrTester/moc_predefs.h -I/home/<USER>/Qt5.13.0/5.13.0/gcc_64/mkspecs/linux-g++ -I/home/<USER>/Desktop/ldfcr/DlpPolicyModule_new/DlpPolicyModule/ldfcrTester -I/home/<USER>/build-ldfcrTester-Desktop_Qt_5_13_0_GCC_64bit-Release/libs_x64 -I/home/<USER>/build-ldfcrTester-Desktop_Qt_5_13_0_GCC_64bit-Release/libs_x64 -I/home/<USER>/build-ldfcrTester-Desktop_Qt_5_13_0_GCC_64bit-Release/libs_x64 -I/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include -I/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtWidgets -I/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtGui -I/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore -I/usr/include/c++/5 -I/usr/include/x86_64-linux-gnu/c++/5 -I/usr/include/c++/5/backward -I/usr/lib/gcc/x86_64-linux-gnu/5/include -I/usr/local/include -I/usr/lib/gcc/x86_64-linux-gnu/5/include-fixed -I/usr/include/x86_64-linux-gnu -I/usr/include mainwindow.h -o moc_mainwindow.cpp

compiler_moc_objc_header_make_all:
compiler_moc_objc_header_clean:
compiler_moc_source_make_all:
compiler_moc_source_clean:
compiler_uic_make_all: ui_mainwindow.h
compiler_uic_clean:
	-$(DEL_FILE) ui_mainwindow.h
ui_mainwindow.h: mainwindow.ui \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/bin/uic
	/home/<USER>/Qt5.13.0/5.13.0/gcc_64/bin/uic mainwindow.ui -o ui_mainwindow.h

compiler_yacc_decl_make_all:
compiler_yacc_decl_clean:
compiler_yacc_impl_make_all:
compiler_yacc_impl_clean:
compiler_lex_make_all:
compiler_lex_clean:
compiler_clean: compiler_moc_predefs_clean compiler_moc_header_clean compiler_uic_clean 

####### Compile

DirNode.o: DirNode.cpp stdafx.h \
		targetver.h \
		DirNode.h
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o DirNode.o DirNode.cpp

main.o: main.cpp mainwindow.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtWidgets/QMainWindow \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtWidgets/qmainwindow.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtWidgets/qtwidgetsglobal.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtGui/qtguiglobal.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qglobal.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qconfig-bootstrapped.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qconfig.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qtcore-config.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qsystemdetection.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qprocessordetection.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qcompilerdetection.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qtypeinfo.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qsysinfo.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qlogging.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qflags.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qatomic.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qbasicatomic.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qatomic_bootstrap.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qgenericatomic.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qatomic_cxx11.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qatomic_msvc.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qglobalstatic.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qmutex.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qnumeric.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qversiontagging.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtGui/qtgui-config.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtWidgets/qtwidgets-config.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtWidgets/qwidget.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtGui/qwindowdefs.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qobjectdefs.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qnamespace.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qobjectdefs_impl.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtGui/qwindowdefs_win.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qobject.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qstring.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qchar.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qbytearray.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qrefcount.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qarraydata.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qstringliteral.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qstringalgorithms.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qstringview.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qstringbuilder.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qlist.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qalgorithms.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qiterator.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qhashfunctions.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qpair.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qvector.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qpoint.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qbytearraylist.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qstringlist.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qregexp.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qstringmatcher.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qcoreevent.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qscopedpointer.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qmetatype.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qvarlengtharray.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qcontainerfwd.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qobject_impl.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qmargins.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtGui/qpaintdevice.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qrect.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qsize.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtGui/qpalette.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtGui/qcolor.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtGui/qrgb.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtGui/qrgba64.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtGui/qbrush.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtGui/qmatrix.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtGui/qpolygon.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtGui/qregion.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qdatastream.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qiodevice.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qline.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtGui/qtransform.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtGui/qpainterpath.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtGui/qimage.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtGui/qpixelformat.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtGui/qpixmap.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qsharedpointer.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qshareddata.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qhash.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qsharedpointer_impl.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtGui/qfont.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtGui/qfontmetrics.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtGui/qfontinfo.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtWidgets/qsizepolicy.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtGui/qcursor.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtGui/qkeysequence.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtGui/qevent.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qvariant.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qmap.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qdebug.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qtextstream.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qlocale.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qset.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qcontiguouscache.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qurl.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qurlquery.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qfile.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qfiledevice.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtGui/qvector2d.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtGui/qtouchdevice.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtWidgets/qtabwidget.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtGui/qicon.h \
		../ldfcr/ldfcr.h \
		../Pub/OS/4linux/winapi4linux.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtWidgets/QApplication \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtWidgets/qapplication.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qcoreapplication.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qeventloop.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtWidgets/qdesktopwidget.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtGui/qguiapplication.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtGui/qinputmethod.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtWidgets/QStyleFactory \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtWidgets/qstylefactory.h
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o main.o main.cpp

mainwindow.o: mainwindow.cpp mainwindow.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtWidgets/QMainWindow \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtWidgets/qmainwindow.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtWidgets/qtwidgetsglobal.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtGui/qtguiglobal.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qglobal.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qconfig-bootstrapped.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qconfig.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qtcore-config.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qsystemdetection.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qprocessordetection.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qcompilerdetection.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qtypeinfo.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qsysinfo.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qlogging.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qflags.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qatomic.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qbasicatomic.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qatomic_bootstrap.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qgenericatomic.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qatomic_cxx11.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qatomic_msvc.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qglobalstatic.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qmutex.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qnumeric.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qversiontagging.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtGui/qtgui-config.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtWidgets/qtwidgets-config.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtWidgets/qwidget.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtGui/qwindowdefs.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qobjectdefs.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qnamespace.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qobjectdefs_impl.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtGui/qwindowdefs_win.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qobject.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qstring.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qchar.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qbytearray.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qrefcount.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qarraydata.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qstringliteral.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qstringalgorithms.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qstringview.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qstringbuilder.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qlist.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qalgorithms.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qiterator.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qhashfunctions.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qpair.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qvector.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qpoint.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qbytearraylist.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qstringlist.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qregexp.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qstringmatcher.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qcoreevent.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qscopedpointer.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qmetatype.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qvarlengtharray.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qcontainerfwd.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qobject_impl.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qmargins.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtGui/qpaintdevice.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qrect.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qsize.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtGui/qpalette.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtGui/qcolor.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtGui/qrgb.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtGui/qrgba64.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtGui/qbrush.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtGui/qmatrix.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtGui/qpolygon.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtGui/qregion.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qdatastream.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qiodevice.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qline.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtGui/qtransform.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtGui/qpainterpath.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtGui/qimage.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtGui/qpixelformat.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtGui/qpixmap.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qsharedpointer.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qshareddata.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qhash.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qsharedpointer_impl.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtGui/qfont.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtGui/qfontmetrics.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtGui/qfontinfo.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtWidgets/qsizepolicy.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtGui/qcursor.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtGui/qkeysequence.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtGui/qevent.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qvariant.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qmap.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qdebug.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qtextstream.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qlocale.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qset.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qcontiguouscache.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qurl.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qurlquery.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qfile.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qfiledevice.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtGui/qvector2d.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtGui/qtouchdevice.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtWidgets/qtabwidget.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtGui/qicon.h \
		../ldfcr/ldfcr.h \
		../Pub/OS/4linux/winapi4linux.h \
		ui_mainwindow.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/QVariant \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtWidgets/QApplication \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtWidgets/qapplication.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qcoreapplication.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qeventloop.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtWidgets/qdesktopwidget.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtGui/qguiapplication.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtGui/qinputmethod.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtWidgets/QFrame \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtWidgets/qframe.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtWidgets/QGridLayout \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtWidgets/qgridlayout.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtWidgets/qlayout.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtWidgets/qlayoutitem.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtWidgets/qboxlayout.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtWidgets/QHBoxLayout \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtWidgets/QLabel \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtWidgets/qlabel.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtWidgets/QLineEdit \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtWidgets/qlineedit.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtGui/qtextcursor.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtGui/qtextformat.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtGui/qpen.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtGui/qtextoption.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtWidgets/QPushButton \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtWidgets/qpushbutton.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtWidgets/qabstractbutton.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtWidgets/QSpacerItem \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtWidgets/QTextEdit \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtWidgets/qtextedit.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtWidgets/qabstractscrollarea.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtGui/qtextdocument.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtWidgets/QVBoxLayout \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtWidgets/QWidget \
		../trcrt/trcrt.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtWidgets/QMessageBox \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtWidgets/qmessagebox.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtWidgets/qdialog.h
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o mainwindow.o mainwindow.cpp

slots.o: slots.cpp mainwindow.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtWidgets/QMainWindow \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtWidgets/qmainwindow.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtWidgets/qtwidgetsglobal.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtGui/qtguiglobal.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qglobal.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qconfig-bootstrapped.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qconfig.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qtcore-config.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qsystemdetection.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qprocessordetection.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qcompilerdetection.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qtypeinfo.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qsysinfo.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qlogging.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qflags.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qatomic.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qbasicatomic.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qatomic_bootstrap.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qgenericatomic.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qatomic_cxx11.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qatomic_msvc.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qglobalstatic.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qmutex.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qnumeric.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qversiontagging.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtGui/qtgui-config.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtWidgets/qtwidgets-config.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtWidgets/qwidget.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtGui/qwindowdefs.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qobjectdefs.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qnamespace.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qobjectdefs_impl.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtGui/qwindowdefs_win.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qobject.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qstring.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qchar.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qbytearray.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qrefcount.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qarraydata.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qstringliteral.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qstringalgorithms.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qstringview.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qstringbuilder.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qlist.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qalgorithms.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qiterator.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qhashfunctions.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qpair.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qvector.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qpoint.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qbytearraylist.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qstringlist.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qregexp.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qstringmatcher.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qcoreevent.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qscopedpointer.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qmetatype.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qvarlengtharray.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qcontainerfwd.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qobject_impl.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qmargins.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtGui/qpaintdevice.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qrect.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qsize.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtGui/qpalette.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtGui/qcolor.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtGui/qrgb.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtGui/qrgba64.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtGui/qbrush.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtGui/qmatrix.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtGui/qpolygon.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtGui/qregion.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qdatastream.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qiodevice.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qline.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtGui/qtransform.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtGui/qpainterpath.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtGui/qimage.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtGui/qpixelformat.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtGui/qpixmap.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qsharedpointer.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qshareddata.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qhash.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qsharedpointer_impl.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtGui/qfont.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtGui/qfontmetrics.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtGui/qfontinfo.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtWidgets/qsizepolicy.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtGui/qcursor.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtGui/qkeysequence.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtGui/qevent.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qvariant.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qmap.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qdebug.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qtextstream.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qlocale.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qset.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qcontiguouscache.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qurl.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qurlquery.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qfile.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qfiledevice.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtGui/qvector2d.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtGui/qtouchdevice.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtWidgets/qtabwidget.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtGui/qicon.h \
		../ldfcr/ldfcr.h \
		../Pub/OS/4linux/winapi4linux.h \
		ui_mainwindow.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/QVariant \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtWidgets/QApplication \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtWidgets/qapplication.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qcoreapplication.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qeventloop.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtWidgets/qdesktopwidget.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtGui/qguiapplication.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtGui/qinputmethod.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtWidgets/QFrame \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtWidgets/qframe.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtWidgets/QGridLayout \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtWidgets/qgridlayout.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtWidgets/qlayout.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtWidgets/qlayoutitem.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtWidgets/qboxlayout.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtWidgets/QHBoxLayout \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtWidgets/QLabel \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtWidgets/qlabel.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtWidgets/QLineEdit \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtWidgets/qlineedit.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtGui/qtextcursor.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtGui/qtextformat.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtGui/qpen.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtGui/qtextoption.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtWidgets/QPushButton \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtWidgets/qpushbutton.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtWidgets/qabstractbutton.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtWidgets/QSpacerItem \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtWidgets/QTextEdit \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtWidgets/qtextedit.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtWidgets/qabstractscrollarea.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtGui/qtextdocument.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtWidgets/QVBoxLayout \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtWidgets/QWidget \
		../trcrt/trcrt.h \
		DirNode.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtWidgets/QFileDialog \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtWidgets/qfiledialog.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qdir.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtCore/qfileinfo.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtWidgets/qdialog.h \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtWidgets/QMessageBox \
		/home/<USER>/Qt5.13.0/5.13.0/gcc_64/include/QtWidgets/qmessagebox.h
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o slots.o slots.cpp

moc_mainwindow.o: moc_mainwindow.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o moc_mainwindow.o moc_mainwindow.cpp

####### Install

install_target: first FORCE
	@test -d $(INSTALL_ROOT)/opt/ldfcrTester/bin || mkdir -p $(INSTALL_ROOT)/opt/ldfcrTester/bin
	-$(QINSTALL_PROGRAM) $(QMAKE_TARGET) $(INSTALL_ROOT)/opt/ldfcrTester/bin/$(QMAKE_TARGET)

uninstall_target: FORCE
	-$(DEL_FILE) $(INSTALL_ROOT)/opt/ldfcrTester/bin/$(QMAKE_TARGET)
	-$(DEL_DIR) $(INSTALL_ROOT)/opt/ldfcrTester/bin/ 


install: install_target  FORCE

uninstall: uninstall_target  FORCE

FORCE:

