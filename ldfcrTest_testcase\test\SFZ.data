{"respondRule": [{"stopOutgoing": 0, "takeScreenshot": false, "createTime": 1669103074000, "name": "外发", "sendMail": false, "enableCancel": 0, "id": 2, "filters": [{"type": "deviceType", "relation": "either", "object": ["terminal-agent"]}, {"type": "lossType", "relation": "either", "object": ["8", "9", "26", "25", "2", "3", "4", "14", "15", "16", "11", "1", "18", "5", "17", "7", "23", "24", "13", "0", "20"]}], "alarmInfo": {"alarmLimit": 3, "msgFormType": 0, "configMethod": 1, "name": "外发", "msgFormPosition": 0, "id": 1, "isDel": false, "msgFormClose": 30}, "warnContent": ""}], "checkRule": [{"mode": 1, "exceptWord": "", "checkTimes": 3, "createTime": 1669282002000, "ruleType": "3", "name": "身份证匹配", "lua": "", "id": 6, "type": 3, "regular": "[1-9]\\d{5}(18|19|([2]\\d))?\\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)(\\d{3}|\\d{2})[0-9Xx]"}], "strategy": [{"severity": "1", "respondRule": "2", "createTime": 1669282691419, "checkRule": "6", "name": "身份证", "dripScan": false, "id": 6, "classification": [{"checkExpr": "6", "name": "身份证", "exceptRule": "", "id": 5}]}], "businessType": {"opTypes": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 20], "type": 99}}