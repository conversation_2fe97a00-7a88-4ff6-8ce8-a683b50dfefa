#include <iostream>
#include "src/ldfcrtest.h"
#include <string>
#include "utils.h"
#include <string.h>
#if defined(__GNUC__)
#include <unistd.h>
#endif
#define CF "\n"

static void print_usage() {
	printf("Usage: ldfcrtest [-h] [-s strategyPath] [-f testfile] [-d testDir]" CF);
	printf(CF "Options:" CF);
	printf("    -h: print this usage info." CF);
	printf("    -s <path>: strategy's file path" CF);
	printf("    -f <testfile>: the path of the file to be detected" CF);
	printf("    -d <testDir>: the path of the dir to be detected" CF);
	printf("    -c <WorkDir>: Switch the working path to the previous layer of the current directory (linux)" CF);
	printf("    -t <test>: basic algorithms to be detected" CF);
}

static std::string strategy;
static std::string FilePath;
static std::string FileDir;
static std::string WorkDir;
static std::string AlgoDetect;

std::string GetParentDir(const std::string& path) {
	size_t pos = path.find_last_of('/');
	if (pos == std::string::npos) {
		return path; // 如果没有 '/'，直接返回原路径
	}
	// 检查是否以 '/' 结尾，防止多次移除
	if (pos == path.length() - 1) {
		pos = path.find_last_of('/', pos - 1);
	}
	return path.substr(0, pos + 1);
}

std::vector<tString> filesvec;
std::string currentPath = GetCurPath();
std::string parentDir = GetParentDir(currentPath);

static int parseOpt(int argc, char **argv) {
	for (auto i = 1; i < argc; i++) {
		if (*argv[i] == '-') {
			switch (argv[i][1]) {
			case 'h':
				print_usage();
				exit(0);
			case 's':
				i += 1;
				strategy = argv[i];
				break;
			case 'f':
				i += 1;
				FilePath = argv[i];
#if defined(__GNUC__)
				filesvec.push_back(FilePath);
#elif defined(_WIN32)
				filesvec.push_back(string_to_wstring(FilePath).c_str());
#endif
				break;
			case 'd':
				i += 1;
				FileDir = (argv[i]);
#if defined(__GNUC__)
				filesvec = trave_dir(FileDir.c_str());
#elif defined(_WIN32)
				filesvec = read_dir(string_to_wstring(FileDir).c_str());
#endif
				break;
			case 'c':
				i += 1;
				WorkDir = ("../");
				break;
			case 't':
				if (strcmp(argv[i], "-test1") == 0) {
					strategy = parentDir + "test/data/hahaha.data";
					FilePath = parentDir + "test/hahaha.txt";
				}
				else if (strcmp(argv[i], "-test2") == 0) {
					strategy = parentDir + "test/data/SFZ.data";
					FilePath = parentDir + "test/SFZ.txt";
				}
				else if (strcmp(argv[i], "-test3") == 0) {
					strategy = parentDir + "test/data/author.data";
					FilePath = parentDir + "test/Multi_Author.doc";
				}
				else if (strcmp(argv[i], "-test4") == 0) {
					strategy = parentDir + "test/data/Source_code.data";
					FilePath = parentDir + "test/bai.java";
				}
				else if (strcmp(argv[i], "-test5") == 0) {
					strategy = parentDir + "test/tel/s_image.data";
					FilePath = parentDir + "test/testOCR.png1";
				}
				else if (strcmp(argv[i], "-test6") == 0) {
					strategy = parentDir + "test/data/KW_Drip.data";
					FilePath = parentDir + "test/KW_Drip.txt";
				}
				else if (strcmp(argv[i], "-test7") == 0) {
					strategy = parentDir + "test/data/SFZ_Drip.data";
					FilePath = parentDir + "test/SFZ_Drip.txt";
				}
				else if (strcmp(argv[i], "-test8") == 0) {
					strategy = parentDir + "test/data/DB.data";
					FilePath = parentDir + "test/DataFile.txt";
				}
				else {
					printf("Error: not supported test option!\n");
					return -1;
				}
#if defined(__GNUC__)
				WorkDir = "../";
				filesvec.push_back(FilePath);
#elif defined(_WIN32)
				filesvec.push_back(string_to_wstring(FilePath));
#endif
				break;
			default:
				printf("Error: not support argument!\n");
				return -1;
			}
		}
	}
	return 0;
}

int main(int argc, char** argv) {
	/*parse cmd line*/
	int res = parseOpt(argc, argv);
	if (res) {
		return 1;
	}

#if defined(__GNUC__)
	chdir(WorkDir.c_str());
#endif

	ldfcrtest * pLdfcr = new ldfcrtest();
	pLdfcr->initSetting();
	INISetting inis;
	pLdfcr->GetSetting(inis);
#if defined(_WIN32)
	if (filesvec.size() == 0)
	{
		printf("there is no detection file inside the container\n");
		return 0;
	}
#endif

	if (strcmp(inis.modeSelect.c_str(), "G") == 0)
	{
		pLdfcr->Gdetect(strategy, filesvec, inis);
	}
	else
	{
		pLdfcr->Pdetect(strategy, filesvec, inis);
	}

	delete pLdfcr;
	pLdfcr = nullptr;
	return 0;
}

//#elif defined(_WIN32)
//int main()
//{
//	ldfcrtest * pLdfcr = new ldfcrtest();
//	pLdfcr->initSetting();
//	INISetting inis;
//	pLdfcr->GetSetting(inis);
//	char chStrategyPath[MAX_PATH_LEN];
//	char chFilePath[MAX_PATH_LEN];
//	char chDirPath[MAX_PATH_LEN];
//	std::vector<std::wstring> m_vecFile;
//	while (true)
//	{
//		printf("select 1 update strategy\n");
//		printf("select 2 choose file to detected\n");
//		printf("select 3 choose dir to detected\n");
//		printf("select 4 detect\n");
//		printf("select 5 show strategy and detect file list\n");
//		printf("select 6 reflash detect file container\n");
//		printf("select 0 exit\n");
//
//		printf("select funtion : \n");
//		int iSelect = 0;
//		scanf("%d", &iSelect);
//		switch (iSelect)
//		{
//		case 1:
//			printf("input the path of strategy: \n");
//			scanf("%s", &chStrategyPath);
//			break;
//		case 2:
//			printf("input the path of file to detected: \n");
//			scanf("%s", &chFilePath);
//			m_vecFile.push_back(string_to_wstring(chFilePath));
//			break;
//		case 3:
//			printf("input the dir path of file to detected: \n");
//			scanf("%s", &chDirPath);
//			m_vecFile = read_dir(string_to_wstring(chDirPath).c_str());
//			break;
//		case 4:
//			if (sizeof(chStrategyPath) == 0)
//			{
//				printf("strategy is null,return\n");
//				break;
//			}
//			if (m_vecFile.size() == 0)
//			{
//				printf("the file be deceted is empty,return\n");
//				break;
//			}
//			printf("the strategy is [%s]\n", chStrategyPath);
//			if (m_vecFile.size() == 1)
//			{
//				printf("the file be deceted is [%ws]\n", m_vecFile.front());
//			}
//			else
//			{
//				printf("the file be deceted size is [%d]\n", m_vecFile.size());
//			}
//			pLdfcr->GetSetting(inis);
//			if (strcmp(inis.modeSelect.c_str(), "G") == 0)
//			{
//				pLdfcr->Gdetect((wchar_t *)string_to_wstring(chStrategyPath).c_str(), m_vecFile, inis);
//			}
//			else
//			{
//				pLdfcr->Pdetect((wchar_t *)string_to_wstring(chStrategyPath).c_str(), m_vecFile, inis);
//			}
//			break;
//		case 5:
//			printf("strategy is [%s]\n", chStrategyPath);
//			if (m_vecFile.size() != 0)
//			{
//				for (int i = 0; i < m_vecFile.size(); ++i)
//				{
//					printf("%d : [%ws]\n",i + 1, m_vecFile[i].c_str());
//				}
//			}
//			break;
//		case 6:
//			m_vecFile.clear();
//			break;
//		case 0:
//			delete pLdfcr;
//			pLdfcr = nullptr;
//			exit(0);
//		default:
//			printf("error input ,input again\n");
//			break;
//		}
//	}
//	delete pLdfcr;
//	pLdfcr = nullptr;
//	system("pause");
//	return 0;
//}
//#endif
