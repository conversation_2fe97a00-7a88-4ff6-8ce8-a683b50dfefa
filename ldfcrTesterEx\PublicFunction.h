#pragma once
#include <string>
#include <map>
#include <list>
#include <ctime>


using namespace std;

bool ReadTextContent(const char* file_path, string& text);//1008 读取文本文件内容

void ExtractIdWithName(const CString& filePath, map<int, string>& idNameMap); // 提取策略id和策略名称

CString GetProPath();  // 函数：获取运行程序目录

void ergodicFolder(const wchar_t * wch, std::list<std::wstring>& filename_list); // 遍历文件夹

string IniPath();  // 将所有的ini配置文件初始化写到公共函数中

std::string GetCurrentTie();

