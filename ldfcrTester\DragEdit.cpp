#include "stdafx.h"
#include "DragEdit.h"


CDragEdit::CDragEdit()
{
}


CDragEdit::~CDragEdit()
{
}
BEGIN_MESSAGE_MAP(CDragEdit, CEdit)
	ON_WM_CREATE()
	ON_WM_DROPFILES()
END_MESSAGE_MAP()


int CDragEdit::OnCreate(LPCREATESTRUCT lpCreateStruct)
{
	if (CEdit::OnCreate(lpCreateStruct) == -1)
		return -1;

	// TODO:  在此添加您专用的创建代码
	DragAcceptFiles(TRUE);

	return 0;
}


void CDragEdit::OnDropFiles(HDROP hDropInfo)
{
	// TODO: 在此添加消息处理程序代码和/或调用默认值

	UINT count;
	TCHAR filePath[MAX_PATH] = { 0 };
	count = DragQueryFile(hDropInfo, -1, NULL, 0);
	if (1 == count)
	{
		DragQueryFile(hDropInfo, 0, filePath, sizeof(filePath));
		this->SetWindowTextW(filePath);

		UpdateData(FALSE);
		DragFinish(hDropInfo); //拖放成功后，释放内存
	}
	else
	{
		CString szFilePath;
		for (UINT i = 0; i < count; i++)
		{
			int pahtLen = DragQueryFile(hDropInfo, i, filePath, sizeof(filePath));
			szFilePath = szFilePath + filePath + _T("\r\n");
		}
		this->SetWindowTextW(szFilePath);
		UpdateData(FALSE);
		DragFinish(hDropInfo);
	}
}

