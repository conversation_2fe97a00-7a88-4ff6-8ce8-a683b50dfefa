
// ldfcrTesterDlg.cpp : 实现文件
//

#include "stdafx.h"
#include "ldfcrTester.h"
#include "ldfcrTesterDlg.h"

#include "trcrt.h"

#include <sys/stat.h>

#include "./ui/XFolderDialog.h"
#include "DirNode.h"
#include <list>
#include <map>

#include "InputCheckTextDlg.h"
#include "CldfcrLog.h"
#include "viewStgy.h"

#include <sys/timeb.h>
#include "../INC/DlpULog.h"
#include <imagehlp.h> 
#include <stdlib.h> 
#include <fstream>
#include "SimpleIni.h"
#include "CodeT.h"
#include "StringCoder.hpp"
#include "filesystem.hpp"
#include "UnicodeString.h"
#include "InitChose.h"
#include "thread/ThreadPool.h"
//#include "cpprest/http_client.h"
//#include "cpprest/filestream.h"

#include "rapidjson/document.h"
#include "rapidjson/error/en.h"

#include "ldfcr.h"

#pragma comment(lib, "dbghelp.lib")
#pragma comment(lib, "version.lib")


using namespace std;
using namespace rapidjson;
//using namespace utility;
//using namespace web;
//using namespace web::http;
//using namespace web::http::client;
//using namespace concurrency::streams;

LOG_HANDLE   g_log_handle = -1;
#define MAX_FILE_COUNT          1024

#ifdef _DEBUG
#define new DEBUG_NEW
#endif

CSimpleIniA ini;
// 用于应用程序“关于”菜单项的 CAboutDlg 对话框

class CAboutDlg : public CDialog
{
public:
	CAboutDlg();

	// 对话框数据
	enum { IDD = IDD_ABOUTBOX };

protected:
	virtual void DoDataExchange(CDataExchange* pDX);    // DDX/DDV 支持

// 实现
protected:
	DECLARE_MESSAGE_MAP()
};

CAboutDlg::CAboutDlg() : CDialog(CAboutDlg::IDD)
{
}

void CAboutDlg::DoDataExchange(CDataExchange* pDX)
{
	CDialog::DoDataExchange(pDX);
}

BEGIN_MESSAGE_MAP(CAboutDlg, CDialog)
END_MESSAGE_MAP()


// CldfcrTesterDlg 对话框




CldfcrTesterDlg::CldfcrTesterDlg(CWnd* pParent /*=NULL*/)
	: CDialog(CldfcrTesterDlg::IDD, pParent)
	, m_strStrategyFilePath(_T(""))
	, m_strFilePath4FCR(_T(""))
	, m_iFcrCount(0)
	, m_iTcrCount(0)
	, m_strSingleFcrTime(_T(""))
	, m_strFolderPath4Fcr(_T(""))
	, m_iFileCount(0)
	, m_iSensFileCount(0)
	, m_strShowLogPath(_T(""))
	, m_strShowLogPath2(_T(""))
	, m_outTimeJug(0)
	, m_intOptype(0)
	, m_bfilefp(0)
	, m_bsvm(0)
	, m_bfiledb(0)
	, m_bocr_embed(0)
	, m_bocr(0)
	, m_filext(0)
	, m_password(_T(""))
	, m_csNormalKey(_T(""))
	, m_strTimeOutRespRulePath(_T(""))
	, m_strTimeOutRulesName(_T(""))
	, m_strPassWordRespRulePath(_T(""))
	, m_tim_count(_T(""))
	, m_thread_num(_T(""))
{
	m_pILDFcr = NULL;
	m_bDoingDirFcr = FALSE;

	m_hIcon = AfxGetApp()->LoadIcon(IDR_MAINFRAME);
}

void CldfcrTesterDlg::DoDataExchange(CDataExchange* pDX)
{
	CDialog::DoDataExchange(pDX);
	DDX_Text(pDX, IDC_STRATEGY_FILEPATH, m_strStrategyFilePath);
	DDX_Text(pDX, IDC_EDIT_FCR_FILEPATH, m_strFilePath4FCR);
	DDX_Text(pDX, IDC_FCRCOUNT, m_iFcrCount);
	DDV_MinMaxInt(pDX, m_iFcrCount, 0, 10000000);
	DDX_Text(pDX, IDC_FCRCOUNT, m_iTcrCount);
	DDV_MinMaxInt(pDX, m_iTcrCount, 0, 10000000);
	DDX_Text(pDX, IDC_SINGLE_FCRTIME, m_strSingleFcrTime);
	DDX_Text(pDX, IDC_EDIT_FCR_FOLDERPATH, m_strFolderPath4Fcr);
	DDX_Text(pDX, IDC_FILE_COUNT, m_iFileCount);
	DDX_Text(pDX, IDC_SENSFILE_COUNT, m_iSensFileCount);
	DDV_MinMaxInt(pDX, m_iSensFileCount, 0, 10000000);
	DDX_Text(pDX, IDC_SHOW_LOGPATH, m_strShowLogPath);
	DDX_Text(pDX, IDC_SHOW_LOGPATH2, m_strShowLogPath2);
	DDX_Control(pDX, IDC_INPUT_INTERCEPT, editinput);
	DDX_Text(pDX, IDC_INPUT_INTERCEPT, m_outTimeJug);
	DDX_Text(pDX, IDC_EDIT2, m_intOptype);
	DDX_Control(pDX, IDC_EDIT3, m_edit_password);
	DDX_Text(pDX, IDC_EDIT3, m_password);
	DDX_Text(pDX, IDC_EDIT9, m_csNormalKey);
	DDX_Text(pDX, IDC_EDIT_TIMEOUT_RULEPATH, m_strTimeOutRespRulePath);
	DDX_Text(pDX, IDC_EDIT_PASSWORD_RULEPATH, m_strPassWordRespRulePath);
	DDX_Text(pDX, IDC_EDIT_TIMEOUT_RULES_NAME, m_strTimeOutRulesName);
	DDX_Text(pDX, IDC_EDIT_PASSWORD_RULENAME, m_strPassWordRuleName);
	DDX_Control(pDX, IDC_STRATEGY_FILEPATH, m_CStrategyEdit);
	DDX_Control(pDX, IDC_EDIT_FCR_FILEPATH, m_CFcrEdit);
	DDX_Control(pDX, IDC_EDIT_TIMEOUT_RULEPATH, m_CTimeOutRuleEdit);
	DDX_Control(pDX, IDC_EDIT_PASSWORD_RULEPATH, m_CPassWordRuleEdit);
	DDX_Control(pDX, IDC_EDIT_FCR_FOLDERPATH, m_CFolderPath4FcrEdit);
	DDX_Control(pDX, IDC_SHOW_LOGPATH, m_CShowLogPathEdit);
	DDX_Control(pDX, IDC_SHOW_LOGPATH2, m_CShowLogPathEdit2);
	DDX_Text(pDX, IDC_EDIT7, m_tim_count);
	DDX_Text(pDX, IDC_EDIT1, m_thread_num);
}

BEGIN_MESSAGE_MAP(CldfcrTesterDlg, CDialog)
	ON_WM_SYSCOMMAND()
	ON_WM_PAINT()
	ON_WM_QUERYDRAGICON()
	//}}AFX_MSG_MAP
	ON_BN_CLICKED(IDC_BTN_VIEW_STRATEGY, &CldfcrTesterDlg::OnBnClickedBtnViewStrategy)
	ON_BN_CLICKED(IDC_BTN_SELECT_FILE, &CldfcrTesterDlg::OnBnClickedBtnSelectFile)
	ON_BN_CLICKED(IDC_BTN_EXEC_FCR_SINGLEFILE, &CldfcrTesterDlg::OnBnClickedBtnExecFcrSinglefile)
	ON_BN_CLICKED(IDC_BTN_SELECT_FOLDER, &CldfcrTesterDlg::OnBnClickedBtnSelectFolder)
	ON_BN_CLICKED(IDC_BTN_EXEC_FCR_FOLDER, &CldfcrTesterDlg::OnBnClickedBtnExecFcrFolder)
	ON_BN_CLICKED(EXEC_TCR, &CldfcrTesterDlg::OnBnClickedTcr)
	ON_BN_CLICKED(IDC_BTN_UPDATE_STRATEGY, &CldfcrTesterDlg::OnBnClickedBtnUpdateStrategy)
	ON_BN_CLICKED(IDC_BTN_TACTICS_FILE2, &CldfcrTesterDlg::OnBnClickedBtnTacticsFile2)
	ON_BN_CLICKED(IDC_LOG_FLUSH, &CldfcrTesterDlg::OnBnClickedLogFlush)
	ON_BN_CLICKED(IDC_CHOOSE_LOG, &CldfcrTesterDlg::OnBnClickedChooseLog)
	ON_EN_CHANGE(IDC_INPUT_INTERCEPT, &CldfcrTesterDlg::OnEnChangeInputIntercept)
	ON_EN_CHANGE(IDC_EDIT_FCR_FILEPATH, &CldfcrTesterDlg::OnEnChangeEditFcrFilepath)
	ON_BN_CLICKED(IDC_CHECK3, &CldfcrTesterDlg::OnBnClickedFILEFP)
	ON_BN_CLICKED(IDC_CHECK4, &CldfcrTesterDlg::OnBnClickedSVM)
	ON_BN_CLICKED(IDC_CHECK5, &CldfcrTesterDlg::OnBnClickedFILEDB)
	ON_BN_CLICKED(IDC_CHECK7, &CldfcrTesterDlg::OnBnClickedOCR_EMBED)
	ON_BN_CLICKED(IDC_CHECK8, &CldfcrTesterDlg::OnBnClickedOCR)
	ON_BN_CLICKED(IDC_CHECK9, &CldfcrTesterDlg::OnBnClickedFILEXT)
	ON_BN_CLICKED(IDC_CHECK1, &CldfcrTesterDlg::OnBnClickedCheck1)
	ON_BN_CLICKED(IDC_BUTTON1, &CldfcrTesterDlg::OnBnClickedSetAdvKeyWord)
	ON_BN_CLICKED(IDC_BUTTON2, &CldfcrTesterDlg::OnBnClickedGetDefaultList)
	ON_EN_CHANGE(IDC_STRATEGY_FILEPATH, &CldfcrTesterDlg::OnEnChangeStrategyFilepath)
	ON_CBN_SELCHANGE(IDC_COMBO_SELECT_ID_NAME, &CldfcrTesterDlg::OnCbnSelchangeComboSelectIdName)
	ON_BN_CLICKED(IDC_BTN_SELECT_TIMEOUT_JSON, &CldfcrTesterDlg::OnBnClickedBtnSelectTimeoutJson)
	ON_BN_CLICKED(IDC_BTN_SELECT_PASSWORD_JSON, &CldfcrTesterDlg::OnBnClickedBtnSelectPasswordJson)
	ON_EN_CHANGE(IDC_EDIT_TIMEOUT_RULES_NAME, &CldfcrTesterDlg::OnEnChangeEditTimeoutRulesName)
	ON_EN_CHANGE(IDC_EDIT_PASSWORD_RULENAME, &CldfcrTesterDlg::OnEnChangeEditPasswordRulename)
	ON_BN_CLICKED(IDC_BUT_TIMEOUT_TRIGGER_RULE, &CldfcrTesterDlg::OnBnClickedButTimeoutTriggerRule)
	ON_BN_CLICKED(IDC_BUT_PASSWORD_TRIGGER_RULE, &CldfcrTesterDlg::OnBnClickedButPasswordTriggerRule)
	ON_EN_CHANGE(IDC_EDIT9, &CldfcrTesterDlg::OnEnChangeEdit9)
	ON_BN_CLICKED(IDC_BUT_GET_STOP_TYPE, &CldfcrTesterDlg::OnBnClickedButGetStopType)
	ON_BN_CLICKED(IDC_BTN_EXEC_TCR_FOLDER, &CldfcrTesterDlg::OnBnClickedBtnExecTcrFolder)
	ON_BN_CLICKED(IDC_BTN_EXEC_TCR_SINGLETEXT, &CldfcrTesterDlg::OnBnClickedBtnExecTcrSingletext)
	ON_BN_CLICKED(IDC_CHOOSE_LOG2, &CldfcrTesterDlg::OnBnClickedChooseLog2)
	ON_BN_CLICKED(IDC_LOG_FLUSH2, &CldfcrTesterDlg::OnBnClickedLogFlush2)
	ON_EN_CHANGE(IDC_EDIT_PASSWORD_RULEPATH, &CldfcrTesterDlg::OnEnChangeEditPasswordRulepath)
	ON_EN_CHANGE(IDC_EDIT_PASSWORD_RULENAME, &CldfcrTesterDlg::OnEnChangeEditPasswordRulename)
	ON_EN_CHANGE(IDC_EDIT_TIMEOUT_RULES_NAME, &CldfcrTesterDlg::OnEnChangeEditTimeoutRulesName)
	ON_EN_CHANGE(IDC_EDIT_TIMEOUT_RULEPATH, &CldfcrTesterDlg::OnEnChangeEditTimeoutRulepath)
END_MESSAGE_MAP()


LONG WINAPI MyUnhandledExceptionFilter(struct _EXCEPTION_POINTERS *ExceptionInfo)
{
	HANDLE hFile = CreateFile(L"mini.dmp", GENERIC_READ | GENERIC_WRITE,
		FILE_SHARE_WRITE, NULL, CREATE_ALWAYS, FILE_ATTRIBUTE_NORMAL, NULL);

	if (hFile == INVALID_HANDLE_VALUE)
		return EXCEPTION_EXECUTE_HANDLER;

	MINIDUMP_EXCEPTION_INFORMATION mdei;
	mdei.ThreadId = GetCurrentThreadId();
	mdei.ExceptionPointers = ExceptionInfo;
	mdei.ClientPointers = NULL;
	MINIDUMP_CALLBACK_INFORMATION mci;
	mci.CallbackRoutine = NULL;
	mci.CallbackParam = 0;

	MiniDumpWriteDump(GetCurrentProcess(), GetCurrentProcessId(), hFile, MiniDumpWithFullMemory, &mdei, NULL, &mci);

	CloseHandle(hFile);

	return EXCEPTION_EXECUTE_HANDLER;
}

// CldfcrTesterDlg 消息处理程序
/*
* 文件初始化
*
*
*/
BOOL CldfcrTesterDlg::OnInitDialog()
{
	initLog();
	CDialog::OnInitDialog();

	try
	{
		//-------- 创建client客户端 --------
		// 初始化
		WSADATA wsadata;
		WSAStartup(MAKEWORD(2, 2), &wsadata);

		// 创建套接字
		SOCKET clientSocket = socket(PF_INET, SOCK_STREAM, 0);
		if (clientSocket == INVALID_SOCKET)
		{
			dlplog_debug(g_log_handle, "[%s] Socket creation failed!", __FUNCTION__);
		}
		else
		{
			dlplog_debug(g_log_handle, "[%s] The socket was created successfully!", __FUNCTION__);
		}

		// 绑定套接字	指定绑定的IP地址和端口号
		sockaddr_in socketAddr;
		socketAddr.sin_family = AF_INET;

		socketAddr.sin_addr.S_un.S_addr = inet_addr("************");
		socketAddr.sin_port = htons(80);

		int cRes = connect(clientSocket, (SOCKADDR*)&socketAddr, sizeof(socketAddr));
		if (SOCKET_ERROR == cRes)
		{
			dlplog_info(g_log_handle, "[%s] 本设备需联网，请联网使用！.....", __FUNCTION__);
			dlplog_debug(g_log_handle, "[%s] Client:\tConnection to server failed.....", __FUNCTION__);
			exit(0);
		}
		else
		{
			dlplog_debug(g_log_handle, "[%s] Client:\tConnection to server successfully.....", __FUNCTION__);
		}
	}
	catch (const exception&e)
	{
		exit(0);
	}
	
	////	4	发送请求
	//const char* sendBuf = "from Client: hello service.";
	//send(clientSocket, sendBuf, strlen(sendBuf), 0);

	////	5	接收 数据
	//char recvBuf[1024] = {};
	//int bytesReceived = recv(clientSocket, recvBuf, 1024, 0);
	//if (bytesReceived>0)
	//{	
	//	cout << "The client receives the data: " << recvBuf << endl << endl;
	//}
	//else if (bytesReceived == 0)
	//{
	//	dlplog_debug(g_log_handle, "Connection closed by server");
	//}
	//else
	//{
	//	dlplog_debug(g_log_handle, "Receive error");
	//}

	////	6	关闭socket
	//closesocket(clientSocket);

	////	7	终止
	//WSACleanup();
	//dlplog_debug(g_log_handle, "The client exits");
	//cin.get();


	/*try
	{
		const char_t * strIntelnal = L"http://192.168.4.73";
		http_client client(strIntelnal);

		http_request req;
		req.set_method(methods::GET);
		http_response response = client.request(req).get();
		std::string status = std::to_string(response.status_code());
		if (response.status_code() != status_codes::OK)
		{
			MessageBox(_T("link 4.73 failed"));
			dlplog_debug(g_log_handle, "4.73 link falied");
			exit(0);
		}
	}
	catch (const std::exception &e)
	{
		exit(0);
	}*/
	dlplog_debug(g_log_handle, "4.45 link success");
	//生成dump文件
	SetUnhandledExceptionFilter(MyUnhandledExceptionFilter);

	// 将“关于...”菜单项添加到系统菜单中。

	// IDM_ABOUTBOX 必须在系统命令范围内。
	ASSERT((IDM_ABOUTBOX & 0xFFF0) == IDM_ABOUTBOX);
	ASSERT(IDM_ABOUTBOX < 0xF000);

	CMenu* pSysMenu = GetSystemMenu(FALSE);
	if (pSysMenu != NULL)
	{
		BOOL bNameValid;
		CString strAboutMenu;
		bNameValid = strAboutMenu.LoadString(IDS_ABOUTBOX);
		ASSERT(bNameValid);
		if (!strAboutMenu.IsEmpty())
		{
			pSysMenu->AppendMenu(MF_SEPARATOR);
			pSysMenu->AppendMenu(MF_STRING, IDM_ABOUTBOX, strAboutMenu);
		}
	}

	// 设置此对话框的图标。当应用程序主窗口不是对话框时，框架将自动
	//  执行此操作
	SetIcon(m_hIcon, TRUE);			// 设置大图标
	SetIcon(m_hIcon, FALSE);		// 设置小图标

	m_InitChosen = 0;  //虽然加不加结果好像不影响，加一个预防出错
					   // TODO: 在此添加额外的初始化代码
	InitChose initchose;
	initchose.DoModal();

	m_InitChosen = initchose.getInitChose();

	OnNewDocument();

	this->m_iFcrCount = 1;
	this->m_iTcrCount = 1;

	/////////////////////////////////////////////////////////////////
	//初始化内容识别组件
	//trcrt::str::SafeString  strategy_sstr;
	//trcrt::str::tr_str_initString(&strategy_sstr);
	//tr::str::CUnicodeString strategy_sstr;	20230807

	/////////////////////////////////////////////////////////////////


	ini.SetUnicode(1);//设置ini编码

	CString wcsAppDir;
	wcsAppDir = GetProPath();//获取运行程序路径

	CString _str, _str1, _str2, _str3, _str4, _str5, _str6, _str7, _str8, _str9;
	//ini地址初始化
	_str += wcsAppDir;
	_str += L"\\Setting.ini";
	//策略地址初始化
	_str1 += wcsAppDir;
	_str1 += L"\\Strategy.data";
	//待检查文件地址初始化
	_str2 += wcsAppDir;
	_str2 += L"\\SampleFcr.txt";
	//日志文件初始化
	_str3 += wcsAppDir;
	_str3 += L"\\log\\ldfcr.log";
	_str7 += wcsAppDir;
	_str7 += L"\\log\\ldfcrTester.log";
	//待检测文件夹初始化
	_str4 += wcsAppDir;
	_str4 += L"\\log\\ldfcrTester.log";
	m_strShowLogPath = _str4;
	//响应规则地址初始化
	_str5 += wcsAppDir;
	_str5 += L"\\TimeOutRule.json";
	//带密码响应规则地址初始化
	_str8 += wcsAppDir;
	_str8 += L"\\PassWordRule.json";

	//阻断时间初始化
	m_time_input = 0;

	USES_CONVERSION;//声明标识
	const char* _str_str = T2A(_str);
	const char* _str_str1 = T2A(_str1);
	const char* _str_str2 = T2A(_str2);
	const char* _str_str3 = T2A(_str3);//1019
	const char* _str_str4 = T2A(_str4);
	const char* _str_str5 = T2A(_str5);
	const char* _str_str6 = T2A(_str6);
	const char* _str_str7 = T2A(_str7);
	const char* _str_str8 = T2A(_str8);
	const char* _str_str9 = T2A(_str9);
	int i = 1;
	char itc[10];
	sprintf(itc, "%d", i);

	//Utf8转GBK
	string str1 = Utf8ToGbk(_str_str);
	int rc = ini.LoadFile(_str);

	//查找ini文件是否存在
	//不存在则创建并设置默认路径
	//存在则查找ini路径并提取文件内容
	if (rc)
	{
		ini.SetValue("path0", "m_strStrategyFilePath", _str_str1);//写入选择的路径
		ini.SetValue("path1", "m_strFilePath4FCR", _str_str2);//写入选择的路径
		ini.SetValue("showLog", "m_strShowLogPath", _str_str3);//写入选择日志的路径  1019
		ini.SetValue("path2", "m_strFolderPath4Fcr", _str_str4);//写入选择的路径
		ini.SetValue("ResponseRule", "m_strTimeOutRespRulePath", _str_str5);//写入选择的响应规则路径
		ini.SetValue("ResponseRule", "m_strPassWordRespRulePath", _str_str8);//写入选择的带密码响应规则路径
		ini.SetValue("ResponseRule", "m_strTimeOutRulesName", _str_str6);//写入选择的规则名
		ini.SetValue("ResponseRule", "m_strPassWordRuleName", _str_str9);//写入选择的超时响应规则
		ini.SetValue("showLog", "m_strShowLogPath2", _str_str7);//写入选择日志的路径		

		ini.SetValue("LDFCR_MODULE", "m_bfilefp", itc);//写入选择的路径
		((CButton *)GetDlgItem(IDC_CHECK3))->SetCheck(1);
		ini.SetValue("LDFCR_MODULE", "m_bsvm", itc);//写入选择的路径
		((CButton *)GetDlgItem(IDC_CHECK4))->SetCheck(1);
		ini.SetValue("LDFCR_MODULE", "m_bfiledb", itc);//写入选择的路径
		((CButton *)GetDlgItem(IDC_CHECK5))->SetCheck(1);
		ini.SetValue("LDFCR_MODULE", "m_filext", itc);//写入选择的路径ccc
		((CButton *)GetDlgItem(IDC_CHECK9))->SetCheck(1);
		ini.SetValue("LDFCR_MODULE", "m_bocr_embed", itc);//写入选择的路径
		((CButton *)GetDlgItem(IDC_CHECK7))->SetCheck(1);
		ini.SetValue("LDFCR_MODULE", "m_bocr", itc);//写入选择的路径
		((CButton *)GetDlgItem(IDC_CHECK8))->SetCheck(1);
		ini.SetValue("block_on_pwderror", "m_b_o_pwderror", 0);//密码阻断初始化
		m_pwd_error = false;

		ini.SetValue("block_on_pwderror", "password", "");//密码框初始化

		ini.SaveFile(_str, 0);//保存ini文件

							  //bt Steve add 此处进行ini配置文件赋值
		this->m_strStrategyFilePath = wcsAppDir;    //构造策略文件的路径
		this->m_strStrategyFilePath += L"\\Strategy.data";

		this->m_strFilePath4FCR = wcsAppDir;
		this->m_strFilePath4FCR += L"\\SampleFcr.txt";

		this->m_strShowLogPath = wcsAppDir;			//日志文件的路径 1019
		this->m_strShowLogPath += L"\\ldfcr.log";

		this->m_strShowLogPath2 = wcsAppDir;			
		this->m_strShowLogPath2 += L"\\ldfcrTester.log";

		this->m_strFolderPath4Fcr = wcsAppDir;
		this->m_strFolderPath4Fcr += L"";

		this->m_strTimeOutRespRulePath = wcsAppDir;		//响应规则的路径
		this->m_strTimeOutRespRulePath += L"\\TimeOutRule.json";

		this->m_strPassWordRespRulePath = wcsAppDir;		//带密码响应规则的路径
		this->m_strPassWordRespRulePath += L"\\PassWordRule.json";
	}
	else
	{
		//读取ini数据
		const char * path1 = ini.GetValue("path0", "m_strStrategyFilePath", NULL);
		const char * path2 = ini.GetValue("path1", "m_strFilePath4FCR", NULL);
		const char * path3 = ini.GetValue("path2", "m_strFolderPath4Fcr", NULL);
		const char * strShowLog = ini.GetValue("showLog", "m_strShowLogPath", NULL);//1019
		const char * strShowLog2 = ini.GetValue("showLog", "m_strShowLogPath2", NULL);
		const char * strTimeOutRespRulePath = ini.GetValue("ResponseRule", "m_strTimeOutRespRulePath", NULL);
		const char * strPassWordRespRulePath = ini.GetValue("ResponseRule", "m_strPassWordRespRulePath", NULL);
		const char * strTimeOutRulesName = ini.GetValue("ResponseRule", "m_strTimeOutRulesName", NULL);
		const char * strPassWordRuleName = ini.GetValue("ResponseRule", "m_strPassWordRuleName", NULL);

		int v_optype = ini.GetLongValue("optype", "optype", 0);
		int v_OutTimeJug = ini.GetLongValue("OutTime", "m_outTimeJug", 0);

		int v_filefp = ini.GetLongValue("LDFCR_MODULE", "m_bfilefp", 0);
		int v_svm = ini.GetLongValue("LDFCR_MODULE", "m_bsvm", 0);
		int v_filedb = ini.GetLongValue("LDFCR_MODULE", "m_bfiledb", 0);
		int v_filext = ini.GetLongValue("LDFCR_MODULE", "m_filext", 0);
		int v_ocr_embed = ini.GetLongValue("LDFCR_MODULE", "m_bocr_embed", 0);
		int v_ocr = ini.GetLongValue("LDFCR_MODULE", "m_bocr", 0);
		m_pwd_error = ini.GetLongValue("block_on_pwderror", "m_b_o_pwderror", 0);
		const char * password = ini.GetValue("block_on_pwderror", "password", NULL);
		const char * normalKey = ini.GetValue("AdvInitParam", "m_csNormalKey", NULL);
		int v_thread_count = ini.GetLongValue("thread", "m_threads", 0);

		//界面赋值
		this->m_strStrategyFilePath = path1;
		this->m_strFilePath4FCR = path2;
		this->m_strFolderPath4Fcr = path3;
		this->m_strShowLogPath = strShowLog;//1019
		this->m_strShowLogPath2 = strShowLog2;
		this->m_strTimeOutRespRulePath = strTimeOutRespRulePath;
		this->m_strPassWordRespRulePath = strPassWordRespRulePath;
		this->m_strTimeOutRulesName = strTimeOutRulesName;
		this->m_strPassWordRuleName = strPassWordRuleName;
		this->m_intOptype = v_optype;
		this->m_outTimeJug = v_OutTimeJug;
		this->m_password = password;
		this->m_csNormalKey = normalKey;
		this->m_thread_num.Format(L"%d", v_thread_count);

		if (v_filefp == 1)
		{
			((CButton *)GetDlgItem(IDC_CHECK3))->SetCheck(1);
			ldfcr_ControlModule(LDFCR_MODULE_FILEFP, 1);
		}
		else
		{
			ldfcr_ControlModule(LDFCR_MODULE_FILEFP, 0);
		}

		if (v_svm == 1)
		{
			((CButton *)GetDlgItem(IDC_CHECK4))->SetCheck(1);
			ldfcr_ControlModule(LDFCR_MODULE_SVM, 1);
		}
		else
		{
			ldfcr_ControlModule(LDFCR_MODULE_SVM, 0);
		}

		if (v_filedb == 1)
		{
			((CButton *)GetDlgItem(IDC_CHECK5))->SetCheck(1);
			ldfcr_ControlModule(LDFCR_MODULE_DBFP, 1);
		}
		else
		{
			ldfcr_ControlModule(LDFCR_MODULE_DBFP, 0);
		}

		if (v_filext == 1)
		{
			dlplog_info(g_log_handle, "[%s] Module LDFCR_MODULE_FILEXT enabled", __FUNCTION__);
			((CButton *)GetDlgItem(IDC_CHECK9))->SetCheck(1);
			//ldfcr_ControlModule(LDFCR_MODULE_OCR_EMBED, 1);20230804
			ldfcr_ControlModule(LDFCR_MODULE_FILEXT, 1);
		}
		else
		{
			dlplog_info(g_log_handle, "[%s] Module LDFCR_MODULE_FILEXT disabled", __FUNCTION__);
			dlplog_debug(g_log_handle, "Module LDFCR_MODULE_FILEXT disabled");
			ldfcr_ControlModule(LDFCR_MODULE_FILEXT, 0);
		}

		if (v_ocr_embed == 1)
		{
			dlplog_info(g_log_handle, "[%s] Module LDFCR_MODULE_OCR_EMBED enabled", __FUNCTION__);
			((CButton *)GetDlgItem(IDC_CHECK7))->SetCheck(1);
			//ldfcr_ControlModule(LDFCR_MODULE_OCR, 1);20230804
			ldfcr_ControlModule(LDFCR_MODULE_OCR_EMBED, 1);
		}
		else
		{
			dlplog_info(g_log_handle, "[%s] Module LDFCR_MODULE_OCR_EMBED disabled", __FUNCTION__);
			ldfcr_ControlModule(LDFCR_MODULE_OCR_EMBED, 0);
		}

		if (v_ocr == 1)
		{
			dlplog_info(g_log_handle, "[%s] Module LDFCR_MODULE_OCR enabled", __FUNCTION__);
			((CButton *)GetDlgItem(IDC_CHECK8))->SetCheck(1);
			//ldfcr_ControlModule(LDFCR_MODULE_FILEXT, 1);20230804
			ldfcr_ControlModule(LDFCR_MODULE_OCR, 1);
		}
		else
		{
			dlplog_info(g_log_handle, "[%s] Module LDFCR_MODULE_OCR disabled", __FUNCTION__);
			ldfcr_ControlModule(LDFCR_MODULE_OCR, 0);
		}
		if (m_pwd_error == true)
		{
			((CButton *)GetDlgItem(IDC_CHECK1))->SetCheck(1);
		}


	}
	//阻断时间未初始化
	m_time_input = this->m_outTimeJug;

	const wchar_t *v_csStrategy = NULL; //

										//读取策略信息（从文件中读出一个字符串）
										//注意：原始文件中的策略信息utf8编码的
	do {
		struct _stat st;
		int ret = _wstat(m_strStrategyFilePath.operator LPCTSTR(), &st);
		if ((0 != ret) || (_S_IFREG != (_S_IFREG & st.st_mode)))
		{
			//创建文件
			ofstream myfile;
			myfile.open(this->m_strStrategyFilePath, ios::out | ios::app);
			myfile.close();
		}
		if ((0 != ret) || (_S_IFREG != (_S_IFREG & st.st_mode)))
		{
			ini.SetValue("path0", "m_strStrategyFilePath", _str_str1);//写入选择的路径
			ini.SetValue("path1", "m_strFilePath4FCR", _str_str2);//写入选择的路径
			ini.SetValue("showLog", "m_strShowLogPath", _str_str3);//写入选择日志的路径  1019
			ini.SetValue("showLog", "m_strShowLogPath2", _str_str7);//写入选择日志的路径
			ini.SetValue("path2", "m_strFolderPath4Fcr", _str_str4);//写入选择的路径
			ini.SetValue("ResponseRule", "m_strTimeOutRespRulePath", _str_str5);//写入选择的超时响应规则路径
			ini.SetValue("ResponseRule", "m_strPassWordRespRulePath", _str_str8);//写入选择的带密码响应规则路径
			ini.SaveFile(_str, 0);//保存ini文件

			this->m_strStrategyFilePath = wcsAppDir;    //构造策略文件的路径
			this->m_strStrategyFilePath += L"\\Strategy.data";

			this->m_strFilePath4FCR = wcsAppDir;
			this->m_strFilePath4FCR += L"\\SampleFcr.txt";

			this->m_strShowLogPath = wcsAppDir;			//日志文件的路径 1019
			this->m_strShowLogPath += L"\\ldfcr.log";

			this->m_strShowLogPath2 = wcsAppDir;		
			this->m_strShowLogPath2 += L"\\ldfcrTester.log";

			this->m_strFolderPath4Fcr = wcsAppDir;
			this->m_strFolderPath4Fcr += L"";

			this->m_strTimeOutRespRulePath = wcsAppDir;
			this->m_strTimeOutRespRulePath += L"\\TimeOutRule.json";

			this->m_strPassWordRespRulePath = wcsAppDir;
			this->m_strPassWordRespRulePath += L"\\PassWordRule.json";

			MessageBox(L"策略文件不存在，无法加载策略信息,加载空策略");

			//MessageBox(L" 加载默认策略成功");
			break;
		}

		//加载策略文件
		int iStrategyLen = st.st_size;
		char* strategy_utf8str = new char[iStrategyLen + 1];
		FILE* fp = _wfopen(this->m_strStrategyFilePath.operator LPCTSTR(), L"rb");
		if (NULL == fp)
		{
			MessageBox(L"打开策略文件失败，无法加载策略信息！");
			break;
		}

		int iReadLen = fread(strategy_utf8str, sizeof(char), iStrategyLen, fp);
		if (iReadLen != iStrategyLen)
		{
			MessageBox(L"读取策略文件出错，返回");
			break;
		}

		strategy_utf8str[iStrategyLen] = 0; //设置字符串结尾符

											//创建

											//strategy_sstr->set_fromUTF8(
											//    trcrt::str::IString::UTF16, 
											//    strategy_utf8str, iStrategyLen
											//    );
		//strategy_sstr.convertFromUtf8(strategy_utf8str);20230807
		//v_csStrategy = strategy_sstr.wstr();20230807
		//strategy_wUtf8str = Utf8ToUnicode(strategy_utf8str);//20230807  2023 8 17弃用W接口
		m_strStrategyMerge = strategy_utf8str;
		

		fclose(fp);
		fp = NULL;


	} while (false);

	/////////////////////////////////////////////////////////////////
	//设置策略
	/*if (v_csStrategy != NULL && *v_csStrategy != '\0')	20230807
	{
		this->m_strStrategy = v_csStrategy;
	}*/
	//if (!strategy_wUtf8str.empty())//20230807 2023 8 17弃用W接口
	//{
	//	this->m_strStrategy = strategy_wUtf8str.c_str();
	//}

	if (ldfcr_InitStartup())
	{
		if (m_InitChosen == PointMoudle)
		{
			ldfcr_CreateInstance((void **)&m_pILDFcr); //5.17
		}
		//ldfcr_CreateInstance(&m_pILDFcr);
	}
	else
	{
		MessageBox(L"文件内容识别组件初始化失败！");
		return FALSE;
	}
	if (m_InitChosen == PointMoudle)
	{
		if (NULL == m_pILDFcr) //5.17
		{
			MessageBox(L"创建识别接口失败！");
			return FALSE;
		}
	}

	BOOL bRet;
	//设置策略
	if (m_InitChosen == PointMoudle)
	{
		bRet = m_pILDFcr->updateStrategy(m_strStrategyMerge.c_str()); //8.17
	}
	else
	{
		bRet = ldfcr_UpdateStrategy(m_strStrategyMerge.c_str());
	}

	if (!bRet)
	{
		MessageBox(L"策略更新失败！");
		return FALSE;
	}

	// 获取当前安装程序的运行路径 
	// 2023 01 30 检查时发现这段代码 m_current_dir 不知道是怎么用的但是既然写在这里了就先不动他 。。 kai
	wchar_t temp[MAX_PATH] = {};	
	GetModuleFileNameW(GetModuleHandle(NULL), temp, MAX_PATH);
	ghc::filesystem::path fs_temp = temp;
	fs_temp = fs_temp.parent_path();
	m_current_dir = fs_temp;
	

	//2023 6 9 特地记录模式选择选项，为了适配linux版本ini配置文件
	ini.SetUnicode(true);
	CString cstrAppDir;
	cstrAppDir = GetProPath();
	CString cstr;
	cstr += cstrAppDir;
	cstr += L"\\Setting.ini";

	ini.LoadFile(cstr);//加载ini文件
					   //ini文件读取、写入、保存
	string str_mode;
	if (m_InitChosen == PointMoudle)
	{
		str_mode = "G";
	}
	else
	{
		str_mode = "P";
	}
	ini.SetValue("MODE", "mode", str_mode.c_str());//为了适配linux版本的ini配置文件新增模式记录
	ini.SaveFile(_str, 0);//保存ini文件

	this->UpdateData(FALSE);


	//下拉框获取ID和NAME
	m_ComboSelectIdName.SubclassDlgItem(IDC_COMBO_SELECT_ID_NAME, this);

	return TRUE;  // 除非将焦点设置到控件，否则返回 TRUE
}

void CldfcrTesterDlg::OnSysCommand(UINT nID, LPARAM lParam)
{
	if ((nID & 0xFFF0) == IDM_ABOUTBOX)
	{
		CAboutDlg dlgAbout;
		dlgAbout.DoModal();
	}
	else
	{
		CDialog::OnSysCommand(nID, lParam);
	}
}

// 如果向对话框添加最小化按钮，则需要下面的代码
//  来绘制该图标。对于使用文档/视图模型的 MFC 应用程序，
//  这将由框架自动完成。

void CldfcrTesterDlg::OnPaint()
{
	if (IsIconic())
	{
		CPaintDC dc(this); // 用于绘制的设备上下文

		SendMessage(WM_ICONERASEBKGND, reinterpret_cast<WPARAM>(dc.GetSafeHdc()), 0);

		// 使图标在工作区矩形中居中
		int cxIcon = GetSystemMetrics(SM_CXICON);
		int cyIcon = GetSystemMetrics(SM_CYICON);
		CRect rect;
		GetClientRect(&rect);
		int x = (rect.Width() - cxIcon + 1) / 2;
		int y = (rect.Height() - cyIcon + 1) / 2;

		// 绘制图标
		dc.DrawIcon(x, y, m_hIcon);
	}
	else
	{
		CDialog::OnPaint();
	}
}

//当用户拖动最小化窗口时系统调用此函数取得光标
//显示。
HCURSOR CldfcrTesterDlg::OnQueryDragIcon()
{
	return static_cast<HCURSOR>(m_hIcon);
}

/*
* 控件：查看策略
* 功能：选择策略路径后，点击控件可查看当前策略的内容
*/
void CldfcrTesterDlg::OnBnClickedBtnViewStrategy()
{
	this->UpdateData(TRUE);//更新策略
	CWnd *pWnd = GetDlgItem(IDC_VIEW_STGY);

	ini.SetUnicode(true);

	CString strMfc;

	// TODO: 在此添加控件通知处理程序代码
	CFile file(m_strStrategyFilePath, CFile::modeRead);

	int len = file.GetLength();//获取file文件中内容的长度；

	char *data = new char[len + 1];//定义一个存放数据的指针；

	memset(data, 0, len + 1);//   将已开辟内存空间 data的,长度为len+1首 个字节的值设为值 0

	file.Read(data, len);//读取文件内容并赋值给data;
						 /*utf8转GBK*/
	string _str;
	_str = Utf8ToGbk(data);
	strMfc = _str.c_str();
	//创建非模态 注意内存泄漏，创建delete
	viewStgy* dlgView = new viewStgy(strMfc);
	BOOL bRet = dlgView->Create(IDD_DIALOG_VIEW);
	if (bRet)
	{
		dlgView->ShowWindow(SW_SHOW);
	}
	//创建模态
	//dlgView.DoModal();
	//MessageBox(strMfc);//弹出消息框，并显示data的值；
}

/*
* 控件：更新策略
* 功能：选择路径后，点击按钮，即可更换检查时使用的策略文件
*		 如果策略文件不存在，则提示不存在；打开失败，则提示打开失败；
*		 打开策略文件后，进行检查，有错误便提示错误；策略文件正常，进行策略设置
*/
void CldfcrTesterDlg::OnBnClickedBtnUpdateStrategy()
{
	//trcrt::str::SafeString  strategy_sstr;
	//trcrt::str::tr_str_initString(&strategy_sstr);
	//tr::str::CUnicodeString strategy_sstr;20230807


	const wchar_t *v_csStrategy = NULL; //
	do {
		struct _stat st;
		int ret = _wstat(m_strStrategyFilePath.operator LPCTSTR(), &st);
		if ((0 != ret) || (_S_IFREG != (_S_IFREG & st.st_mode)))
		{

			MessageBox(L"策略文件不存在，无法加载策略信息！");
			break;
		}

		//加载策略文件
		int iStrategyLen = st.st_size;
		char* strategy_utf8str = new char[iStrategyLen + 1];
		FILE* fp = _wfopen(m_strStrategyFilePath.operator LPCTSTR(), L"rb");
		if (NULL == fp)
		{
			MessageBox(L"打开策略文件失败，无法加载策略信息！");
			break;
		}

		int iReadLen = fread(strategy_utf8str, sizeof(char), iStrategyLen, fp);
		if (iReadLen != iStrategyLen)
		{
			MessageBox(L"读取策略文件出错，返回");
			break;
		}

		strategy_utf8str[iStrategyLen] = 0; //设置字符串结尾符

											//创建

											//strategy_sstr->set_fromUTF8(
											//	trcrt::str::IString::UTF16,
											//	strategy_utf8str, iStrategyLen
											//);
		//strategy_sstr.convertFromUtf8(strategy_utf8str);20230807
		//v_csStrategy = strategy_sstr.wstr();20230807
		//strategy_wUtf8str = Utf8ToUnicode(strategy_utf8str);//20230807 
		m_strStrategyMerge = strategy_utf8str;

		fclose(fp);
		fp = NULL;

	} while (false);

	//设置策略
	/*if (v_csStrategy != NULL && *v_csStrategy != '\0')	//20230807
	{
		this->m_strStrategy = v_csStrategy;
	}*/
	//if (!strategy_wUtf8str.empty())//20230807 2023 8 17弃用W接口
	//{
	//	this->m_strStrategy = strategy_wUtf8str.c_str();
	//}
	if (m_InitChosen == PointMoudle)
	{
		if (nullptr == m_pILDFcr)
		{
			if (ldfcr_InitStartup())
			{
				//ldfcr_CreateInstance(&m_pILDFcr);
				ldfcr_CreateInstance((void **)&m_pILDFcr);
			}
			else
			{
				MessageBox(L"文件内容识别组件初始化失败！");
				return;
			}
		}
		if (NULL == m_pILDFcr)
		{
			MessageBox(L"创建识别接口失败！");
			return;
		}
	}


	//设置策略
	BOOL bRet;
	//char s[256] = { 0 };
	//设置策略
	if (m_InitChosen == PointMoudle)
	{
		bRet = m_pILDFcr->updateStrategy(m_strStrategyMerge.c_str()); //8.17
	}
	else
	{
		bRet = ldfcr_UpdateStrategy(m_strStrategyMerge.c_str());
	}

	if (!bRet | (m_strStrategyFilePath.Compare(L"") == 0))
	{
		MessageBox(L"策略更新失败！");
		return;
	}
	else
	{
		MessageBox(L"策略更新成功！");
		//CStringA stra((TCHAR*)(LPCTSTR)(this->m_strStrategy)); 2023 8 17
		//std::string str((char *)(LPCSTR)(stra));
		dlplog_debug(g_log_handle, "[%s]new strategy : %s", __FUNCTION__, m_strStrategyMerge.c_str());
	}

	/*
	*  获取策略ID模块
	*/

	//清空idNameMap
	idNameMap.clear();

	//获取编辑框中的文件路径
	CString filePath;
	GetDlgItemText(IDC_STRATEGY_FILEPATH, filePath);

	//提取策略ID和NAME
	ExtractIdWithName(filePath, idNameMap);
	//清空下拉框
	m_ComboSelectIdName.ResetContent();

	//将策略ID和NAME添加到下拉框
	for (const auto& pair : idNameMap)
	{
		CString idString;
		CString idNameString;
		//格式化策略ID
		idString.Format(L"%d", pair.first);

		//转换策略ID名称为Unicode
		wstring unicode_strategy_name = Utf8ToUnicode(pair.second);

		//格式化策略名称
		idNameString.Format(L"%d-%s", pair.first, unicode_strategy_name.c_str());
		int indexIdName = m_ComboSelectIdName.AddString(idNameString);
		m_ComboSelectIdName.SetItemData(indexIdName, pair.first);
		//设置默认第一个选项
		m_ComboSelectIdName.SetCurSel(0);
	}

	//更新下拉框选择
	OnCbnSelchangeComboSelectIdName();
}

BOOL CldfcrTesterDlg::DestroyWindow()
{
	// TODO: 在此添加专用代码和/或调用基类
	if (NULL != m_pILDFcr)
	{
		m_pILDFcr->Release();
		m_pILDFcr = NULL;
	}
	ldfcr_StopRelease();

	return CDialog::DestroyWindow();
}

/*
* 控件：选择待检测内容的文件
* 功能：可自行选择需要检测的文件，支持选择所有文件内容。选择成功后，将路径保存到ini文件中。
*/
void CldfcrTesterDlg::OnBnClickedBtnSelectFile()
{
	// TODO: 在此添加控件通知处理程序代码
	CString strInitialDir;
	m_CFcrEdit.GetWindowText(strInitialDir);
	CString initialPath;

	if (!strInitialDir.IsEmpty())
	{
		// 如果编辑框中的路径不为空，将其作为初始路径
		initialPath = strInitialDir;
	}
	else
	{
		// 如果编辑框中的路径为空，将.exe文件所在的目录作为初始路径
		char exeDir[MAX_PATH];
		trcrt::app::tr_app_get_app_directory(exeDir, MAX_PATH);
		initialPath = CString(exeDir);
	}

	//CString strFile = _T("R:\\");
	CFileDialog   dlgFile(TRUE, _T("txt"), NULL, OFN_HIDEREADONLY, _T("All Files (*.*)|*.*||"), NULL);
	//dlgFile.m_ofn.lpstrInitialDir = _T("C:\\Users\\<USER>\\Desktop\\log(3)\\"); //指定文件夹	1025
	dlgFile.m_ofn.lpstrInitialDir = initialPath;//1025
	if (dlgFile.DoModal())
	{
		strInitialDir = dlgFile.GetPathName();
	}

	if (strInitialDir.IsEmpty())
	{
		return;
	}

	m_strFilePath4FCR = strInitialDir;
	m_strFilePath4FCRMerge = WstringToString(m_strFilePath4FCR.GetString());
	this->UpdateData(FALSE);

	ini.SetUnicode(true);
	CString wcsAppDir;
	wcsAppDir = GetProPath();
	CString _str;
	_str += wcsAppDir;
	_str += L"\\Setting.ini";

	USES_CONVERSION;//声明标识
	const char* _str_str = T2A(_str);

	string str1 = Utf8ToGbk(_str_str);

	//ini文件读取、写入、保存
	const char* strpath = T2A(m_strFilePath4FCR);
	ini.LoadFile(_str);//加载ini文件
	ini.SetValue("path1", "m_strFilePath4FCR", strpath);//写入选择的路径
	ini.SaveFile(_str, 0);//保存ini文件

}

/*
* 控件：检测文件
* 功能：执行单个文件的内容识别，根据所选择的策略文件，对选中的待检测文件进行检查。如果待检测文件中
*		 含有敏感词汇等，则进行提示；可对所有类型的文件进行检测。
*/
void CldfcrTesterDlg::OnBnClickedBtnExecFcrSinglefile()
{
	// TODO: 在此添加控件通知处理程序代码
	this->UpdateData(TRUE);

	if ((m_intOptype < 0) || (m_intOptype > 256))
	{
		MessageBox(_T("数值超过范围，清输入0~256内的数字"), _T("错误"), MB_OK);
		return;
	}

	//init moudle
	OnBnClickedSetMoudle();

	if (0 >= this->m_iFcrCount)
	{
		return;
	}

	if (m_InitChosen == PointMoudle)
	{
		if (NULL == m_pILDFcr)
		{
			return;
		}
	}

	BOOL bIsSensitive = FALSE;

	FCRPARAM fcrParam;

	memset(&fcrParam, 0, sizeof(fcrParam));

	fcrParam.interrupt = 0;
	fcrParam.block_on_timeout = 0;	//超时是否阻断
	fcrParam.timeout = m_time_input;		//超时时间
	fcrParam.block_on_pwderror = m_pwd_error;  //设置密码错误是否返回 2023 Q1

											   //by Kai 接口好像没用
											   //fcrParam.disableTEModules = m_ldfcr_moudle;
	CString string_temp;
	m_edit_password.GetWindowTextW(string_temp);
	string password;
	CStringCoder::UnicodeToUTF8(string_temp.GetBuffer(), password);

	string strJson = ("{\"password\": [\"" + password + "\"]}");

	if (m_InitChosen == PointMoudle)
	{
		m_pILDFcr->SetFilterGlobalParamer(strJson.c_str()); //2023 Q1
	}


	if (!m_time_input)
	{
		fcrParam.block_on_timeout = 0;//超时是否阻断
	}
	else
	{
		fcrParam.block_on_timeout = 1;
	}


	fcrParam.devType = 1;			//设备类型
	fcrParam.opType = m_intOptype;			//操作类型
	fcrParam.use_all_rule = TRUE;   //启用所有规则
	fcrParam.target_class_code = 3; //达到或超过代码3，即停止
	fcrParam.output_detail = TRUE;  //要求返回时输出策略的细节信息
	fcrParam.fcrInfo = "{\"IP\":\"***********\"}";
	m_strFilePath4FCRMerge = GbkToUtf8(WstringToString(m_strFilePath4FCR.GetBuffer()).c_str());
	int curSelectStrategyId = m_ComboSelectIdName.GetCurSel();
	if (curSelectStrategyId == -1)
	{
		curSelectStrategyId = 0;  //没有更新策略的状态默认就启用所有
		dlplog_warn(g_log_handle, "no update strategy");
	} 
	dlplog_info(g_log_handle, "The current detected strategy ID is [%d]", curSelectStrategyId);
	if (curSelectStrategyId != 0)  //选用状态就启用单策略检测 strategyIdsCount == 1
	{
		fcrParam.strategyIdsArray = new UINT64;
		fcrParam.strategyIdsCount = 1;  //设置检测策略数量为1
		*fcrParam.strategyIdsArray = curSelectStrategyId;           //2023 8 8 选择检测规则 use_all_rule改为false
		string strStrategyName = idNameMap[curSelectStrategyId];
		dlplog_info(g_log_handle, "strategy name is [%s]", Utf8ToGbk(strStrategyName.c_str()).c_str());
	}
	DWORD dwTick = GetTickCount();
	for (int i = 0; i < m_iFcrCount; i++)
	{
		IFCRResult*  result_p = NULL;
		IFCRDripResult* drip_result_p = NULL;
		BOOL res = 0;
		if (m_InitChosen == PointMoudle)
		{
			res = m_pILDFcr->executeFCR(
				this->m_strFilePath4FCRMerge.c_str(),
				&fcrParam,
				(void**)&result_p,
				(void**)&drip_result_p
				//NULL
			);
		}
		else
		{
			res = ldfcr_ExecuteFCR(
				this->m_strFilePath4FCRMerge.c_str(),
				&fcrParam,
				(void**)&result_p,
				(void**)&drip_result_p
				//NULL
			);
		}
		//大文件崩溃,executeFCRW返回0
		std::string strStrategyIds;
		std::string strText;
		int textSize = 0;

		//dlplog_info(g_log_handle,
		//	"<OnBnClickedBtnExecFcrSinglefile> 这里是过程1[result_p]");

		//bool haspassword = result_p->hasPassword();
		//if (haspassword == true)
		//{
		//	if (password.empty())
		//	{
		//		// TODO: 在此添加控件通知处理程序代码
		//		MessageBox(TEXT("文件已被加密，需要输入密码"), TEXT("温馨提示"), MB_OK);
		//		return;
		//	}
		//}

		if (NULL != result_p)
		{
			////
			//result_p->moveFirstPart();
			//while (NULL != result_p->moveNextPart())
			//{
			//}

			/////

			result_p->moveFirstPart();
			ICRPart * icrpaet = result_p->moveNextPart();
			if (NULL != icrpaet)
			{
				dlplog_info(g_log_handle, ("常规检测"));
				int i3 = icrpaet->getClassCode();
				string filename(WstringToString(this->m_strFilePath4FCR.GetString()));
				string gbTextTrimed = toBase64(Utf8ToGbk(icrpaet->getTextTrimed()).c_str());
				string gbStrategyMatched = Utf8ToGbk(icrpaet->getStrategyMatched());
				dlplog_info(g_log_handle, ("文件名: " + filename).c_str());
				dlplog_info(g_log_handle, ("分级代码: " + to_string(i3)).c_str());
				dlplog_info(g_log_handle, gbTextTrimed.c_str());
				dlplog_info(g_log_handle, gbStrategyMatched.c_str());
			}


			strStrategyIds = result_p->GetAdvancedStrategyIdsFCR();
			textSize = result_p->GetTextSize();
			std::string _strText;
			result_p->moveFirstTextPart();
			do
			{
				_strText = std::move(result_p->GetNextTextPart());
				if (_strText.length()>0)
				{
					strText.append(_strText);
				}

			} while (_strText.length()>0);

			//dlplog_info(g_log_handle,
			//	"<OnBnClickedBtnExecFcrSinglefile> 这里是过程2[result_p]");

			result_p->Release();
			result_p = NULL;
		}

		////
		//dlplog_info(g_log_handle,
		//	"<OnBnClickedBtnExecFcrSinglefile> 这里是过程1[drip_result_p]");

		if (NULL != drip_result_p)
		{
			drip_result_p->moveFirstIncident();
			ICRIncident *incident = NULL;
			while ((incident = drip_result_p->moveNextIncident()) != NULL)
			{
				if (strlen(incident->getStrategyName()) <= 0)
				{
					return;
				}
				incident->moveFirstMatch();
				ICRMatch *match = NULL;
				dlplog_info(g_log_handle, ("零星检测"));
				while ((match = incident->moveNextMatch()) != NULL)
				{
					if (strlen(match->getRuleName()) <= 0)
					{
						return;
					}
					int i3 = match->getRuleId();
					string RuleName = Utf8ToGbk(match->getRuleName());
					string filename(WstringToString(this->m_strFilePath4FCR.GetString()));
					string gbTextTrimed = toBase64(Utf8ToGbk(match->getTextTrimed()).c_str());
					string gbStrategyMatched = Utf8ToGbk(match->getFCRInfo());


					dlplog_info(g_log_handle, ("文件名: " + filename).c_str());
					dlplog_info(g_log_handle, ("匹配规则ID: " + to_string(i3) + " 规则名称 : " + RuleName.c_str()).c_str());
					dlplog_info(g_log_handle, gbTextTrimed.c_str());
					dlplog_info(g_log_handle, gbStrategyMatched.c_str());
				}
			}
			//int n = 0;
			//for (int i = 0; i < 3; i++)
			//{
			//	n++;
			//}

			//dlplog_info(g_log_handle,
			//	"<OnBnClickedBtnExecFcrSinglefile> 这里是过程2[drip_result_p]");

			drip_result_p->Release();
			drip_result_p = NULL;
		}


		bIsSensitive |= res;
	}
	dwTick = GetTickCount() - dwTick;

	m_strSingleFcrTime.Format(L"%8.3f", (double)dwTick / 1000);
	this->UpdateData(FALSE);

	//dlplog_info(g_log_handle,
	//	"<OnBnClickedBtnExecFcrSinglefile> 这里是过程3");

	if (bIsSensitive)
	{
		MessageBox(L"检测为敏感文件！");
	}
	else
	{
		MessageBox(L"检测无敏感内容.");
	}
	delete fcrParam.strategyIdsArray;  //释放指针

	ini.SetUnicode(true);
	CString wcsAppDir;
	wcsAppDir = GetProPath();
	CString _str;
	_str += wcsAppDir;
	_str += L"\\Setting.ini";

	USES_CONVERSION;//声明标识
	const char* _str_str = T2A(_str);

	string str1 = Utf8ToGbk(_str_str);

	//ini文件读取、写入、保存
	//const char* strpath = T2A(m_intOptype);
	char m_itc[10];
	char m_iti[10];
	sprintf(m_itc, "%d", m_intOptype);//保存操作类型
	sprintf(m_iti, "%d", m_outTimeJug);//保存阻断时间
	ini.LoadFile(_str);//加载ini文件
	ini.SetValue("optype", "optype", m_itc);//写入选择的路径
	ini.SetValue("outTime", "m_outTimeJug", m_iti);//写入选择的路径
	ini.SetValue("block_on_pwderror", "password", password.c_str());//写入选择的路径
	ini.SaveFile(_str, 0);//保存ini文件
}


/*
 * 控件：检测文本	1026
 * 功能：执行单个文本的内容识别，根据所选择的策略文件，对选中的待检测文本进行检查。如果待检测文本中
 *		 含有敏感词汇等，则进行提示；可对所有类型的文本进行检测。
 */
void CldfcrTesterDlg::OnBnClickedBtnExecTcrSingletext()
{
	// TODO: 在此添加控件通知处理程序代码
	this->UpdateData(TRUE);

	if ((m_intOptype < 0) || (m_intOptype > 256))
	{
		MessageBox(_T("数值超过范围，清输入0~256内的数字"), _T("错误"), MB_OK);
		return;
	}

	//init moudle
	OnBnClickedSetMoudle();

	if (0 >= this->m_iTcrCount)
	{
		return;
	}

	if (m_InitChosen == PointMoudle)
	{
		if (NULL == m_pILDFcr)
		{
			return;
		}
	}
	
	BOOL bIsSensitive = FALSE;

	FCRPARAM fcrParam;

	memset(&fcrParam, 0, sizeof(fcrParam));

	fcrParam.interrupt = 0;
	fcrParam.block_on_timeout = 0;	//超时是否阻断
	fcrParam.timeout = m_time_input;		//超时时间
	fcrParam.block_on_pwderror = m_pwd_error;  //设置密码错误是否返回 

	CString string_temp;
	m_edit_password.GetWindowTextW(string_temp);
	string password;
	CStringCoder::UnicodeToUTF8(string_temp.GetBuffer(), password);

	string strJson = ("{\"password\": [\"" + password + "\"]}");

	if (m_InitChosen == PointMoudle)
	{
		m_pILDFcr->SetFilterGlobalParamer(strJson.c_str()); 
	}

	if (!m_time_input)
	{
		fcrParam.block_on_timeout = 0;//超时是否阻断
	}
	else
	{
		fcrParam.block_on_timeout = 1;
	}
	
	fcrParam.devType = 1;			//设备类型
	fcrParam.opType = m_intOptype;			//操作类型
	fcrParam.use_all_rule = TRUE;   //启用所有规则
	fcrParam.target_class_code = 3; //达到或超过代码3，即停止
	fcrParam.output_detail = TRUE;  //要求返回时输出策略的细节信息
	fcrParam.fcrInfo = "{\"IP\":\"***********\"}";
	m_strFilePath4FCRMerge = WstringToString(m_strFilePath4FCR.GetBuffer()).c_str();
	int curSelectStrategyId = m_ComboSelectIdName.GetCurSel();
	if (curSelectStrategyId == -1)
	{
		curSelectStrategyId = 0;  //没有更新策略的状态默认就启用所有
		dlplog_warn(g_log_handle, "no update strategy");
	}
	dlplog_info(g_log_handle, "The current detected strategy ID is [%d]", curSelectStrategyId);
	if (curSelectStrategyId != 0)  //选用状态就启用单策略检测 strategyIdsCount == 1
	{
		fcrParam.strategyIdsArray = new UINT64;
		fcrParam.strategyIdsCount = 1;  //设置检测策略数量为1
		*fcrParam.strategyIdsArray = curSelectStrategyId;          
		string strStrategyName = idNameMap[curSelectStrategyId];
		dlplog_info(g_log_handle, "strategy name is [%s]", Utf8ToGbk(strStrategyName.c_str()).c_str());
	}
	DWORD dwTick = GetTickCount();
	
	for (int i = 0; i < m_iTcrCount; i++)
	{
		IFCRResult*  result_p = NULL;
		IFCRDripResult* drip_result_p = NULL;
		BOOL res = 0;

		//读取文本内容
		std::string strTextContent = "";
		if (ReadTextContent(m_strFilePath4FCRMerge.c_str(), strTextContent))
		{
			if (m_InitChosen == PointMoudle)
			{
				res = m_pILDFcr->executeTCR(
					strTextContent.c_str(), -1,
					&fcrParam,
					(void**)&result_p,
					(void**)&drip_result_p
				);
			}
			else
			{
				res = ldfcr_ExecuteTCR(
					strTextContent.c_str(), -1,
					&fcrParam,
					(void**)&result_p,
					(void**)&drip_result_p
				);
			}
		}
		else  //路径不正确文件打开失败测试空值 2023 11 8
		{
			if (m_InitChosen == PointMoudle)
			{
				res = m_pILDFcr->executeTCR(
					strTextContent.c_str(), -1,
					&fcrParam,
					(void**)&result_p,
					(void**)&drip_result_p
				);
			}
			else
			{
				res = ldfcr_ExecuteTCR(
					strTextContent.c_str(), -1,
					&fcrParam,
					(void**)&result_p,
					(void**)&drip_result_p
				);
			}
		}
		
		//大文件崩溃,executeFCRW返回0
		std::string strStrategyIds;
		std::string strText;
		int textSize = 0;

		if (NULL != result_p)
		{
			result_p->moveFirstPart();
			ICRPart * icrpaet = result_p->moveNextPart();
			if (NULL != icrpaet)
			{
				dlplog_info(g_log_handle, ("常规检测"));
				int i3 = icrpaet->getClassCode();
				string textname(WstringToString(this->m_strFilePath4FCR.GetString()));
				string gbTextTrimed = toBase64(Utf8ToGbk(icrpaet->getTextTrimed()).c_str());
				string gbStrategyMatched = Utf8ToGbk(icrpaet->getStrategyMatched());
				dlplog_info(g_log_handle, ("文本名: " + textname).c_str());
				dlplog_info(g_log_handle, ("分级代码: " + to_string(i3)).c_str());
				dlplog_info(g_log_handle, gbTextTrimed.c_str());
				dlplog_info(g_log_handle, gbStrategyMatched.c_str());
			}

			strStrategyIds = result_p->GetAdvancedStrategyIdsFCR();
			textSize = result_p->GetTextSize();
			std::string _strText;
			result_p->moveFirstTextPart();
			do 
			{
				_strText = std::move(result_p->GetNextTextPart());
				if (_strText.length()>0)
				{
					strText.append(_strText);
				}
			} while (_strText.length() > 0);

			result_p->Release();
			result_p = NULL;

		}

		if (NULL != drip_result_p)
		{
			drip_result_p->moveFirstIncident();
			ICRIncident *incident = NULL;
			while ((incident = drip_result_p->moveNextIncident()) != NULL)
			{
				if (strlen(incident->getStrategyName()) <= 0)
				{
					return;
				}
				incident->moveFirstMatch();
				ICRMatch *match = NULL;
				dlplog_info(g_log_handle, ("零星检测"));
				while ((match = incident->moveNextMatch()) != NULL)
				{
					if(strlen(match->getRuleName())<=0)
					{
						return;
					}
					int i3 = match->getRuleId();
					string RuleName = Utf8ToGbk(match->getRuleName());
					string textname(WstringToString(this->m_strFilePath4FCR.GetString()));
					string gbTextTrimed = toBase64(Utf8ToGbk(match->getTextTrimed()).c_str());
					string gbStrategyMatched = Utf8ToGbk(match->getFCRInfo());

					dlplog_info(g_log_handle, ("文本名: " + textname).c_str());
					dlplog_info(g_log_handle, ("匹配规则ID: " + to_string(i3) + " 规则名称 : " + RuleName.c_str()).c_str());
					dlplog_info(g_log_handle, gbTextTrimed.c_str());
					dlplog_info(g_log_handle, gbStrategyMatched.c_str());
				}
			}

			drip_result_p->Release();
			drip_result_p = NULL;
		}
		bIsSensitive |= res;
	}
	dwTick = GetTickCount() - dwTick;
	m_strSingleFcrTime.Format(L"%8.3f", (double)dwTick / 1000);

	this->UpdateData(FALSE);

	if (bIsSensitive)
	{
		MessageBox(L"检测为敏感文本！");
	}
	else
	{
		MessageBox(L"检测无敏感内容.");
	}
	delete fcrParam.strategyIdsArray;  //释放指针


	ini.SetUnicode(true);
	CString wcsAppDir;
	wcsAppDir = GetProPath();
	CString _str;
	_str += wcsAppDir;
	_str += L"\\Setting.ini";

	USES_CONVERSION;//声明标识
	const char* _str_str = T2A(_str);

	//ini文件读取、写入、保存
	char m_itc[10];
	char m_iti[10];
	sprintf(m_itc, "%d", m_intOptype);//保存操作类型
	sprintf(m_iti, "%d", m_outTimeJug);//保存阻断时间
	ini.LoadFile(_str);//加载ini文件
	ini.SetValue("optype", "optype_single_t", m_itc);//写入选择的路径
	ini.SetValue("outTime", "m_outTimeJug_single_t", m_iti);//写入选择的路径
	ini.SetValue("block_on_pwderror", "password", password.c_str());//写入选择的路径
	ini.SaveFile(_str, 0);//保存ini文件
}

void CldfcrTesterDlg::OnBnClickedBtnSelectFolder()
{
	// TODO: 在此添加控件通知处理程序代码
	CString strInitialDir;
	m_CFolderPath4FcrEdit.GetWindowText(strInitialDir);
	CString initialPath;

	// 编辑框路径不为空
	
	if (!strInitialDir.IsEmpty())
	{
		initialPath = strInitialDir;
	}
	// 路径为空或不存在，使用.exe文件所在目录
	else
	{		
		char exeDir[MAX_PATH];
		trcrt::app::tr_app_get_app_directory(exeDir, MAX_PATH);
		initialPath = CString(exeDir);
		strInitialDir = initialPath;
	}

	//CString  initFolder = L"";
	CXFolderDialog dlg(strInitialDir);
	dlg.m_ofn.lpstrInitialDir = initialPath;
	dlg.SetOsVersion((CXFolderDialog::XFILEDIALOG_OS_VERSION) 0);
	CXFolderDialog::XFILEDIALOG_OS_VERSION eVer = dlg.GetOsVersion();

	dlg.EnableRegistry(TRUE);

	dlg.SetViewMode(0);
	//dlg.SetTitle(_T("Select Folder"));	// defaults to "Select Folder"
	if (dlg.DoModal() == IDOK)//打开对话框
	{
		strInitialDir = dlg.GetPath();
		if (strInitialDir.IsEmpty() || !PathFileExists(strInitialDir))
		{
			MessageBox(L"无效目录！");
			return;
		}
		m_strFolderPath4Fcr = strInitialDir;
		this->UpdateData(FALSE);
	}
	
	//存写ini文件
	ini.SetUnicode(true);
	CString wcsAppDir;
	wcsAppDir = GetProPath();
	CString _str;
	_str += wcsAppDir;
	_str += L"\\Setting.ini";

	USES_CONVERSION;//声明标识
	const char* _str_str = T2A(_str);

	string str1 = Utf8ToGbk(_str_str);

	//ini文件读取、写入、保存
	const char* strpath = T2A(m_strFolderPath4Fcr);
	ini.LoadFile(_str);//加载ini文件
	ini.SetValue("path2", "m_strFolderPath4Fcr", strpath);//写入选择的路径
	ini.SaveFile(_str, 0);//保存ini文件
}

void g_DoEvent()
{
	MSG msg;
	while (PeekMessage(&msg, NULL, 0, 0, PM_REMOVE)) //非阻塞查看消息队列是否有消息过来，有消息返回非零进入循环
	{//无消息返回0则跳出
		TranslateMessage(&msg); //有消息时，虚拟键消息转换为字符消息
		DispatchMessage(&msg);//发送消息给窗口程序
	}
}

void CldfcrTesterDlg::doMatchTask()
{
	std::list<std::wstring>::iterator iterFileName;
	for (iterFileName = filename_list.begin(); iterFileName != filename_list.end(); iterFileName++)
	{
		const std::wstring& fileName = *iterFileName;
		std::wstring filePath = fld_path;

		if (filePath.size() - 1 != filePath.rfind('\\'))
		{
			filePath += L"\\";
		}
		filePath += fileName;

		int _count = 0;
		//检测文件
		if (this->dlp_checkSingleFile(filePath.c_str()))
		{
			file_count_sens++;
		}

		this->m_iFileCount = ++file_count_scaned;
		this->m_iSensFileCount = file_count_sens;

		g_DoEvent();    //驱动消息循环，防止界面卡死
		if (!m_bDoingDirFcr)
		{
			break;
		}
	}
}

/*
* 控件：检测目录文件
* 功能：//使用非递归，深度优先的方式遍历
*/
void CldfcrTesterDlg::OnBnClickedBtnExecFcrFolder()
{
	// TODO: 在此添加控件通知处理程序代码

	//init moudle
	OnBnClickedSetMoudle();

	m_filename_list.clear();
	if (m_bDoingDirFcr)
	{
		m_bDoingDirFcr = FALSE;
		this->SetDlgItemText(IDC_BTN_EXEC_FCR_FOLDER, L"开始检测文件");
		return;
	}

	m_bDoingDirFcr = TRUE;
	this->SetDlgItemText(IDC_BTN_EXEC_FCR_FOLDER, L"停止检测文件");

	m_file_count_scaned = 0;
	m_file_count_sens = 0;

	//线程启动
	int thread_max = atoi(WstringToString(m_thread_num.GetString()).c_str());
	if (thread_max < 1 || thread_max > 48)
	{
		MessageBox(L"线程开辟范围建议1到48");
		m_bDoingDirFcr = FALSE;
		this->SetDlgItemText(IDC_BTN_EXEC_FCR_FOLDER, L"开始检测文件");
		return;
	}
	dlplog_debug(g_log_handle, "threads FCR start , current threads init [%d]", thread_max);
	ThreadPool pool(thread_max);
	pool.init();


	WCHAR module_dir[MAX_PATH];
	::GetModuleFileNameW(NULL, module_dir, sizeof(module_dir) / sizeof(WCHAR));
	while ('\\' != module_dir[wcslen(module_dir) - 1])
	{
		module_dir[wcslen(module_dir) - 1] = '\0';
	}
	wcscat(module_dir, L"files.log");

	WCHAR log_dir[MAX_PATH];
	wcscpy(log_dir, module_dir);
	FILE* fp = _wfopen(log_dir, L"wt");
	//	int _err = GetLastError();
	if (NULL != fp)
	{
		_wsetlocale(0, L"chs");
	}

	this->UpdateData(TRUE);
	CDirNode* top_node_p = new CDirNode(m_strFolderPath4Fcr.operator LPCTSTR());
	CDirNode* cur_node_p = top_node_p;

	while (NULL != cur_node_p)
	{
		if (cur_node_p->isDealed())
		{//当前节点已经被处理过
			CDirNode* todel_p = cur_node_p;
			cur_node_p = cur_node_p->detach();
			todel_p->release();

			continue;
		}

		cur_node_p->setDealed(true); //标记已经处理
		if (!m_bDoingDirFcr) continue; //中途终止

		std::wstring fld_path = cur_node_p->getFullPath();

		//遍历当前文件夹，找出子目录和文件

		//std::list<std::wstring>  filename_list; //保存遍历出来的文件名列表
		std::wstring srch_path = fld_path + L"\\*";

		WIN32_FIND_DATAW  findata;
		HANDLE hFind = FindFirstFileW(srch_path.c_str(), &findata);
		if (INVALID_HANDLE_VALUE == hFind)
		{
			CDirNode* todel_p = cur_node_p;
			cur_node_p = cur_node_p->detach();
			todel_p->release();

			continue;
		}

		while (true)
		{
			std::wstring filename = findata.cFileName;
			if (L"." == filename || L".." == filename)
			{
			}
			else if (FILE_ATTRIBUTE_DIRECTORY == (FILE_ATTRIBUTE_DIRECTORY & findata.dwFileAttributes))
			{//是目录
				cur_node_p->addChild(filename);
			}
			else
			{//其他视为文件
				m_filename_list.push_back(fld_path + filename);
			}

			if (!FindNextFileW(hFind, &findata)) break;
		}

		FindClose(hFind);

		//如果有子目录节点，那么深度优先展开
		CDirNode* childs = cur_node_p->getChilds();
		if (NULL != childs)
		{
			cur_node_p = childs;
		}
	}

	// =======================================2023 11 2 多线程模式============================================

	auto startT = std::chrono::duration_cast<std::chrono::microseconds>(std::chrono::system_clock::now().time_since_epoch()).count();

	this->m_iFileCount = m_filename_list.size();
	int size = m_filename_list.size() + 1;  //防止爆栈

	dlplog_debug(g_log_handle, "size is [%d]\n", size - 1);

	int thread_index = 0;
	while (size != 0)
	{
		pool.submit(std::bind(&CldfcrTesterDlg::ldfcrTesterThreadFCR, this, thread_index));
		thread_index++;
		size--;
	}


	if (NULL != fp)
	{
		fclose(fp);
	}

	while (m_filename_list.size() != 0)
	{
		Sleep(1000);  //release 版本循环次数过多导致程序认为死循环 导致程序未响应进入死循环
	}
	this->m_iSensFileCount = m_file_count_sens;
	pool.shutdown();
	auto endT = std::chrono::duration_cast<std::chrono::microseconds>(std::chrono::system_clock::now().time_since_epoch()).count();
	double tim = (double)(endT - startT) / 1000000;
	this->m_tim_count.Format(_T("%.3f"), tim);
	this->UpdateData(FALSE);

	MessageBox(L"检测完毕！");
	m_bDoingDirFcr = FALSE;
	this->SetDlgItemText(IDC_BTN_EXEC_FCR_FOLDER, L"开始检测文件");

	ini.SetUnicode(true);
	CString wcsAppDir;
	wcsAppDir = GetProPath();
	CString _str;
	_str += wcsAppDir;
	_str += L"\\Setting.ini";

	USES_CONVERSION;//声明标识
	const char* _str_str = T2A(_str);

	string str1 = Utf8ToGbk(_str_str);

	//ini文件读取、写入、保存
	//const char* strpath = T2A(m_intOptype);
	char m_itc[10];
	char m_iti[10];
	char m_its[10];
	sprintf(m_itc, "%d", m_intOptype);//保存操作类型
	sprintf(m_iti, "%d", m_outTimeJug);//保存阻断时间
	sprintf(m_its, "%d", thread_max);//保存阻断时间
	ini.LoadFile(_str);//加载ini文件
	ini.SetValue("optype", "optype", m_itc);//写入选择的路径
	ini.SetValue("outTime", "m_outTimeJug", m_iti);//写入选择的路径
	ini.SetValue("thread", "m_threads", m_its);//写入线程数
	ini.SaveFile(_str, 0);//保存ini文件
}


/*
* 控件：检测文本
* 功能：//使用非递归，深度优先的方式遍历
*/
void CldfcrTesterDlg::OnBnClickedBtnExecTcrFolder()
{
	// TODO: 在此添加控件通知处理程序代码

	//init moudle
	OnBnClickedSetMoudle();

	m_filename_list.clear();

	if (m_bDoingDirTcr)
	{
		m_bDoingDirTcr = FALSE;
		this->SetDlgItemText(IDC_BTN_EXEC_TCR_FOLDER, L"开始检测文本");
		return;
	}

	m_bDoingDirTcr = TRUE;
	this->SetDlgItemText(IDC_BTN_EXEC_TCR_FOLDER, L"停止检测文本");

	m_file_count_scaned = 0;
	m_file_count_sens = 0;

	int thread_max = atoi(WstringToString(m_thread_num.GetString()).c_str());
	if (thread_max < 1 || thread_max > 48)
	{
		MessageBox(L"领导说了线程要开辟1到48");
		m_bDoingDirFcr = FALSE;
		this->SetDlgItemText(IDC_BTN_EXEC_FCR_FOLDER, L"开始检测文件");
		return;
	}
	dlplog_debug(g_log_handle, "threads TCR start , current threads init [%d]", thread_max);
	ThreadPool pool(thread_max);
	pool.init();

	WCHAR module_dir[MAX_PATH];
	::GetModuleFileNameW(NULL, module_dir, sizeof(module_dir) / sizeof(WCHAR));
	while ('\\' != module_dir[wcslen(module_dir) - 1])
	{
		module_dir[wcslen(module_dir) - 1] = '\0';
	}
	wcscat(module_dir, L"files.log");

	WCHAR log_dir[MAX_PATH];
	wcscpy(log_dir, module_dir);
	FILE* fp = _wfopen(log_dir, L"wt");
	if (NULL != fp)
	{
		_wsetlocale(0, L"chs");
	}

	this->UpdateData(TRUE);
	CDirNode* top_node_p = new CDirNode(m_strFolderPath4Fcr.operator LPCTSTR());
	CDirNode* cur_node_p = top_node_p;

	while (NULL != cur_node_p)
	{
		if (cur_node_p->isDealed())
		{//当前节点已经被处理过
			CDirNode* todel_p = cur_node_p;
			cur_node_p = cur_node_p->detach();
			todel_p->release();

			continue;
		}

		cur_node_p->setDealed(true); //标记已经处理
		if (!m_bDoingDirTcr) continue; //中途终止

		std::wstring fld_path = cur_node_p->getFullPath();

		//遍历当前文件夹，找出子目录和文本

		//std::list<std::wstring>  filename_list; //保存遍历出来的文件名列表
		std::wstring srch_path = fld_path + L"\\*";

		WIN32_FIND_DATAW  findata;
		HANDLE hFind = FindFirstFileW(srch_path.c_str(), &findata);
		if (INVALID_HANDLE_VALUE == hFind)
		{
			CDirNode* todel_p = cur_node_p;
			cur_node_p = cur_node_p->detach();
			todel_p->release();

			continue;
		}

		while (true)
		{
			std::wstring filename = findata.cFileName;
			if (L"." == filename || L".." == filename)
			{
			}
			else if (FILE_ATTRIBUTE_DIRECTORY == (FILE_ATTRIBUTE_DIRECTORY & findata.dwFileAttributes))
			{//是目录
				cur_node_p->addChild(filename);
			}
			else
			{//其他视为文件
				m_filename_list.push_back(fld_path + filename);
			}

			if (!FindNextFileW(hFind, &findata)) break;
		}

		FindClose(hFind);

		//如果有子目录节点，那么深度优先展开
		CDirNode* childs = cur_node_p->getChilds();
		if (NULL != childs)
		{
			cur_node_p = childs;
		}
	}

	// =======================================2023 11 7 多线程模式============================================

	auto startT = std::chrono::duration_cast<std::chrono::microseconds>(std::chrono::system_clock::now().time_since_epoch()).count();

	this->m_iFileCount = m_filename_list.size();
	int size = m_filename_list.size() + 1;  //防止爆栈
	dlplog_debug(g_log_handle, "size is [%d]\n", size - 1);
	int thread_index = 0;
	while (size != 0)
	{
		pool.submit(std::bind(&CldfcrTesterDlg::ldfcrTesterThreadTCR, this, thread_index));
		thread_index++;
		size--;
	}

	if (NULL != fp)
	{
		fclose(fp);
	}

	while (m_filename_list.size() != 0)
	{
		Sleep(1000);  //release 版本循环次数过多导致程序认为死循环 导致程序未响应进入死循环
	}
	this->m_iSensFileCount = m_file_count_sens;
	pool.shutdown();
	auto endT = std::chrono::duration_cast<std::chrono::microseconds>(std::chrono::system_clock::now().time_since_epoch()).count();
	double tim = (double)(endT - startT) / 1000000;
	this->m_tim_count.Format(_T("%.3f"), tim);
	this->UpdateData(FALSE);
	MessageBox(L"检测完毕！");
	m_bDoingDirTcr = FALSE;
	this->SetDlgItemText(IDC_BTN_EXEC_TCR_FOLDER, L"开始检测文本");

	ini.SetUnicode(true);
	CString wcsAppDir;
	wcsAppDir = GetProPath();
	CString _str;
	_str += wcsAppDir;
	_str += L"\\Setting.ini";

	USES_CONVERSION;//声明标识
	const char* _str_str = T2A(_str);

	string str1 = Utf8ToGbk(_str_str);

	//ini文件读取、写入、保存
	//const char* strpath = T2A(m_intOptype);
	char m_itc[10];
	char m_iti[10];
	char m_its[10];
	sprintf(m_itc, "%d", m_intOptype);//保存操作类型
	sprintf(m_iti, "%d", m_outTimeJug);//保存阻断时间
	sprintf(m_its, "%d", thread_max);//保存阻断时间
	ini.LoadFile(_str);//加载ini文件
	ini.SetValue("optype", "optype_t", m_itc);//写入选择的路径
	ini.SetValue("outTime", "m_outTimeJug_t", m_iti);//写入选择的路径
	ini.SetValue("thread", "m_threads", m_its);//写入线程数
	ini.SaveFile(_str, 0);//保存ini文件
}


BOOL CldfcrTesterDlg::dlp_checkSingleFile(const wchar_t* file_path)
{
	if (m_InitChosen == PointMoudle)
	{
		if (NULL == m_pILDFcr) return false;
	}

	string strFilepath = GbkToUtf8(WstringToString(file_path).c_str());
	FCRPARAM fcrParam;

	fcrParam.use_all_rule = TRUE;   //启用所有规则
	fcrParam.target_class_code = 3; //达到或超过代码3，即停止
	fcrParam.output_detail = TRUE;  //要求返回时输出策略的细节信息
	fcrParam.opType = m_intOptype;			//操作类型

	fcrParam.timeout = m_time_input;		//超时时间
	fcrParam.block_on_pwderror = m_pwd_error; //2023 Q1

											  //string s1 = "timeout 参数为" + to_string(m_time_input);
											  //dlplog_info(g_log_handle, s1.c_str());
											  //by Kai
	fcrParam.disableTEModules = m_intOptype;			//ldfcr_moudle模式

	if (!m_time_input)
	{
		fcrParam.block_on_timeout = 0;//超时是否阻断
	}
	else
	{
		fcrParam.block_on_timeout = 1;
	}

	IFCRResult*  result_p = NULL;
	IFCRDripResult* drip_result_p = NULL;

	BOOL bIsSensitive;
	if (m_InitChosen == PointMoudle)
	{
		bIsSensitive = m_pILDFcr->executeFCR(
			strFilepath.c_str(),
			&fcrParam,
			(void**)&result_p,
			(void**)&drip_result_p
			//NULL
		);
	}
	else
	{
		bIsSensitive = ldfcr_ExecuteFCR(
			strFilepath.c_str(),
			&fcrParam,
			(void**)&result_p,
			(void**)&drip_result_p
			//NULL
		);
	}

	if (NULL != result_p)
	{
		result_p->moveFirstPart();

		ICRPart * icrpaet = result_p->moveNextPart();
		if (NULL != icrpaet)
		{
			string filename = WstringToString(file_path);
			int i3 = icrpaet->getClassCode();
			string gbTextTrimed = toBase64(Utf8ToGbk(icrpaet->getTextTrimed()).c_str());
			string gbStrategyMatched = Utf8ToGbk(icrpaet->getStrategyMatched());
			dlplog_info(g_log_handle, ("文件名: " + filename).c_str());
			dlplog_info(g_log_handle, ("分级代码: " + to_string(i3)).c_str());
			dlplog_info(g_log_handle, gbTextTrimed.c_str());
			dlplog_info(g_log_handle, gbStrategyMatched.c_str());
		}

		result_p->Release();
		result_p = NULL;
	}

	if (NULL != drip_result_p)
	{
		drip_result_p->moveFirstIncident();
		ICRIncident *incident = NULL;
		while ((incident = drip_result_p->moveNextIncident()) != NULL)
		{
			if (strlen(incident->getStrategyName()) <= 0)
			{
				return false;
			}
			incident->moveFirstMatch();
			ICRMatch *match = NULL;
			dlplog_info(g_log_handle, ("零星检测"));
			while ((match = incident->moveNextMatch()) != NULL)
			{
				if (strlen(match->getRuleName()) <= 0)
				{
					return false;
				}
				int i3 = match->getRuleId();
				string RuleName = Utf8ToGbk(match->getRuleName());
				string filename(WstringToString(this->m_strFilePath4FCR.GetString()));
				string gbTextTrimed = toBase64(Utf8ToGbk(match->getTextTrimed()).c_str());
				string gbStrategyMatched = Utf8ToGbk(match->getFCRInfo());
				dlplog_info(g_log_handle, ("文件名: " + filename).c_str());
				dlplog_info(g_log_handle, ("匹配规则ID: " + to_string(i3) + " 规则名称 : " + RuleName.c_str()).c_str());
				dlplog_info(g_log_handle, gbTextTrimed.c_str());
				dlplog_info(g_log_handle, gbStrategyMatched.c_str());
			}
		}

		drip_result_p->Release();
		drip_result_p = NULL;
	}

	return bIsSensitive;
}

BOOL CldfcrTesterDlg::dlp_checkSingleText(const wchar_t* file_path)//1008
{
	if (m_InitChosen == PointMoudle)
	{
		if (NULL == m_pILDFcr) return false;
	}

	//读取文本内容
	std::string strTextContent = " ";
	std::string filepath = WstringToString(file_path);
	FCRPARAM fcrParam;

	fcrParam.use_all_rule = TRUE;   //启用所有规则
	fcrParam.target_class_code = 3; //达到或超过代码3，即停止
	fcrParam.output_detail = TRUE;  //要求返回时输出策略的细节信息
	fcrParam.opType = m_intOptype;	//操作类型

	fcrParam.timeout = m_time_input;		//超时时间
	fcrParam.block_on_pwderror = m_pwd_error;

	fcrParam.disableTEModules = m_intOptype;			//ldfcr_moudle模式

	if (!m_time_input)
	{
		fcrParam.block_on_timeout = 0;//超时是否阻断
	}
	else
	{
		fcrParam.block_on_timeout = 1;
	}

	IFCRResult*  result_p = NULL;
	IFCRDripResult* drip_result_p = NULL;

	BOOL bIsSensitive;
	if (ReadTextContent(filepath.c_str(), strTextContent))
	{
		if (m_InitChosen == PointMoudle)
		{
			bIsSensitive = m_pILDFcr->executeTCR(
				strTextContent.c_str(), -1,
				&fcrParam,
				(void**)&result_p,
				(void**)&drip_result_p
			);
		}
		else
		{
			bIsSensitive = ldfcr_ExecuteTCR(
				strTextContent.c_str(), -1,
				&fcrParam,
				(void**)&result_p,
				(void**)&drip_result_p
			);
		}
	}
	else
	{
		if (m_InitChosen == PointMoudle)
		{
			bIsSensitive = m_pILDFcr->executeTCR(
				strTextContent.c_str(), -1,
				&fcrParam,
				(void**)&result_p,
				(void**)&drip_result_p
			);
		}
		else
		{
			bIsSensitive = ldfcr_ExecuteTCR(
				strTextContent.c_str(), -1,
				&fcrParam,
				(void**)&result_p,
				(void**)&drip_result_p
			);
		}
	}
	if (NULL != result_p)
	{
		result_p->moveFirstPart();

		ICRPart * icrpaet = result_p->moveNextPart();
		if (NULL != icrpaet)
		{
			string filename = WstringToString(file_path);
			int i3 = icrpaet->getClassCode();
			string gbTextTrimed = toBase64(Utf8ToGbk(icrpaet->getTextTrimed()).c_str());
			string gbStrategyMatched = Utf8ToGbk(icrpaet->getStrategyMatched());
			dlplog_info(g_log_handle, ("文件名: " + filename).c_str());
			dlplog_info(g_log_handle, ("分级代码: " + to_string(i3)).c_str());
			dlplog_info(g_log_handle, gbTextTrimed.c_str());
			dlplog_info(g_log_handle, gbStrategyMatched.c_str());
		}

		result_p->Release();
		result_p = NULL;
	}

	if (NULL != drip_result_p)
	{
		drip_result_p->moveFirstIncident();
		ICRIncident *incident = NULL;
		while ((incident = drip_result_p->moveNextIncident()) != NULL)
		{
			if (strlen(incident->getStrategyName()) <= 0)
			{
				return false;
			}
			incident->moveFirstMatch();
			ICRMatch *match = NULL;
			dlplog_info(g_log_handle, ("零星检测"));
			while ((match = incident->moveNextMatch()) != NULL)
			{
				if (strlen(match->getRuleName()) <= 0)
				{
					return false;
				}
				int i3 = match->getRuleId();
				string RuleName = Utf8ToGbk(match->getRuleName());
				string filename(WstringToString(this->m_strFilePath4FCR.GetString()));
				string gbTextTrimed = toBase64(Utf8ToGbk(match->getTextTrimed()).c_str());
				string gbStrategyMatched = Utf8ToGbk(match->getFCRInfo());
				dlplog_info(g_log_handle, ("文件名: " + filename).c_str());
				dlplog_info(g_log_handle, ("匹配规则ID: " + to_string(i3) + " 规则名称 : " + RuleName.c_str()).c_str());
				dlplog_info(g_log_handle, gbTextTrimed.c_str());
				dlplog_info(g_log_handle, gbStrategyMatched.c_str());
			}
		}

		drip_result_p->Release();
		drip_result_p = NULL;
	}

	return bIsSensitive;
	
}

// 读取文本文件内容
bool CldfcrTesterDlg::ReadTextContent(const char* file_path, std::string& text)//1008
{
	FILE* file = fopen(file_path, "rb");
	if (file)
	{
		fseek(file, 0, SEEK_END);
		long size = ftell(file);
		fseek(file, 0, SEEK_SET);

		if (size > 0)
		{
			text.resize(size);
			fread(&text[0], 1, size, file);
		}
		fclose(file);
		return true;
	}
	return false;
}


/*
* 控件：检测文本
* 功能：点击按钮，可使用当前的策略检测文本。可自由输入文本进行检测
*	     如果有策略中的敏感文件，会进行提示。
*		 使用的策略若没有点击更新控件，则还是使用上次的策略文件
*/
void CldfcrTesterDlg::OnBnClickedTcr()
{
	// TODO: 在此添加控件通知处理程序代码

	CInputCheckTextDlg dlg;

	if (IDOK != dlg.DoModal())
	{
		return;
	}

	if (m_InitChosen == PointMoudle)
	{
		if (NULL == m_pILDFcr) return;
	}

	string strTestContext = GbkToUtf8(WstringToString(dlg.m_strCheckText.GetString()).c_str());  //2023 8 17 弃用W接口，将输入框内的文本从cstring转换为string

	BOOL bIsSensitive = FALSE;
	OnBnClickedSetMoudle();
	FCRPARAM fcrParam;

	fcrParam.devType = 1;
	fcrParam.opType = 1;
	fcrParam.use_all_rule = TRUE;   //启用所有规则
	fcrParam.target_class_code = 3; //达到或超过代码3，即停止
	fcrParam.output_detail = TRUE;  //要求返回时输出策略的细节信息

	IFCRResult*  result_p = NULL;
	IFCRDripResult* drip_result_p = NULL;

	if (m_InitChosen == PointMoudle)
	{
		bIsSensitive = m_pILDFcr->executeTCR(
			strTestContext.c_str(), -1,
			&fcrParam, (void**)&result_p,
			(void**)&drip_result_p
			//NULL
		);
	}
	else
	{
		bIsSensitive = ldfcr_ExecuteTCR(
			strTestContext.c_str(), -1,
			&fcrParam, (void**)&result_p,
			(void**)&drip_result_p
			//NULL
		);
	}



	std::string strStrategyIds;
	std::string strText;
	int textSize = 0;

	if (NULL != result_p)
	{
		int n = 0;
		for (int i = 0; i < 3; i++)
		{
			result_p->moveFirstPart();
			while (NULL != result_p->moveNextPart())
			{
				n++;
			}
		}

		/////
		strStrategyIds = result_p->GetAdvancedStrategyIdsFCR();
		textSize = result_p->GetTextSize();
		std::string _strText;
		result_p->moveFirstTextPart();
		do
		{
			_strText = std::move(result_p->GetNextTextPart());
			if (_strText.length()>0)
			{
				strText.append(_strText);
			}

		} while (_strText.length()>0);
		/////

		//dlplog_info(g_log_handle,
		//	"<OnBnClickedBtnExecFcrSinglefile> 这里是过程2[result_p]");
		result_p->Release();
		result_p = NULL;
	}

	if (bIsSensitive)
	{
		MessageBox(L"检测为敏感文本！");
	}
	else
	{
		MessageBox(L"检测无敏感文本.");
	}
}


/*
* 日志初始化
*/
void CldfcrTesterDlg::initLog()
{
	trcrt::trcrt_init();
	char config_file_path[1024] = { 0 };
	trcrt::app::tr_app_get_config_directory(config_file_path, sizeof(config_file_path));
	trcrt::path::tr_path_make_sub_path(config_file_path, sizeof(config_file_path), "log_ldfcr.properties");
	int suceess = dlplog_load_config(config_file_path);   //加载日志配置

														  //dlplog_cd();  //修改日志目录
	dlplog_init("ldfcrTester.log");

	//开启日志功能
	g_log_handle = dlplog_open("ldfcrTester");
	//dlplog_info(g_log_handle, "$version$: 2.20 at revision %d on %s %s", 10460, __DATE__, __TIME__);

}

/*
* 控件：策略选择
* 功能：可选择后缀为(.data)的文件，选择路径成功后，保存策略的路径至ini文件中，并显示到示例编辑框里。
*/
void CldfcrTesterDlg::OnBnClickedBtnTacticsFile2()
{
	// TODO: 在此添加控件通知处理程序代码
	CString strInitialDir;
	m_CStrategyEdit.GetWindowText(strInitialDir); // 获取 IDC_STRATEGY_FILEPATH 的路径
	CString initialPath; 

	if (!strInitialDir.IsEmpty())
	{
		// 编辑框中的路径不为空，将其作为初始路径
		initialPath = strInitialDir;
	}
	else
	{
		// 编辑框中的路径为空，将.exe文件所在的目录作为初始路径
		char exeDir[MAX_PATH];
		trcrt::app::tr_app_get_app_directory(exeDir, MAX_PATH);
		initialPath = CString(exeDir);
		strInitialDir = initialPath;//1024
	}

	static WCHAR T;
	//CString strFile = _T("R:\\");
	CFileDialog dlgFile(TRUE, _T("data"), NULL, OFN_HIDEREADONLY, _T("Data Files (*.data)|*.data||"), NULL);
	dlgFile.m_ofn.lpstrInitialDir = initialPath;//1023

	if (dlgFile.DoModal())
	{
		strInitialDir = dlgFile.GetPathName();
		m_strStrategyFilePath = strInitialDir;
	}

	if (strInitialDir.IsEmpty())
	{
		return;
	}

	m_strStrategyFilePath = strInitialDir;

	this->UpdateData(FALSE);


	//存写ini文件

	ini.SetUnicode(true);
	CString wcsAppDir;
	wcsAppDir = GetProPath();
	CString _str;
	_str += wcsAppDir;
	_str += L"\\Setting.ini";
	//USES_CONVERSION;//声明标识
	//const char* _str_str = T2A(_str);
	//string str1 = Utf8ToGbk(_str_str); //转码
	USES_CONVERSION;
	//ini文件读取、写入、保存
	const char* strpath = T2A(m_strStrategyFilePath);

	ini.LoadFile(_str);
	ini.SetValue("path0", "m_strStrategyFilePath", strpath);//设置ini文件
	ini.SaveFile(_str, 0);//保存ini文件


	///*
	//*  获取策略ID模块
	//*/

	////清空idNameMap
	//idNameMap.clear();

	////获取编辑框中的文件路径
	//CString filePath;
	//GetDlgItemText(IDC_STRATEGY_FILEPATH, filePath);

	////提取策略ID和NAME
	//ExtractIdWithName(filePath, idNameMap);
	////清空下拉框
	//m_ComboSelectIdName.ResetContent();

	////将策略ID和NAME添加到下拉框
	//for (const auto& pair : idNameMap)
	//{
	//	CString idString;
	//	CString idNameString;
	//	//格式化策略ID
	//	idString.Format(L"%d", pair.first);

	//	//转换策略ID名称为Unicode
	//	wstring unicode_strategy_name = Utf8ToUnicode(pair.second);

	//	//格式化策略名称
	//	idNameString.Format(L"%d-%s", pair.first, unicode_strategy_name.c_str());
	//	int indexIdName = m_ComboSelectIdName.AddString(idNameString);
	//	m_ComboSelectIdName.SetItemData(indexIdName, pair.first);
	//	//设置默认第一个选项
	//	m_ComboSelectIdName.SetCurSel(0);
	//}

	////更新下拉框选择
	//OnCbnSelchangeComboSelectIdName();

	//更新策略
	OnBnClickedBtnUpdateStrategy();

}

/*
* 函数：获取运行程序目录
* 功能：可获取运行程序的目录
*/
CString CldfcrTesterDlg::GetProPath()
{

	WCHAR wcsAppDir[MAX_PATH];
	::GetModuleFileNameW(NULL, wcsAppDir, sizeof(wcsAppDir) / sizeof(WCHAR));
	int  iLen = ::wcslen(wcsAppDir);
	while (0 < iLen)
	{
		if (L'\\' == wcsAppDir[iLen - 1])
		{
			wcsAppDir[iLen - 1] = 0;
			break;
		}

		iLen--;
	}

	CString _str = wcsAppDir;
	return _str;
}


void CldfcrTesterDlg::OnLvnItemchangedList2(NMHDR *pNMHDR, LRESULT *pResult)
{
	LPNMLISTVIEW pNMLV = reinterpret_cast<LPNMLISTVIEW>(pNMHDR);
	// TODO: 在此添加控件通知处理程序代码
	*pResult = 0;
}

/*
* 控件：刷新目录
* 功能：从运行目录下的log文件夹中读取ldfcr.log文件。将文件的最后20行显示在list control控件
*		 中。每次点击按钮都会清空上次插入的数据。若读取不到文件，会进行提示。刷新日志后，list control控件的
*		 滚轮会自动跳转到最后一行。
*/
void CldfcrTesterDlg::OnBnClickedLogFlush()
{
	// TODO: 在此添加控件通知处理程序代码
	//初始化
	//m_listCtrl.DeleteAllItems();	//清空所有表项
	//while (m_listCtrl.DeleteColumn(0));	//清空所有表头
	//m_listCtrl.InsertColumn(0, _T("日志"), LVCFMT_LEFT, 3000, -1);

	//CListCtrl *pList = (CListCtrl *)GetDlgItem(IDC_LIST2);//获取list control控件

	//ifstream in(m_showLogPath);  //打开文件
	//string line;
	//string test;
	//string Getline;

	//string::size_type idx;
	//CString c_string = 0;
	//if (!in) // 无该文件
	//{
	//	MessageBox(_T("暂无日记"));
	//	return;
	//}
	//int linecount = CountLogLine();
	//int count = 0;
	//int countflag = (linecount - 50);
	//while (getline(in,test))
	//{
	//	count++;
	//	if (count >= countflag)
	//	{
	//		line = test.c_str();
	//		c_string = line.c_str();
	//		LPCTSTR lp = c_string;
	//		int listcount = pList->GetItemCount();  //列表中的个数
	//		pList->InsertItem(listcount, lp);
	//		m_listCtrl.EnsureVisible(listcount, FALSE);//滚轮默认自动跳转到最后

	//	}
	//}
	//in.close();

	CldfcrLog dlgLog(m_strShowLogPath);
	dlgLog.DoModal();
}

void CldfcrTesterDlg::OnBnClickedLogFlush2()
{
	// TODO: 在此添加控件通知处理程序代码

	CldfcrLog dlgLog(m_strShowLogPath2);
	dlgLog.DoModal();
}


/*
* 函数：获取日志文件行数
* 功能：计算日志文件行数
* 返回：日志行数
*/
int CldfcrTesterDlg::CountLogLine()
{
	CString wcsAppDir;
	wcsAppDir = GetProPath();
	CString _str;
	_str += wcsAppDir;
	_str += L"\\log\\ldfcr.log";

	ifstream in(m_strShowLogPath);  //打开文件
	string line;
	string Getline;
	if (!in) // 有该文件
	{
		return -1;
	}
	int linecount = 0;
	while (getline(in, Getline))
	{
		linecount++;
	}
	return linecount;
}


void CldfcrTesterDlg::OnBnClickedChooseLog()
{
	// TODO: 在此添加控件通知处理程序代码
	CString strInitialDir;
	m_CShowLogPathEdit.GetWindowText(strInitialDir);
	CString initialPath;

	if (!strInitialDir.IsEmpty())
	{
		// 如果编辑框中的路径不为空，将其作为初始路径
		initialPath = strInitialDir;
	}
	else
	{
		// 如果编辑框中的路径为空，将.log文件所在的目录作为初始路径
		char logDir[MAX_PATH];
		trcrt::app::tr_app_get_log_directory(logDir, MAX_PATH);//1024
		initialPath = CString(logDir);
	}

	static WCHAR T;
	//CString strFile = _T("R:\\");
	CFileDialog   dlgFile(TRUE, _T("log"), NULL, OFN_HIDEREADONLY, _T("Log (*.log)|*.log||"), NULL);
	dlgFile.m_ofn.lpstrInitialDir = initialPath;//1025 
	if (dlgFile.DoModal())
	{
		strInitialDir = dlgFile.GetPathName();
	}

	if (strInitialDir.IsEmpty())
	{
		return;
	}

	m_strShowLogPath = strInitialDir;

	this->UpdateData(FALSE);

	//存写ini文件
	ini.SetUnicode(true);
	CString wcsAppDir;
	wcsAppDir = GetProPath();
	CString _str;
	_str += wcsAppDir;
	_str += L"\\Setting.ini";
	USES_CONVERSION;
	//ini文件读取、写入、保存
	const char* strpath = T2A(m_strShowLogPath);

	ini.LoadFile(_str);
	ini.SetValue("showLog", "m_strShowLogPath", strpath);//设置ini文件
	ini.SaveFile(_str, 0);//保存ini文件
}

void CldfcrTesterDlg::OnBnClickedChooseLog2()
{
	// TODO: 在此添加控件通知处理程序代码
	CString strInitialDir;
	m_CShowLogPathEdit2.GetWindowText(strInitialDir);
	CString initialPath;

	if (!strInitialDir.IsEmpty())
	{
		// 如果编辑框中的路径不为空，将其作为初始路径
		initialPath = strInitialDir;
	}
	else
	{
		// 如果编辑框中的路径为空，将log文件夹所在的目录作为初始路径
		char logDir[MAX_PATH];
		trcrt::app::tr_app_get_log_directory(logDir, MAX_PATH);
		initialPath = CString(logDir);
	}

	static WCHAR T;
	//CString strFile = _T("R:\\");
	CFileDialog   dlgFile(TRUE, _T("log"), NULL, OFN_HIDEREADONLY, _T("Log (*.log)|*.log||"), NULL);
	dlgFile.m_ofn.lpstrInitialDir = initialPath;
	if (dlgFile.DoModal())
	{
		strInitialDir = dlgFile.GetPathName();
	}

	if (strInitialDir.IsEmpty())
	{
		return;
	}

	m_strShowLogPath2 = strInitialDir;

	this->UpdateData(FALSE);

	//存写ini文件
	ini.SetUnicode(true);
	CString wcsAppDir;
	wcsAppDir = GetProPath();
	CString _str;
	_str += wcsAppDir;
	_str += L"\\Setting.ini";
	USES_CONVERSION;
	//ini文件读取、写入、保存
	const char* strpath = T2A(m_strShowLogPath2);

	ini.LoadFile(_str);
	ini.SetValue("showLog", "m_strShowLogPath2", strpath);//设置ini文件
	ini.SaveFile(_str, 0);//保存ini文件

}



void CldfcrTesterDlg::OnEnChangeInputIntercept()
{
	// TODO:  如果该控件是 RICHEDIT 控件，它将不
	// 发送此通知，除非重写 CDialog::OnInitDialog()
	// 函数并调用 CRichEditCtrl().SetEventMask()，
	// 同时将 ENM_CHANGE 标志“或”运算到掩码中。

	// TODO:  在此添加控件通知处理程序代码

	CString input_time = 0;
	editinput.GetWindowText(input_time);
	m_time_input = _ttoi(input_time);

}


void CldfcrTesterDlg::OnEnChangeEditFcrFilepath()
{
	// TODO:  如果该控件是 RICHEDIT 控件，它将不
	// 发送此通知，除非重写 CDialog::OnInitDialog()
	// 函数并调用 CRichEditCtrl().SetEventMask()，
	// 同时将 ENM_CHANGE 标志“或”运算到掩码中。

	// TODO:  在此添加控件通知处理程序代码
}

BOOL CldfcrTesterDlg::OnNewDocument()
{

	// TODO: add reinitialization code here

	// (SDI documents will reuse this document)

	//名字拼接
	CString wcsAppDir;
	wcsAppDir = GetProPath();//获取运行程序路径


	wcsAppDir += returnProName();//文件执行路径

	USES_CONVERSION;//声明标识
	const char* chAppDir = T2A(wcsAppDir);
	string strAppDir = GetSoftVersion(chAppDir);

	if (m_InitChosen == GlobalMoudle)
	{
		strAppDir = "文件内容识别组件测试程序 " + strAppDir + " (global)";
	}
	else
	{
		strAppDir = "文件内容识别组件测试程序 " + strAppDir;
	}
	CString cstrAppTitle;
	cstrAppTitle = strAppDir.c_str();
	std::string AppTitle = Utf8ToGbk(strAppDir.c_str());
	cstrAppTitle = AppTitle.c_str();
	SetWindowText(cstrAppTitle);//设置初始窗口的标题

	return TRUE;

}

string CldfcrTesterDlg::GetSoftVersion(const char* exepath)
{
	std::string r = "";
	if (!exepath)
		return r;
	//if (_access(exepath, 0) != 0)
	//	return r;
	UINT sz = GetFileVersionInfoSizeA(exepath, 0);
	if (sz != 0) {
		r.resize(sz, 0);
		char *pBuf = NULL;
		pBuf = new char[sz];
		VS_FIXEDFILEINFO *pVsInfo;
		if (GetFileVersionInfoA(exepath, 0, sz, pBuf)) {
			if (VerQueryValue(pBuf, L"\\", (void **)&pVsInfo, &sz)) {
				sprintf(pBuf, "%d.%d.%d.%d", HIWORD(pVsInfo->dwFileVersionMS), LOWORD(pVsInfo->dwFileVersionMS), HIWORD(pVsInfo->dwFileVersionLS), LOWORD(pVsInfo->dwFileVersionLS));
				r = pBuf;
			}
		}
		delete pBuf;
	}
	return r;
}

CString CldfcrTesterDlg::returnProName()
{

	TCHAR szExeFileName[MAX_PATH];

	GetModuleFileName(NULL, szExeFileName, MAX_PATH);

	wchar_t drive[_MAX_DRIVE];
	wchar_t dir[_MAX_DIR];
	wchar_t fname[_MAX_FNAME];
	wchar_t ext[_MAX_EXT];

	_wsplitpath(szExeFileName, drive, dir, fname, ext);
	wstring wsfname = fname;
	wstring wsext = ext;
	wsfname += wsext;
	wsfname = L"\\" + wsfname;

	return wsfname.c_str();

}

string CldfcrTesterDlg::WstringToString(const wstring v_wstr)
{// wstring转string
	unsigned len = v_wstr.size() * 4;
	setlocale(LC_CTYPE, "");
	char *p = new char[len];
	wcstombs(p, v_wstr.c_str(), len);
	string str1(p);
	delete[] p;
	return str1;
}


void CldfcrTesterDlg::OnBnClickedFILEFP()
{
	// TODO: 在此添加控件通知处理程序代码
	//FILEFP
	int state = ((CButton *)GetDlgItem(IDC_CHECK3))->GetCheck();

	ini.SetUnicode(true);
	CString wcsAppDir;
	wcsAppDir = GetProPath();
	CString _str;
	_str += wcsAppDir;
	_str += L"\\Setting.ini";

	USES_CONVERSION;//声明标识
	const char* _str_str = T2A(_str);

	string str1 = Utf8ToGbk(_str_str);

	//ini文件读取、写入、保存
	//const char* strpath = T2A(m_intOptype);
	char m_itc[10];
	sprintf(m_itc, "%d", state);//保存操作类型
	ini.LoadFile(_str);//加载ini文件
	ini.SetValue("LDFCR_MODULE", "m_bfilefp", m_itc);//写入选择的路径
	ini.SaveFile(_str, 0);//保存ini文件
}


void CldfcrTesterDlg::OnBnClickedSVM()
{
	// TODO: 在此添加控件通知处理程序代码
	//SVM
	int state = ((CButton *)GetDlgItem(IDC_CHECK4))->GetCheck();

	ini.SetUnicode(true);
	CString wcsAppDir;
	wcsAppDir = GetProPath();
	CString _str;
	_str += wcsAppDir;
	_str += L"\\Setting.ini";

	USES_CONVERSION;//声明标识
	const char* _str_str = T2A(_str);

	string str1 = Utf8ToGbk(_str_str);

	//ini文件读取、写入、保存
	//const char* strpath = T2A(m_intOptype);
	char m_itc[10];
	sprintf(m_itc, "%d", state);//保存操作类型
	ini.LoadFile(_str);//加载ini文件
	ini.SetValue("LDFCR_MODULE", "m_bsvm", m_itc);//写入选择的路径
	ini.SaveFile(_str, 0);//保存ini文件
}


void CldfcrTesterDlg::OnBnClickedFILEDB()
{
	// TODO: 在此添加控件通知处理程序代码
	//FILEDB
	int state = ((CButton *)GetDlgItem(IDC_CHECK5))->GetCheck();

	ini.SetUnicode(true);
	CString wcsAppDir;
	wcsAppDir = GetProPath();
	CString _str;
	_str += wcsAppDir;
	_str += L"\\Setting.ini";

	USES_CONVERSION;//声明标识
	const char* _str_str = T2A(_str);

	string str1 = Utf8ToGbk(_str_str);

	//ini文件读取、写入、保存
	//const char* strpath = T2A(m_intOptype);
	char m_itc[10];
	sprintf(m_itc, "%d", state);//保存操作类型
	ini.LoadFile(_str);//加载ini文件
	ini.SetValue("LDFCR_MODULE", "m_bfiledb", m_itc);//写入选择的路径
	ini.SaveFile(_str, 0);//保存ini文件
}


void CldfcrTesterDlg::OnBnClickedOCR_EMBED()
{
	// TODO: 在此添加控件通知处理程序代码
	//OCR_EMBED
	int state = ((CButton *)GetDlgItem(IDC_CHECK7))->GetCheck();

	ini.SetUnicode(true);
	CString wcsAppDir;
	wcsAppDir = GetProPath();
	CString _str;
	_str += wcsAppDir;
	_str += L"\\Setting.ini";

	USES_CONVERSION;//声明标识
	const char* _str_str = T2A(_str);

	string str1 = Utf8ToGbk(_str_str);

	//ini文件读取、写入、保存
	//const char* strpath = T2A(m_intOptype);
	char m_itc[10];
	sprintf(m_itc, "%d", state);//保存操作类型
	ini.LoadFile(_str);//加载ini文件
	ini.SetValue("LDFCR_MODULE", "m_bocr_embed", m_itc);//写入选择的路径
	ini.SaveFile(_str, 0);//保存ini文件
}


void CldfcrTesterDlg::OnBnClickedOCR()
{
	// TODO: 在此添加控件通知处理程序代码
	//OCR
	int state = ((CButton *)GetDlgItem(IDC_CHECK8))->GetCheck();

	ini.SetUnicode(true);
	CString wcsAppDir;
	wcsAppDir = GetProPath();
	CString _str;
	_str += wcsAppDir;
	_str += L"\\Setting.ini";

	USES_CONVERSION;//声明标识
	const char* _str_str = T2A(_str);

	string str1 = Utf8ToGbk(_str_str);

	//ini文件读取、写入、保存
	//const char* strpath = T2A(m_intOptype);
	char m_itc[10];
	sprintf(m_itc, "%d", state);//保存操作类型
	ini.LoadFile(_str);//加载ini文件
	ini.SetValue("LDFCR_MODULE", "m_bocr", m_itc);//写入选择的路径
	ini.SaveFile(_str, 0);//保存ini文件
}


void CldfcrTesterDlg::OnBnClickedFILEXT()
{
	// TODO: 在此添加控件通知处理程序代码
	//FILEXT
	// TODO: 在此添加控件通知处理程序代码
	//OCR
	int state = ((CButton *)GetDlgItem(IDC_CHECK9))->GetCheck();

	ini.SetUnicode(true);
	CString wcsAppDir;
	wcsAppDir = GetProPath();
	CString _str;
	_str += wcsAppDir;
	_str += L"\\Setting.ini";

	USES_CONVERSION;//声明标识
	const char* _str_str = T2A(_str);

	string str1 = Utf8ToGbk(_str_str);

	//ini文件读取、写入、保存
	//const char* strpath = T2A(m_intOptype);
	char m_itc[10];
	sprintf(m_itc, "%d", state);//保存操作类型
	ini.LoadFile(_str);//加载ini文件
	ini.SetValue("LDFCR_MODULE", "m_filext", m_itc);//写入选择的路径
	ini.SaveFile(_str, 0);//保存ini文件
}

string CldfcrTesterDlg::toBase64(const char *src_str)
{

	string str = src_str;

	int len = MultiByteToWideChar(CP_ACP, 0, str.c_str(), -1, NULL, 0);
	wchar_t* wstr = new wchar_t[len + 1];
	memset(wstr, 0, len + 1);
	MultiByteToWideChar(CP_ACP, 0, str.c_str(), -1, wstr, len);
	len = WideCharToMultiByte(CP_UTF8, 0, wstr, -1, NULL, 0, NULL, NULL);
	char* Data1 = new char[len + 1];
	memset(Data1, 0, len + 1);
	WideCharToMultiByte(CP_UTF8, 0, wstr, -1, Data1, len, NULL, NULL);

	int DataByte = len - 1;
	unsigned char * Data = new unsigned char[DataByte];
	memcpy(Data, Data1, DataByte);

	//编码表
	const char EncodeTable[] = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/";
	//返回值
	std::string strEncode;
	unsigned char Tmp[4] = { 0 };
	int LineLength = 0;
	for (int i = 0; i < (int)(DataByte / 3); i++)
	{
		Tmp[1] = *Data++;
		Tmp[2] = *Data++;
		Tmp[3] = *Data++;
		strEncode += EncodeTable[Tmp[1] >> 2];
		strEncode += EncodeTable[((Tmp[1] << 4) | (Tmp[2] >> 4)) & 0x3F];
		strEncode += EncodeTable[((Tmp[2] << 2) | (Tmp[3] >> 6)) & 0x3F];
		strEncode += EncodeTable[Tmp[3] & 0x3F];
		//if (LineLength += 4, LineLength == 76) { strEncode += "\r\n"; LineLength = 0; }
	}
	//对剩余数据进行编码
	int Mod = DataByte % 3;
	if (Mod == 1)
	{
		Tmp[1] = *Data++;
		strEncode += EncodeTable[(Tmp[1] & 0xFC) >> 2];
		strEncode += EncodeTable[((Tmp[1] & 0x03) << 4)];
		strEncode += "==";
	}
	else if (Mod == 2)
	{
		Tmp[1] = *Data++;
		Tmp[2] = *Data++;
		strEncode += EncodeTable[(Tmp[1] & 0xFC) >> 2];
		strEncode += EncodeTable[((Tmp[1] & 0x03) << 4) | ((Tmp[2] & 0xF0) >> 4)];
		strEncode += EncodeTable[((Tmp[2] & 0x0F) << 2)];
		strEncode += "=";
	}

	return strEncode;
}


void CldfcrTesterDlg::OnBnClickedCheck1()
{
	// TODO: 在此添加控件通知处理程序代码
	int state = ((CButton *)GetDlgItem(IDC_CHECK1))->GetCheck();
	if (state == 1)
	{
		m_pwd_error = true;
	}
	else if (state == 0)
	{
		m_pwd_error = false;
	}

	ini.SetUnicode(true);
	CString wcsAppDir;
	wcsAppDir = GetProPath();
	CString _str;
	_str += wcsAppDir;
	_str += L"\\Setting.ini";

	USES_CONVERSION;//声明标识
	const char* _str_str = T2A(_str);

	string str1 = Utf8ToGbk(_str_str);

	//ini文件读取、写入、保存
	//const char* strpath = T2A(m_intOptype);
	char m_itc[10];
	sprintf(m_itc, "%d", state);//保存操作类型
	ini.LoadFile(_str);//加载ini文件
	ini.SetValue("block_on_pwderror", "m_b_o_pwderror", m_itc);//写入选择的路径
	ini.SaveFile(_str, 0);//保存ini文件
}


void CldfcrTesterDlg::OnBnClickedSetAdvKeyWord()
{
	// TODO: 在此添加控件通知处理程序代码
	//高级算法密钥
	this->UpdateData(TRUE);

	if (NULL == m_pILDFcr) return;
	if ((m_csNormalKey.GetLength() < 0) || (m_csNormalKey.GetLength() > 128))
	{
		MessageBox(_T("数值超过范围，清输入0~128内的主密钥加密串"), _T("错误"), MB_OK);
		return;
	}

	char * charNormal = CStringtochar(m_csNormalKey);

	//设置密钥，default是临时密钥加密串，Normal是主密钥加密串 
	BOOL AdvInit = m_pILDFcr->SetAdvInitParam(charNormal, charNormal);//231010

	if (AdvInit <= 0)
	{
		MessageBox(_T("高级算法秘钥设置失败"), _T("错误"), MB_OK);
		return;
	}

	//记录
	ini.SetUnicode(true);
	CString wcsAppDir;
	wcsAppDir = GetProPath();
	CString _str;
	_str += wcsAppDir;
	_str += L"\\Setting.ini";

	USES_CONVERSION;//声明标识
	const char* _str_str = T2A(_str);

	string str1 = Utf8ToGbk(_str_str);

	//ini文件读取、写入、保存
	const char* NormalKey = T2A(m_csNormalKey);
	ini.LoadFile(_str);//加载ini文件
	ini.SetValue("AdvInitParam", "m_csNormalKey", NormalKey);//写入选择的路径
	ini.SaveFile(_str, 0);//保存ini文件


	MessageBox(_T("高级算法秘钥设置成功"), _T("成功"), MB_OK);
}

char * CldfcrTesterDlg::CStringtochar(CString str)
{
	char *ptr;
#ifdef _UNICODE
	LONG len;
	len = WideCharToMultiByte(CP_ACP, 0, str, -1, NULL, 0, NULL, NULL);
	ptr = new char[len + 1]; memset(ptr, 0, len + 1);
	WideCharToMultiByte(CP_ACP, 0, str, -1, ptr, len + 1, NULL, NULL);
#else
	ptr = new char[str.GetAllocLength() + 1];
	sprintf(ptr, _T("%s"), str);
#endif
	return ptr;
}

void CldfcrTesterDlg::OnBnClickedGetDefaultList()
{
	// TODO: 在此添加控件通知处理程序代码
	this->UpdateData(TRUE);

	char chRet[MAX_FILE_COUNT] = { 0 };
	BOOL bRet = m_pILDFcr->getRuleFileList(this->m_strStrategyMerge.c_str(), NULL, chRet, MAX_FILE_COUNT);
	if (!bRet | (m_strStrategyFilePath.Compare(L"") == 0))
	{
		MessageBox(L"列表获取失败");
		return;
	}
	else
	{
		CString cstr(chRet);
		MessageBox(cstr);
		return;
	}
}

// 程序开始检测时对moudle做一个设置，因为作用域问题导致高级算法没有生效
void CldfcrTesterDlg::OnBnClickedSetMoudle()
{
	// TODO: 在此添加控件通知处理程序代码
	this->UpdateData(TRUE);

	ini.SetUnicode(true);
	CString wcsAppDir;
	wcsAppDir = GetProPath();
	CString _str;
	_str += wcsAppDir;
	_str += L"\\Setting.ini";

	USES_CONVERSION;//声明标识
	const char* _str_str = T2A(_str);

	string str1 = Utf8ToGbk(_str_str);

	ini.LoadFile(_str);//加载ini文件

	int v_filefp = ini.GetLongValue("LDFCR_MODULE", "m_bfilefp", 0);
	int v_svm = ini.GetLongValue("LDFCR_MODULE", "m_bsvm", 0);
	int v_filedb = ini.GetLongValue("LDFCR_MODULE", "m_bfiledb", 0);
	int v_filext = ini.GetLongValue("LDFCR_MODULE", "m_filext", 0);
	int v_ocr_embed = ini.GetLongValue("LDFCR_MODULE", "m_bocr_embed", 0);
	int v_ocr = ini.GetLongValue("LDFCR_MODULE", "m_bocr", 0);

	if (v_filefp == 1)
	{
		ldfcr_ControlModule(LDFCR_MODULE_FILEFP, 1);
	}
	else
	{
		ldfcr_ControlModule(LDFCR_MODULE_FILEFP, 0);
	}

	if (v_svm == 1)
	{
		ldfcr_ControlModule(LDFCR_MODULE_SVM, 1);
	}
	else
	{
		ldfcr_ControlModule(LDFCR_MODULE_SVM, 0);
	}

	if (v_filedb == 1)
	{
		ldfcr_ControlModule(LDFCR_MODULE_DBFP, 1);
	}
	else
	{
		ldfcr_ControlModule(LDFCR_MODULE_DBFP, 0);
	}

	if (v_filext == 1)
	{
		//ldfcr_ControlModule(LDFCR_MODULE_OCR_EMBED, 1);20230804
		ldfcr_ControlModule(LDFCR_MODULE_FILEXT, 1);
	}
	else
	{
		ldfcr_ControlModule(LDFCR_MODULE_FILEXT, 0);
	}

	if (v_ocr_embed == 1)
	{
		//ldfcr_ControlModule(LDFCR_MODULE_OCR, 1);20230804
		ldfcr_ControlModule(LDFCR_MODULE_OCR_EMBED, 1);
	}
	else
	{
		ldfcr_ControlModule(LDFCR_MODULE_OCR_EMBED, 0);
	}

	if (v_ocr == 1)
	{
		//ldfcr_ControlModule(LDFCR_MODULE_FILEXT, 1);20230804
		ldfcr_ControlModule(LDFCR_MODULE_OCR, 1);
	}
	else
	{
		ldfcr_ControlModule(LDFCR_MODULE_OCR, 0);
	}

}



//提取策略id和策略名称
void CldfcrTesterDlg::ExtractIdWithName(const CString& filePath, map<int, string>& idNameMap)
{
	ifstream file(filePath);

	//文件打开失败
	if (!file.is_open())
	{
		return;
	}

	//读取文件内容
	string logContent((istreambuf_iterator<char>(file)), istreambuf_iterator<char>());

	//关闭文件
	file.close();

	//创建Document对象
	Document document;

	//解析JSON
	document.Parse(logContent.c_str());

	//检查解析是否成功
	if (document.HasParseError())
	{
		return;
	}
	if (!document.IsObject())
	{
		return;
	}

	//检查json格式，判断是否包含"strategy"数组字段
	const Value& strategies = document["strategy"];
	//不包含"strategy"字段
	if (!strategies.IsArray())
	{
		return;
	}

	//给容器一个0值，选中0值得时候默认检测所有策略
	idNameMap[0] = "all strategies";

	//遍历 "strategy" 数组元素
	for (SizeType i = 0; i < strategies.Size(); ++i)
	{
		const Value& strategy = strategies[i];
		//包含id和name字段
		if (strategy.HasMember("id") && strategy.HasMember("name") && strategy["id"].IsInt() && strategy["name"].IsString())
		{
			//提取id和name
			int strategyId = strategy["id"].GetInt();
			string strategyName = strategy["name"].GetString();

			//存储到map
			idNameMap[strategyId] = strategyName.c_str();
		}
	}
}

//提取id和name
void CldfcrTesterDlg::OnCbnSelchangeComboSelectIdName()
{
	// TODO: 在此添加控件通知处理程序代码

}

//默认文件路径
void CldfcrTesterDlg::OnEnChangeStrategyFilepath()
{
	// TODO:  如果该控件是 RICHEDIT 控件，它将不
	// 发送此通知，除非重写 CDialog::OnInitDialog()
	// 函数并调用 CRichEditCtrl().SetEventMask()，
	// 同时将 ENM_CHANGE 标志“或”运算到掩码中。

	// TODO:  在此添加控件通知处理程序代码

}

/*
* 控件：超时响应规则选择 id=224
* 功能：可选择后缀为(.json)的文件，选择路径成功后，保存策略的路径至ini文件中，并显示到示例编辑框里。
*/
void CldfcrTesterDlg::OnBnClickedBtnSelectTimeoutJson()
{
	// TODO: 在此添加控件通知处理程序代码
	CString strInitialDir;
	m_CTimeOutRuleEdit.GetWindowText(strInitialDir);
	CString initialPath;

	if (!strInitialDir.IsEmpty())
	{
		// 如果编辑框中的路径不为空，将其作为初始路径
		initialPath = strInitialDir;
	}
	else
	{
		// 如果编辑框中的路径为空，将.exe文件所在的目录作为初始路径
		char exeDir[MAX_PATH];
		trcrt::app::tr_app_get_app_directory(exeDir, MAX_PATH);
		initialPath = CString(exeDir);
	}

	static WCHAR T;
	//CString strFile = _T("R:\\");
	CFileDialog   dlgFile(TRUE, _T("json"), NULL, OFN_HIDEREADONLY, _T("Json Files (*.json)|*.json||"), NULL);
	dlgFile.m_ofn.lpstrInitialDir = initialPath;

	if (dlgFile.DoModal())
	{
		strInitialDir = dlgFile.GetPathName();
	}

	if (strInitialDir.IsEmpty())
	{
		return;
	}

	m_strTimeOutRespRulePath = strInitialDir;

	this->UpdateData(FALSE);


	//存写ini文件
	ini.SetUnicode(true);
	CString wcsAppDir;
	wcsAppDir = GetProPath();
	CString _str;
	_str += wcsAppDir;
	_str += L"\\Setting.ini";

	USES_CONVERSION;
	//ini文件读取、写入、保存
	const char* strpath = T2A(m_strTimeOutRespRulePath);

	ini.LoadFile(_str);//加载ini文件
	ini.SetValue("ResponseRule", "m_strTimeOutRespRulePath", strpath);//设置ini文件
	ini.SaveFile(_str, 0);//保存ini文件

}

/*
 * 控件：带密码响应规则选择  id=225
 * 功能：可选择后缀为(.json)的文件，选择路径成功后，保存策略的路径至ini文件中，并显示到示例编辑框里。
 */
void CldfcrTesterDlg::OnBnClickedBtnSelectPasswordJson()
{
	// TODO: 在此添加控件通知处理程序代码
	CString strInitialDir;
	m_CPassWordRuleEdit.GetWindowText(strInitialDir);
	CString initialPath;

	if (!strInitialDir.IsEmpty())
	{
		// 如果编辑框中的路径不为空，将其作为初始路径
		initialPath = strInitialDir;
	}
	else
	{
		// 如果编辑框中的路径为空，将.exe文件所在的目录作为初始路径
		char exeDir[MAX_PATH];
		trcrt::app::tr_app_get_app_directory(exeDir, MAX_PATH);
		initialPath = CString(exeDir);
	}

	static WCHAR T;
	//CString strFile = _T("R:\\");
	CFileDialog   dlgFile(TRUE, _T("json"), NULL, OFN_HIDEREADONLY, _T("Json Files (*.json)|*.json||"), NULL);
	dlgFile.m_ofn.lpstrInitialDir = initialPath;

	if (dlgFile.DoModal())
	{
		strInitialDir = dlgFile.GetPathName();
	}

	if (strInitialDir.IsEmpty())
	{
		return;
	}

	m_strPassWordRespRulePath = strInitialDir;

	this->UpdateData(FALSE);


	//存写ini文件
	ini.SetUnicode(true);
	CString wcsAppDir;
	wcsAppDir = GetProPath();
	CString _str;
	_str += wcsAppDir;
	_str += L"\\Setting.ini";

	USES_CONVERSION;
	//ini文件读取、写入、保存
	const char* strpath = T2A(m_strPassWordRespRulePath);

	ini.LoadFile(_str);//加载ini文件
	ini.SetValue("ResponseRule", "m_strPassWordRespRulePath", strpath);//设置ini文件
	ini.SaveFile(_str, 0);//保存ini文件


}

// 查找响应规则ID
BOOL CldfcrTesterDlg::FindRespRuleId(const string& strLogContent, int ruleId)
{
	// 解析JSON内容
	rapidjson::Document document;
	document.Parse(strLogContent.c_str());

	// 查找指定ID的规则并进行自定义规则名
	if (document.HasMember("strategy") && document["strategy"].IsArray())
	{
		const rapidjson::Value& strategy = document["strategy"];
		for (rapidjson::SizeType i = 0; i < strategy.Size(); i++)
		{
			const rapidjson::Value& rule = strategy[i];
			if (rule.HasMember("classification") && rule["classification"].IsArray())
			{
				const rapidjson::Value& classificationArray = rule["classification"];
				for (rapidjson::SizeType j = 0; j < classificationArray.Size(); j++)
				{
					const rapidjson::Value& classification = classificationArray[j];
					if (classification.HasMember("rules") && classification["rules"].IsArray())
					{
						const rapidjson::Value& rulesArray = classification["rules"];
						for (rapidjson::SizeType k = 0; k < rulesArray.Size(); k++)
						{
							const rapidjson::Value& rule = rulesArray[k];
							if (rule.HasMember("id") && rule["id"].IsInt() && rule["id"].GetInt() == ruleId)
							{
								return TRUE;
							}
							else
							{
								MessageBox(L"非对应响应规则Json！");
								return FALSE;
							}
						}
					}
				}
			}
		}
	}
}


//设置超时响应规则
void CldfcrTesterDlg::OnBnClickedButTimeoutTriggerRule()
{
	// TODO: 在此添加控件通知处理程序代码
	this->UpdateData(TRUE);
	GetDlgItemText(IDC_EDIT_TIMEOUT_RULES_NAME, m_strTimeOutRulesName);//超时规则名称

	//打开文件
	ifstream file(m_strTimeOutRespRulePath);
	//文件打开失败
	if (!file.is_open())
	{
		MessageBox(L"响应规则文件打开失败！");
		//return;
	}

	//读取文件内容  类型：string
	string strLogContent((istreambuf_iterator<char>(file)), istreambuf_iterator<char>());
	if (strLogContent.empty())
	{
		MessageBox(L"读取规则文件失败，文件为空！");
	}

	if (FindRespRuleId(strLogContent, 224))
	{
		string strUtf8Name = UnicodeToUtf8(m_strTimeOutRulesName.GetBuffer(0));
		BOOL result = m_pILDFcr->AddInlineRespond(strLogContent.c_str(), strUtf8Name.c_str(), 224);
		if (result)
		{
			MessageBox(L"自定义响应规则成功！");
		}
		else
		{
			MessageBox(L"自定义响应规则失败！");
		}
	}


	//存写ini文件
	ini.SetUnicode(true);
	CString wcsAppDir;
	wcsAppDir = GetProPath();
	CString _str;
	_str += wcsAppDir;
	_str += L"\\Setting.ini";

	USES_CONVERSION;
	//ini文件读取、写入、保存
	const char* strTimeOutRulesName = T2A(m_strTimeOutRulesName);

	ini.LoadFile(_str);
	ini.SetValue("ResponseRule", "m_strTimeOutRulesName", strTimeOutRulesName);
	ini.SaveFile(_str, 0);//保存ini文件
}

//设置带密码响应规则
void CldfcrTesterDlg::OnBnClickedButPasswordTriggerRule()
{
	// TODO: 在此添加控件通知处理程序代码
	this->UpdateData(TRUE);
	GetDlgItemText(IDC_EDIT_PASSWORD_RULENAME, m_strPassWordRuleName);//带密码规则名称

	//打开文件
	ifstream file(m_strPassWordRespRulePath);
	//文件打开失败
	if (!file.is_open())
	{
		MessageBox(L"响应规则文件打开失败！");
		//return;
	}

	//读取文件内容  类型：string
	string strLogContent((istreambuf_iterator<char>(file)), istreambuf_iterator<char>());
	if (strLogContent.empty())
	{
		MessageBox(L"读取规则文件失败，文件为空！");
	}

	if (FindRespRuleId(strLogContent, 225))
	{
		string strUtf8Name = UnicodeToUtf8(m_strPassWordRuleName.GetBuffer(0));
		BOOL result = m_pILDFcr->AddInlineRespond(strLogContent.c_str(), strUtf8Name.c_str(), 225);
		if (result)
		{
			MessageBox(L"自定义响应规则成功！");
		}
		else
		{
			MessageBox(L"自定义响应规则失败！");
		}
	}


	//存写ini文件
	ini.SetUnicode(true);
	CString wcsAppDir;
	wcsAppDir = GetProPath();
	CString _str;
	_str += wcsAppDir;
	_str += L"\\Setting.ini";

	USES_CONVERSION;
	//ini文件读取、写入、保存
	const char* strPassWordRuleName = T2A(m_strPassWordRuleName);

	ini.LoadFile(_str);
	ini.SetValue("ResponseRule", "m_strPassWordRuleName", strPassWordRuleName);
	ini.SaveFile(_str, 0);//保存ini文件

}

//主密钥
void CldfcrTesterDlg::OnEnChangeEdit9()
{
	// TODO:  如果该控件是 RICHEDIT 控件，它将不
	// 发送此通知，除非重写 CDialog::OnInitDialog()
	// 函数并调用 CRichEditCtrl().SetEventMask()，
	// 同时将 ENM_CHANGE 标志“或”运算到掩码中。

	// TODO:  在此添加控件通知处理程序代码
}

//获取泄露类型是否有阻断
void CldfcrTesterDlg::OnBnClickedButGetStopType()
{
	// TODO: 在此添加控件通知处理程序代码
	char s[256] = { 0 };
	BOOL gR = m_pILDFcr->getStopOutLossType(this->m_strStrategyMerge.c_str(), "", s, 256);
	CString getStopOutLossType(s);
	MessageBox(getStopOutLossType);
}

void CldfcrTesterDlg::ldfcrTesterThreadFCR(int thread_index)
{
	//char szTMP[256] = { 0 };
	//sprintf(szTMP, "[ldfcrTesterThread]current is [%d] \n", thread_index);
	//OutputDebugStringA(szTMP);
	ghc::filesystem::path file_path;
	std::unique_lock<std::mutex> lock_chk(m_mutex);
	lock_chk.unlock();

	lock_chk.lock();
	if (m_filename_list.empty())
	{
		//dlplog_debug(g_log_handle, "Task list empty so return");
		lock_chk.unlock();
		return;
	}
	else
	{
		file_path = m_filename_list.back();
		m_filename_list.pop_back();
		lock_chk.unlock();
	}
	lock_chk.lock();

	std::string strFilename = WstringToString(file_path.c_str());

	//dlplog_debug(g_log_handle, "current file name is [%s] ,index is [%d] , m_filename_list.size is [%d] ", strFilename.c_str(), thread_index, m_filename_list.size());
	//检测文件
	if (!dlp_checkSingleFile(file_path.c_str()))
	{
		//dlplog_debug(g_log_handle, "current file is [%s] , it s insensitive", strFilename.c_str());
	}
	else
	{
		dlplog_debug(g_log_handle, "current file is [%s] , it s sensitive", strFilename.c_str());
		m_file_count_sens++;
	}
	lock_chk.unlock();
	return;
}

void CldfcrTesterDlg::ldfcrTesterThreadTCR(int thread_index)
{


	//char szTMP[256] = { 0 };
	//sprintf(szTMP, "[ldfcrTesterThread]current is [%d] \n", thread_index);
	//OutputDebugStringA(szTMP);
	ghc::filesystem::path file_path;
	std::unique_lock<std::mutex> lock_chk(m_mutex);
	lock_chk.unlock();

	lock_chk.lock();
	if (m_filename_list.empty())
	{
		//dlplog_debug(g_log_handle, "Task list empty so return");
		lock_chk.unlock();
		return;
	}
	else
	{
		file_path = m_filename_list.back();
		m_filename_list.pop_back();
		lock_chk.unlock();
	}
	lock_chk.lock();

	std::string strFilename = WstringToString(file_path.c_str());

	//dlplog_debug(g_log_handle, "current file name is [%s] ,index is [%d] , m_filename_list.size is [%d] ", strFilename.c_str(), thread_index, m_filename_list.size());
	//检测文件
	if (!dlp_checkSingleText(file_path.c_str()))
	{
		//dlplog_debug(g_log_handle, "current file is [%s] , it s insensitive", strFilename.c_str());
	}
	else
	{
		dlplog_debug(g_log_handle, "current file is [%s] , it s sensitive", strFilename.c_str());
		m_file_count_sens++;
	}
	lock_chk.unlock();
	return;
}


void CldfcrTesterDlg::OnEnChangeEditPasswordRulepath()
{
	// TODO: 在此添加控件通知处理程序代码
}


void CldfcrTesterDlg::OnEnChangeEditPasswordRulename()
{
	// TODO:  在此添加控件通知处理程序代码
}


void CldfcrTesterDlg::OnEnChangeEditTimeoutRulesName()
{
	// TODO:  在此添加控件通知处理程序代码
}

void CldfcrTesterDlg::OnEnChangeEditTimeoutRulepath()
{
	// TODO:  在此添加控件通知处理程序代码
}
