// InitChose.cpp : 实现文件
//

#include "stdafx.h"
#include "ldfcrTester.h"
#include "InitChose.h"
#include "afxdialogex.h"


// InitChose 对话框

IMPLEMENT_DYNAMIC(InitChose, CDialog)

InitChose::InitChose(CWnd* pParent /*=NULL*/)
	: CDialog(IDD_DIALOG_INIT, pParent)
{

}

InitChose::~InitChose()
{
}

void InitChose::DoDataExchange(CDataExchange* pDX)
{
	CDialog::DoDataExchange(pDX);
}


BEGIN_MESSAGE_MAP(InitChose, CDialog)
	ON_BN_CLICKED(IDC_BUTTON1, &InitChose::OnBnClickedButton1)
	ON_BN_CLICKED(IDC_BUTTON2, &InitChose::OnBnClickedButton2)
END_MESSAGE_MAP()


// InitChose 消息处理程序




void InitChose::OnBnClickedButton1()
{
	// TODO: 在此添加控件通知处理程序代码
	setInitChose(0);
	OnOK();
}


void InitChose::OnBnClickedButton2()
{
	// TODO: 在此添加控件通知处理程序代码
	setInitChose(1);
	OnOK();
}

void InitChose::setInitChose(int v_InitChose)
{
	m_InitChose = v_InitChose;
}

int InitChose::getInitChose()
{
	return m_InitChose;
}
