// CldfcrLog.cpp : 实现文件
//

#include "stdafx.h"
#include "ldfcrTester.h"
#include "CldfcrLog.h"
#include "afxdialogex.h"
#include "CodeT.h"
#include <fstream>
#include <string>


// CldfcrLog 对话框

IMPLEMENT_DYNAMIC(CldfcrLog, CDialog)

CldfcrLog::CldfcrLog(CWnd* pParent /*=NULL*/)
	: CDialog(IDD_DIALOG1, pParent)
{

}

CldfcrLog::~CldfcrLog()
{
}

void CldfcrLog::DoDataExchange(CDataExchange* pDX)
{
	CDialog::DoDataExchange(pDX);
	DDX_Control(pDX, IDC_EDIT_LOG, m_editLogCtrl);
}


BEGIN_MESSAGE_MAP(CldfcrLog, CDialog)
	ON_BN_CLICKED(IDC_BUTTON1, &CldfcrLog::OnBnClickedLogRefresh)
	ON_EN_CHANGE(IDC_EDIT_LOG, &CldfcrLog::OnEnChangeEditLog)
END_MESSAGE_MAP()


// CldfcrLog 消息处理程序
CldfcrLog::CldfcrLog(CString v_cstr, CWnd* pParent /*=NULL*/)
	: CDialog(IDD_DIALOG1, pParent)
{
	this->m_showLogPath = v_cstr;

	//清理edit control内容
	m_editLogCtrl.Clear();
}



void CldfcrLog::OnBnClickedLogRefresh()
{
	// TODO: 在此添加控件通知处理程序代码

	//清理edit control内容
	m_editLogCtrl.Clear();

	CEdit *pEdit = (CEdit*)GetDlgItem(IDC_EDIT_LOG);//获取edit control控件

	std::ifstream in(m_showLogPath);  //打开文件
	std::string line;
	std::string test;

	std::string::size_type idx;
	CString c_string = 0;
	if (!in) // 无该文件
	{
		MessageBox(_T("暂无日记!"));
		return;
	}
	int linecount = CountLogLine();
	int count = 0;
	int countflag = linecount;
	while (getline(in,test))
	{
		count++;
		if (count <= countflag)
		{
			line = test.c_str();
			c_string = line.c_str();
			LPCTSTR lp = c_string;
			m_editLogCtrl.LineScroll(m_editLogCtrl.GetLineCount());//滚轮默认自动跳转到最后
		}
	}
	in.close();
}

/*
* 函数：获取日志文件行数
* 功能：计算日志文件行数
* 返回：日志行数
*/
int CldfcrLog::CountLogLine()
{
	CString wcsAppDir;
	wcsAppDir = GetProPath();
	CString _str;
	_str += wcsAppDir;
	_str += L"\\log\\ldfcr.log";

	std::ifstream in(m_showLogPath);
	std::string line;
	std::string Getline;
	if (!in) // 有该文件
	{
		return -1;
	}
	int linecount = 0;
	while (getline(in, Getline))
	{
		linecount++;
	}
	return linecount;
}

/*
* 函数：获取运行程序目录
* 功能：可获取运行程序的目录
*/
CString CldfcrLog::GetProPath()
{

	WCHAR wcsAppDir[MAX_PATH];
	::GetModuleFileNameW(NULL, wcsAppDir, sizeof(wcsAppDir) / sizeof(WCHAR));
	int  iLen = ::wcslen(wcsAppDir);
	while (0 < iLen)
	{
		if (L'\\' == wcsAppDir[iLen - 1])
		{
			wcsAppDir[iLen - 1] = 0;
			break;
		}

		iLen--;
	}

	CString _str = wcsAppDir;
	return _str;
}


BOOL CldfcrLog::OnInitDialog()
{
	CDialog::OnInitDialog();

	// TODO:  在此添加额外的初始化

	//// 清空编辑框内容  也可以
	//m_editLogCtrl.SetWindowText(_T(""));
	//// 打开日志文件并读取内容
	//std::ifstream in(m_showLogPath);  // 打开日志文件
	//std::string line;
	//std::string test;
	//CString logText;
	//if (!in) // 无该文件
	//{
	//	MessageBox(_T("暂无日志!"));
	//	return TRUE;
	//}
	//while (getline(in, test))
	//{
	//	line = test.c_str();
	//	logText += line.c_str();
	//	logText += _T("\r\n");  // 换行
	//}
	//// 将日志内容显示在编辑框中
	//m_editLogCtrl.SetWindowText(logText);
	//return TRUE;


	CWnd *pWnd = GetDlgItem(IDC_EDIT_LOG);
	CString strMfc;

	// TODO: 在此添加控件通知处理程序代码
	CFile file(m_showLogPath, CFile::modeRead);

	int len = file.GetLength();//获取file文件中内容的长度；

	char *data = new char[len + 1];//定义一个存放数据的指针；

	memset(data, 0, len + 1);//   将已开辟内存空间 data的,长度为len+1首 个字节的值设为值 0

	file.Read(data, len);//读取文件内容并赋值给data;

	//std::string _str;
	//_str.assign(data, strlen(data));
	//_str = data;
	//strMfc = _str.c_str();

	/*utf8转GBK*/
	std::string _str;
	_str = Utf8ToGbk(data);
	strMfc = _str.c_str();

	// 将日志内容显示在编辑框中	
	m_editLogCtrl.SetWindowText(strMfc);
	m_editLogCtrl.LineScroll(m_editLogCtrl.GetLineCount());//滚轮默认自动跳转到最后
	m_editLogCtrl.Clear();//测试是否被清空
	return TRUE;
}

//显示日志编辑框
void CldfcrLog::OnEnChangeEditLog()
{
	// TODO:  如果该控件是 RICHEDIT 控件，它将不
	// 发送此通知，除非重写 CDialog::OnInitDialog()
	// 函数并调用 CRichEditCtrl().SetEventMask()，
	// 同时将 ENM_CHANGE 标志“或”运算到掩码中。

	// TODO:  在此添加控件通知处理程序代码
}
