#include "stdafx.h"
#include "DirNode.h" 

#include <list>
#include <sstream>

#ifdef _WIN32
CDirNode::CDirNode(const wchar_t* path_or_name)
#elif defined __GNUC__
CDirNode::CDirNode(const char* path_or_name)
#endif
{
    prev_ = nullptr;
    next_ = nullptr;

    parent_ = nullptr;
    childs_ = nullptr;
    last_child_ = nullptr;

    dealed_  = false;
    path_ = path_or_name;
}

#ifdef _WIN32
CDirNode::CDirNode(const std::wstring path_or_name)
#elif defined __GNUC__
CDirNode::CDirNode(const std::string path_or_name)
#endif
{
    prev_ = nullptr;
    next_ = nullptr;

    parent_ = nullptr;
    childs_ = nullptr;
    last_child_ = nullptr;

    dealed_  = false;
    path_ = path_or_name;
}

//释放并删除节点
void CDirNode::release()
{
    CDirNode* node_p = this->childs_;
    while( nullptr != node_p )
    {
        CDirNode* todel_p = node_p;
        node_p = node_p->next_;

        todel_p->release();
    }

    delete this;
}

#ifdef _WIN32
std::wstring CDirNode::getFullPath()
#elif defined __GNUC__
std::string CDirNode::getFullPath()
#endif
{
    std::list<CDirNode*>  list;

    CDirNode* temp_node_p = this;
    while( nullptr != temp_node_p )
    {
        list.push_front( temp_node_p );
        temp_node_p = temp_node_p->parent_;
    }

#ifdef _WIN32
    std::wstringstream ss;
#elif defined (__GNUC__)
    std::stringstream ss;
#endif
    std::list<CDirNode*>::iterator iter;
    for( iter = list.begin(); iter != list.end(); iter ++ )
    {
        CDirNode* node_p = *iter;
        ss << node_p->path_;
        ss << "\\";
    }

    return ss.str();
}

//从节点树中脱离，以便删除
CDirNode* CDirNode::detach()
{
    CDirNode* next_p = nullptr;
    if( nullptr == this->parent_ )
    {//当前节点是顶层节点
        //无从脱离
        return nullptr;
    }

    //非顶层节点
    if( nullptr == this->prev_ )
    {//表示是第一个子节点
        if( nullptr != this->next_ )
        {
            this->next_->prev_ = nullptr;
        }
        this->parent_->childs_ = this->next_;
    }else
    {//表示不是第一个子节点
        if( nullptr != this->next_ )
        {
            this->next_->prev_ = this->prev_;
        }
        this->prev_->next_ = this->next_;
    }

    if( this->parent_->last_child_ == this )
    {//移除的是尾节点，则需要调整
        this->parent_->last_child_ = this->prev_;
    }

    next_p = ( nullptr == this->next_ )
        ? this->parent_ : this->next_;

    //当前节点彻底断开
    this->prev_ = nullptr;
    this->next_ = nullptr;
    this->parent_ = nullptr;
    //子节点不能删除

    return next_p;
}
#ifdef _WIN32
void CDirNode::addChild(const std::wstring& dir_name)
#elif defined __GNUC__
void CDirNode::addChild(const std::string& dir_name)
#endif
{
    CDirNode* new_node_p = new CDirNode(dir_name);
    new_node_p->parent_ = this;

    if( nullptr == this->last_child_ )
    {
        this->childs_ = new_node_p;
        this->last_child_ = new_node_p;
    }else
    {
        this->last_child_->next_ = new_node_p;
        new_node_p->prev_ = this->last_child_;

        last_child_ = new_node_p;
    }
}
