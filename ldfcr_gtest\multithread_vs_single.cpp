#include <vector>
#include <future>
#include <iostream>

#include "test_class.h"

timespec diff(timespec start, timespec end)
{
    timespec temp;
    if ((end.tv_nsec-start.tv_nsec)<0) {
        temp.tv_sec = end.tv_sec-start.tv_sec-1;
        temp.tv_nsec = 1000000000+end.tv_nsec-start.tv_nsec;
    } else {
        temp.tv_sec = end.tv_sec-start.tv_sec;
        temp.tv_nsec = end.tv_nsec-start.tv_nsec;
    }
    return temp;
}

void work(Ldfcr &instance, const char *test_file, int iteration, std::promise<double> promise)
{
    //clock_t start = clock();
    struct timespec start, end;
    clock_gettime(CLOCK_MONOTONIC, &start);

    for (auto i = 0; i < iteration; ++i)
    {
        //std::cout << "detect " << test_file << " [" << i+1 << "]" << std::endl;
        instance.detect(test_file, nullptr, nullptr, 0);
    }

    clock_gettime(CLOCK_MONOTONIC, &end);
    struct timespec eclapsed = diff(start, end);
    double e = eclapsed.tv_sec + eclapsed.tv_nsec / 1e9;
    promise.set_value(e);
}

int multithread_test(const char *strategy_file, const char *test_file, int num_of_instance, int iterations)
{
    Ldfcr ldfcr;
    std::promise<double> promises[num_of_instance];
    std::vector<std::future<double>> futures;
    std::thread  threads[num_of_instance];
    std::map<int, double> map_results;

    std::cout << "Start multithread test" << std::endl;

    ldfcr.init(strategy_file);

    for (int i = 0; i < num_of_instance; ++i)
    {
        futures.push_back(promises[i].get_future());
    }

    for (int i = 0; i < num_of_instance; ++i)
    {
        threads[i] = std::thread(work, std::ref(ldfcr), test_file, iterations, std::move(promises[i]));
    }

    for (auto i = 0; i < num_of_instance; ++i)
    {
        threads[i].join();
        map_results[i] = futures[i].get();
    }

    for (auto&& pair: map_results)
    {
        std::cout << pair.first << ": " << pair.second << std::endl;
    }
    return 0;
}
