
// ldfcrTesterDlg.h : 头文件
//

#pragma once
#include "Mode_P_Dialog.h"
#include "Setting_Dialog.h"
#include "afxcmn.h"
#include "afxwin.h"
#include <windows.h>
#include <tchar.h>
#include "ldfcr.h"
#include "PublicFunction.h"
#include "DragEdit.h"

#pragma comment(lib, "dbghelp.lib")
#pragma comment(lib, "version.lib")

// CldfcrTesterDlg 对话框
class CldfcrTesterDlg : public CDialogEx
{
// 构造
public:
	CldfcrTesterDlg(CWnd* pParent = NULL);	// 标准构造函数

// 对话框数据
#ifdef AFX_DESIGN_TIME
	enum { IDD = IDD_LDFCRTESTER_DIALOG };
#endif

	protected:
	virtual void DoDataExchange(CDataExchange* pDX);	// DDX/DDV 支持


// 实现
protected:
	HICON m_hIcon;

	// 生成的消息映射函数
	virtual BOOL OnInitDialog();
	afx_msg void OnSysCommand(UINT nID, LPARAM lParam);
	afx_msg void OnPaint();
	afx_msg HCURSOR OnQueryDragIcon();
	DECLARE_MESSAGE_MAP()
public:

	//2023 12 8 重构部分 选择模式改为选项卡模式 以下为选项卡变量参数
	afx_msg void OnTcnSelchangeTab1(NMHDR *pNMHDR, LRESULT *pResult);
	int m_CurSelTab;
	Mode_P_Dialog p_page;
	Setting_Dialog s_page;
	CDialog* pDialog[2];  //用来保存对话框对象指针
	CTabCtrl m_tab;
	afx_msg void OnBnClickedLogChoose();
public:
	CString GetProPath();
	CDragEdit m_CShowLogPathEdit;
	afx_msg void OnBnClickedLog();
	void initLog();
	CString m_strShowLogPath;

private:
	virtual void OnOK();
	virtual void OnCancel();
public:
	afx_msg void OnClose();
	afx_msg bool testLink(const char * v_ip);

	// 标题
	BOOL OnNewDocument();
	string GetSoftVersion(const char* exepath);
	CString returnProName();
	afx_msg void OnBnClickedBtnDir();
};
