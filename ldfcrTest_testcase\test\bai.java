#pragma once

#ifndef PUBLIC_DATATYPE_DEF
#define PUBLIC_DATATYPE_DEF
//=====================================================================================



#ifdef WIN32
#pragma warning(disable:4200)
#endif

#include <list>
#include <vector>
#include <string>
#include <map>
#include <stack>
#include <set>
#include <memory>
#ifdef __GNUC__
#include <stdint.h>
#else
#include <limits.h>
#ifndef UINT8_MAX
#define UINT8_MAX UCHAR_MAX
#endif
#endif

#include "ExportDef.h"

#include "rapidjson_wrapper_inc.h"	//太多类引用了这个头文件中的类型，不得不在此处引用

typedef struct _tagTextInfo
{
    String 	m_sMatched;
    /*
     * 匹配的字符串周边字符串,
     * 共享指针也许更合适，当两个匹配相距很近时,
     * 可能还有一个合并的过程，或许这些可以在一次检测的末尾统一来计算
     */
    String 	m_sNearbyText;
    String  m_sFilename;
    String  m_sFilePath;
    String  m_sFcrInfo;
    UInt32	m_ui32OpType;
    time_t	m_timestamp;
} TEXT_INFO, *PTEXT_INFO;

// 文本位置定义，定义在文本串中的偏移位置
// 用于文本串位置判断和文本串截取时使用
// TEXT_POS is POD struct
typedef struct _tagTextPos
{
    _tagTextPos()
    {
        m_ui32CharPos = 0;
        m_ui32CharLen = 0;
        m_ui32BytePos = 0;
        m_ui32ByteLen = 0;
        m_ui8StrmType = 0;
    }

    _tagTextPos( UInt32 _ui32CharPos, UInt32 _ui32CharLen,
                 UInt32 _ui32BytePos, UInt32 _ui32ByteLen )
    {
        m_ui32CharPos = _ui32CharPos;
        m_ui32CharLen = _ui32CharLen;
        m_ui32BytePos = _ui32BytePos;
        m_ui32ByteLen = _ui32ByteLen;
        m_ui8StrmType = 0;
    }

    // 文本字符偏移（按字符计算，一个汉字也是一个字符）
    UInt32 m_ui32CharPos;
    UInt32 m_ui32CharLen;
    // 文本字节偏移（按字节计算，一个汉字可能是2个或3个字节）
    UInt32 m_ui32BytePos;
    UInt32 m_ui32ByteLen;
    // 匹配所在的文本类型
    UInt8  m_ui8StrmType;
} TEXT_POS,*PTEXT_POS;

typedef struct DripTextPos : public _tagTextPos
{
    DripTextPos()
    {
        m_pDripInfo = NULL;
    }
    DripTextPos(UInt32 _ui32CharPos, UInt32 _ui32CharLen,
                UInt32 _ui32BytePos, UInt32 _ui32ByteLen, time_t ts) :
            _tagTextPos(_ui32CharPos, _ui32CharLen,
                        _ui32BytePos, _ui32ByteLen)
    {
    }

    DRIP_INFO *m_pDripInfo;	// 匹配的文本信息

}DRIP_TEXT_POS,*PDRIP_TEXT_POS;

#define DRIP_TIMESTAMP(d)	(d)->m_timestamp



//>>>>BEGIN-下述数据结构用于缓存已经触发的策略信息

// 条件中满足的文本关键字及位置信息
typedef struct _tagTextValuePos
{
    _tagTextValuePos()
    {
        m_ui32OpType = 0;
        m_pTextInfo = NULL_PTR;
    }

    String               m_strValue;      // 字符串值,匹配的关键字
    UInt32               m_ui32OpType;	  // 操作类型

	String				 m_MatchKeyWord;  // 记录匹配的原关键字
	UInt32               m_ui32KeyType;	  // 关键字类型 1-单关键词，2-关键词对，3-关键词组
	UInt32               m_ui32KeyIndex;  // 记录一策略内出现多个关键字系统自动分配的index
    // 匹配的文本信息
    PTEXT_INFO	 		 m_pTextInfo;

    std::list<TEXT_POS>  m_lstPos;        // 位置链表
}TEXT_VALUE_POS,*PTEXT_VALUE_POS;

typedef struct _tagTextValueDripPos
{
    _tagTextValueDripPos()
    {
        m_strValue.clear();
        m_lstPos.clear();
    }

    String               	  m_strValue;      // 字符串值
    std::list<DRIP_TEXT_POS>  m_lstPos;        // 位置链表
}TEXT_VALUE_DRIP_POS,*PTEXT_VALUE_DRIP_POS;


// 触发的检测规则
typedef struct _tagFireCheckRule
{
    _tagFireCheckRule()
    {
        m_ui64CheckRuleId = 0;
        m_lstTextVP.clear();
    }

    UInt64                    m_ui64CheckRuleId;  // 检测规则ID（后台分配）
    std::list<TEXT_VALUE_POS> m_lstTextVP;        // 规则中满足的文本位置信息
    String                    m_strRuleName;      // 规则名称
	UInt32						m_ui32RuleType;   // 规则类型
}FIRE_CHECK_RULE,*PFIRE_CHECK_RULE;

typedef std::list<FIRE_CHECK_RULE> ArryFireCRule;

//触发的零星检测规则
typedef struct _tagFireDripRule
{
    _tagFireDripRule()
    {
        m_ui64RuleId = 0;
        m_ui32Times = 0;
        m_dUnitWeight = 0;
        m_bUniqueMatch = false;
    }
    UInt64  m_ui64RuleId;
    UInt32  m_ui32Times;
    double  m_dUnitWeight;
    bool 	m_bUniqueMatch;

    String        m_sRuleName;
    String        m_sTimeSpan;
    String		  m_sOpTypes;
    std::list<FIRE_CHECK_RULE> m_arryFireCheckRule;
} FIRE_DRIP_RULE, *PFIRE_DRIP_RULE;


class RespAlarmInfo;

// 触发的响应规则
typedef struct _tagFireRespondRule
{
    _tagFireRespondRule( UInt64 _ui64Id,
                         Char* _pWarnContent,
                         bool _bStop,
						 bool _bOutSendApply,
						 UInt32 _ui32StopOutGoingEx,
                         Char* _pHandler,
                         bool _bMathcRespRule,
                         bool _bTakeScreenshot,
                         bool _bSendmail,
                         RespAlarmInfo *_psetting,
						 Char* _pLossType)
    {
        m_ui64RespondRuleId = _ui64Id;
        m_strWarnContent = _pWarnContent;
        m_bStopOutgoing = _bStop;
		m_bOutSendApply = _bOutSendApply;
		m_ui32StopOutgoingEx = _ui32StopOutGoingEx;
        m_strHandler = _pHandler;
        m_bMathcRespRule = _bMathcRespRule;
        m_bTakeScreenshot = _bTakeScreenshot;
        m_bSendMail = _bSendmail;
        m_pPopupSetting = _psetting;
		m_strLossType = _pLossType;
    }

    UInt64              m_ui64RespondRuleId;      // 响应规则ID（后台分配）
    String              m_strWarnContent;         //弹窗提示
    bool                m_bStopOutgoing;          //是否阻断
	bool                m_bOutSendApply;          //是否允许外发申请
	UInt32              m_ui32StopOutgoingEx;     //阻断外发的文件类型
    String              m_strHandler;             //事件处理者
    bool                m_bMathcRespRule;
    bool				m_bTakeScreenshot;		  //是否截屏
    bool				m_bSendMail;			  //是否发送邮件
    RespAlarmInfo		*m_pPopupSetting;		  //弹窗设置
	String              m_strLossType;
}FIRE_RESPOND_RULE,*PFIRE_RESPOND_RULE;

typedef std::map<UInt32, FIRE_DRIP_RULE>  MapIdFireDRule;
typedef std::map<UInt32, FIRE_CHECK_RULE> MapIdFireCRule;

// 触发的分类
typedef struct _tagFireClassify
{
    _tagFireClassify()
    {
        m_ui64ClassifyId = 0;
        m_mapFireCheckRule.clear();
        m_dTotalWeight = 0;
    }

    double						 m_dTotalWeight;	  // 满足分类的总权重值
    UInt64              		 m_ui64ClassifyId;    // 分类ID（后台分配）
    MapIdFireCRule  			 m_mapFireCheckRule;  // 分类中满足的规则信息
    MapIdFireDRule  			 m_mapFireDripRule;	  // 分类中满足的零星规则信息
    String						 m_sCheckRuleExpr;	  // 匹配规则的逻辑关系
	String						 m_strClssifyName;	  // 分类名称
}FIRE_CLASSIFY,*PFIRE_CLASSIFY;

typedef std::map<UInt32, FIRE_CLASSIFY> MapIdFireClassify;

// 触发的策略
typedef struct _tagFireStrategy
{
    _tagFireStrategy(bool _bIsDrip = false)
    {
        m_ui64StrageId = 0;
        m_bIsDrip = _bIsDrip;
        m_strSeverityLevel.clear();
        m_mapFireClassify.clear();
        m_lstFireRespondRule.clear();
		m_strOSOptype.clear();
    }

    UInt64                           m_ui64StrageId;       // 策略ID（后台分配）
    String                           m_strSeverityLevel;   // 严重等级
    String                           m_strStrategyName;	   // 策略名称
    MapIdFireClassify                m_mapFireClassify;    // 策略中满足的分类信息
    bool                             m_bIsDrip;            // 零星检测
    std::list<FIRE_RESPOND_RULE>     m_lstFireRespondRule; // 策略的响应规则
	std::string                      m_strOSOptype;        // 策略的允许文件外发申请的操作类型
}FIRE_STRATEGY,*PFIRE_STRATEGY;

//<<<<END  -上述数据结构用于缓存已经触发的策略信息

//=====================================================================================
#endif
