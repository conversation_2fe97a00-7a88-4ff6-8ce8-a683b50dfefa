#pragma once
#include <map>
#include <vector>
// StrategyDlg 对话框

class StrategyDlg : public CDialogEx
{
	DECLARE_DYNAMIC(StrategyDlg)

public:
	StrategyDlg(CWnd* pParent = NULL);   // 标准构造函数
	virtual ~StrategyDlg();

	void OnInitDlg(const std::map<int, std::string> &v_map);

// 对话框数据
#ifdef AFX_DESIGN_TIME
	enum { IDD = IDD_DIALOG_STATEGY_SELECT };
#endif

protected:
	virtual void DoDataExchange(CDataExchange* pDX);    // DDX/DDV 支持

	DECLARE_MESSAGE_MAP()
public:
	virtual void OnOK();
	virtual void OnCancel();
	afx_msg void OnClose();
	afx_msg void OnBnClickedOk();
	CListCtrl m_stategySelect;

	std::vector<CString> GetStrategyBeChosed();
private:
	std::map<int, std::string> m_map;
	std::vector<CString> m_selected;

	void GetClickedStrategy();
public:
	virtual BOOL OnInitDialog();
};
