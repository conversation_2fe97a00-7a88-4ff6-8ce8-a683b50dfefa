#include "stdafx.h"
#include "ConfigLink.h"
#include "PublicFunction.h"

using namespace std;

ConfigLink::ConfigLink()
{

	m_fcrParam = new(std::nothrow) FCRPARAM();
	memset(m_fcrParam, 0, sizeof(m_fcrParam));  // 初始化置空
	m_fcrParam->strategyIdsArray = new UINT64;
	m_fcrParam->disableTEModules = 0;

	ini_pwd_error = 0;
	ini_optype = 0;
	ini_outTimeJug = 0;
	ini_iCount = 0;
	ini_bR_json_tag = 0;
	ini_bCheck_ALL = 0;
	ini_Context_count = 0;

	ini_password.clear();
	ini_key.clear();

}

ConfigLink::~ConfigLink()
{
	delete m_fcrParam->strategyIdsArray;
}

void ConfigLink::GetIniConfig(const IniSetting& v_instance)
{

	// Moudle 
	int v_filefp = v_instance.getInstance().GetFileFp();
	int v_svm = v_instance.getInstance().GetSVM();
	int v_filedb = v_instance.getInstance().GetFileDB();
	int v_ocr_type = v_instance.getInstance().GetOCRType();
	int v_ocr_embed = v_instance.getInstance().GetOCREmbed();
	int v_ocr = v_instance.getInstance().GetOCR();
	if (v_filefp == 1)
	{
		ldfcr_ControlModule(LDFCR_MODULE_FILEFP, 1);
	}
	else
	{
		ldfcr_ControlModule(LDFCR_MODULE_FILEFP, 0);
	}

	if (v_svm == 1)
	{
		ldfcr_ControlModule(LDFCR_MODULE_SVM, 1);
	}
	else
	{
		ldfcr_ControlModule(LDFCR_MODULE_SVM, 0);
	}

	if (v_filedb == 1)
	{
		ldfcr_ControlModule(LDFCR_MODULE_DBFP, 1);
	}
	else
	{
		ldfcr_ControlModule(LDFCR_MODULE_DBFP, 0);
	}

	if (v_ocr_embed == 1)
	{
		ldfcr_ControlModule(LDFCR_MODULE_OCR_EMBED, 1);
	}
	else
	{
		ldfcr_ControlModule(LDFCR_MODULE_OCR_EMBED, 0);
	}

	if (v_ocr_type == 1)
	{
		ldfcr_ControlModule(LDFCR_MODULE_OCR_TYPE, 1);
	}
	else
	{
		ldfcr_ControlModule(LDFCR_MODULE_OCR_TYPE, 0);
	}

	if (v_ocr == 1)
	{
		ldfcr_ControlModule(LDFCR_MODULE_OCR, 1);
	}
	else
	{
		ldfcr_ControlModule(LDFCR_MODULE_OCR, 0);
	}

	unsigned int v_enFM_OCR = stoi(v_instance.getInstance().GetOCRenFM(),nullptr,16);
	unsigned int v_enFM_OCR_IN_CHILD = stoi(v_instance.getInstance().GetOCRenFMInChild(),nullptr,16);
	ini_disableTEModules = v_enFM_OCR | v_enFM_OCR_IN_CHILD;

	ini_pwd_error = v_instance.getInstance().GetPwdStop();
	ini_optype = v_instance.getInstance().GetOptype();
	ini_outTimeJug = v_instance.getInstance().GetOutTimes();
	ini_outTime_b = v_instance.getInstance().GetOutTime();
	ini_iCount = v_instance.getInstance().GetCirCulate();
	const char* v_password = v_instance.getInstance().GetPassWord();
	ini_bR_json_tag = v_instance.getInstance().GetCheckTag();
	ini_bCheck_ALL = v_instance.getInstance().GetCheckAll();
	ini_Context_count = v_instance.getInstance().GetContextLength();
	return;
}

void ConfigLink::SetIniConfig()
{
	std::string str;
	if (!m_fcrParam)
	{
		return;
	}
	m_fcrParam->interrupt = 0;
	m_fcrParam->block_on_timeout = ini_outTime_b;	//超时是否阻断
	m_fcrParam->timeout = ini_outTimeJug;		//超时时间
	m_fcrParam->block_on_pwderror = ini_pwd_error;  //设置密码错误是否返回 2023 Q1

	//2025 6 26 模块小开关
	m_fcrParam->disableTEModules = ini_disableTEModules;

	string password = ini_password;  //ini 中获取的password值

	passwordJson = ("{\"password\": [\"" + password + "\"]}");

	m_fcrParam->devType = 1;			//设备类型
	m_fcrParam->opType = ini_optype;	//操作类型
	m_fcrParam->use_all_rule = TRUE;   //启用所有规则
	m_fcrParam->target_class_code = 3; //达到或超过代码3，即停止
	m_fcrParam->output_detail = TRUE;  //要求返回时输出策略的细节信息
	m_fcrParam->fcrInfo = "{\"IP\":\"***********\"}";
	m_fcrParam->onlyreturnTagRespond = ini_bR_json_tag;
	m_fcrParam->scanWholeFile = ini_bCheck_ALL;
	m_fcrParam->m_nearbyLen = ini_Context_count;
}

FCRPARAM* ConfigLink::Getfcrparm()
{
	return this->m_fcrParam;
}

int ConfigLink::GetCheckCount()
{
	return ini_iCount;
}

std::string ConfigLink::GetPassWordJson()
{
	return this->passwordJson;
}
