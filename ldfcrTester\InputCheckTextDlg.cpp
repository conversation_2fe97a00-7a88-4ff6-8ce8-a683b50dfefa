// InputCheckTextDlg.cpp : 实现文件
//

#include "stdafx.h"
#include "ldfcrTester.h"
#include "InputCheckTextDlg.h"
#include "DragEdit.h"
#include "DlpULog.h"

extern int  g_log_handle;


// CInputCheckTextDlg 对话框

IMPLEMENT_DYNAMIC(CInputCheckTextDlg, CDialog)

CInputCheckTextDlg::CInputCheckTextDlg(CWnd* pParent /*=NULL*/)
	: CDialog(CInputCheckTextDlg::IDD, pParent)
    , m_strCheckText(_T(""))
	, m_Address(_T(""))
{

}

CInputCheckTextDlg::~CInputCheckTextDlg()
{
}

void CInputCheckTextDlg::DoDataExchange(CDataExchange* pDX)
{
	CDialog::DoDataExchange(pDX);
	DDX_Text(pDX, IDC_CHECK_TEXT, m_strCheckText);
	DDX_Control(pDX, IDC_EDIT2, m_CEdit);
	DDX_Text(pDX, IDC_EDIT2, m_Address);
	DDX_Control(pDX, IDC_CHECK_TEXT, m_EditContext);
}


BEGIN_MESSAGE_MAP(CInputCheckTextDlg, CDialog)
    //ON_BN_CLICKED(IDOK, &CInputCheckTextDlg::OnBnClickedOk)
	//ON_EN_CHANGE(IDC_CHECK_TEXT, &CInputCheckTextDlg::OnEnChangeCheckText)
	ON_BN_CLICKED(inPut, &CInputCheckTextDlg::OnBnClickedinput)
	ON_BN_CLICKED(IDC_OPEN, &CInputCheckTextDlg::OnBnClickedOpen)
END_MESSAGE_MAP()


// CInputCheckTextDlg 消息处理程序

//void CInputCheckTextDlg::OnBnClickedOk()
//{
//    // TODO: 在此添加控件通知处理程序代码
//
//    this->UpdateData(TRUE);
//
//    OnOK();
//}

//屏蔽 enter和esc
void CInputCheckTextDlg::OnOK()
{
	// TODO: 在此添加专用代码和/或调用基类

	//CDialog::OnOK();
}


void CInputCheckTextDlg::OnCancel()
{
	// TODO: 在此添加专用代码和/或调用基类

	CDialog::OnCancel();
}


void CInputCheckTextDlg::OnBnClickedinput()
{
	this->UpdateData(TRUE);
	// TODO: 在此添加控件通知处理程序代码
	CDialog::EndDialog(IDOK);
}

std::string CInputCheckTextDlg::Ws2S(const std::wstring v_wstr)
{// wstring转string
	unsigned len = v_wstr.size() * 4;
	setlocale(LC_CTYPE, "");
	char *p = new char[len];
	wcstombs(p, v_wstr.c_str(), len);
	std::string str1(p);
	delete[] p;
	return str1;
}

std::string CInputCheckTextDlg::WstringToString(const std::wstring v_wstr)
{// wstring转string
	unsigned len = v_wstr.size() * 4;
	setlocale(LC_CTYPE, "");
	char *p = new char[len];
	wcstombs(p, v_wstr.c_str(), len);
	std::string str1(p);
	delete[] p;
	return str1;
}

void CInputCheckTextDlg::OnBnClickedOpen()
{
	// TODO: 在此添加控件通知处理程序代码
	this->UpdateData(TRUE);
	std::string strContext;
	if (!m_Address.IsEmpty())
	{
		std::string textname(WstringToString(this->m_Address.GetString()));
		dlplog_info(g_log_handle, ("文本名: " + textname).c_str());
		//行读取
		int count = 1;
		std::ifstream fin;
		std::string chFilePath = Ws2S(m_Address.GetBuffer());
		//in 向内输入 读文件
		//out 向外输入 写文件
		//trunc 覆盖删除原文件 
		//ate 初始位置，文件尾 
		//app 文件末尾 
		//binary 二进制方式
		fin.open(chFilePath, std::ios::in);
		if (!fin.is_open())
		{
			dlplog_debug(g_log_handle, "[%s] Failed to open text!", __FUNCTION__);
			return;
		}
		std::string line;
		while (getline(fin, line))
		{
			count++;
			strContext += line;
		}
		fin.close();
	}
	CString cstrRes;
	cstrRes = Utf8ToGbk(strContext.c_str()).c_str();
	m_EditContext.SetWindowTextW(cstrRes);
}

std::string CInputCheckTextDlg::GbkToUtf8(const char *src_str)
{
	int len = MultiByteToWideChar(CP_ACP, 0, src_str, -1, NULL, 0);
	wchar_t* wstr = new wchar_t[len + 1];
	memset(wstr, 0, len + 1);
	MultiByteToWideChar(CP_ACP, 0, src_str, -1, wstr, len);
	len = WideCharToMultiByte(CP_UTF8, 0, wstr, -1, NULL, 0, NULL, NULL);
	char* str = new char[len + 1];
	memset(str, 0, len + 1);
	WideCharToMultiByte(CP_UTF8, 0, wstr, -1, str, len, NULL, NULL);
	std::string strTemp = str;
	if (wstr) delete[] wstr;
	if (str) delete[] str;
	return strTemp;
}

std::string CInputCheckTextDlg::Utf8ToGbk(const char *src_str)
{
	int len = MultiByteToWideChar(CP_UTF8, 0, src_str, -1, NULL, 0);
	wchar_t* wszGBK = new wchar_t[len + 1];
	memset(wszGBK, 0, len * 2 + 2);
	MultiByteToWideChar(CP_UTF8, 0, src_str, -1, wszGBK, len);
	len = WideCharToMultiByte(CP_ACP, 0, wszGBK, -1, NULL, 0, NULL, NULL);
	char* szGBK = new char[len + 1];
	memset(szGBK, 0, len + 1);
	WideCharToMultiByte(CP_ACP, 0, wszGBK, -1, szGBK, len, NULL, NULL);
	std::string strTemp(szGBK);

	if (wszGBK)
		delete[] wszGBK;

	if (szGBK)
		delete[] szGBK;
	return strTemp;
}
