{"respondRule": [{"sendMail": false, "enableCancel": 0, "filters": [{"type": "deviceType", "relation": "either", "object": ["terminal-agent"]}, {"type": "lossType", "relation": "either", "object": ["8", "9", "26", "25", "2", "3", "4", "14", "15", "16", "11", "1", "18", "5", "17", "7", "23", "24", "13", "0", "20", "27", "29"]}], "alarmInfo": {"alarmLimit": 3, "msgFormType": 0, "deleted": 0, "configMethod": 1, "name": "响应规则", "msgFormPosition": 0, "id": 1, "msgFormClose": 30}, "outSendApply": 0, "stopOutgoing": 1, "takeScreenshot": false, "stopOutgoingEx": 7, "createTime": 1694585573000, "name": "响应规则", "id": 2, "warnContent": ""}], "checkRule": [{"exceptWord": "", "createTime": 1695284262000, "ruleType": "6", "name": "文件指纹", "id": 20, "type": 6, "fingerPrint": "10433309130699954199"}], "strategy": [{"severity": "1", "respondRule": "2", "createTime": 1695285446548, "checkRule": "20", "name": "文件指纹检测", "dripScan": false, "id": 4, "classification": [{"checkExpr": "20", "name": "文件指纹集", "exceptRule": "", "id": 4}]}], "businessType": {"opTypes": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 20], "type": 99}}