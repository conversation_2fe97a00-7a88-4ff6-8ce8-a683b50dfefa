#pragma once
#include "stdafx.h"
#include "resource.h"		// 主符号
#include "PublicFunction.h"
#include "CodeT.h"
#include <fstream>
#include "DirNode.h"
#include "rapidjson/document.h"
#include "rapidjson/error/en.h"
#include <list>
#include "SimpleIni.h"

extern CSimpleIniA ini;

using namespace rapidjson;

// 读取文本文件内容
bool ReadTextContent(const char* file_path, std::string& text)//1008
{
	FILE* file = fopen(file_path, "rb");
	if (file)
	{
		fseek(file, 0, SEEK_END);
		long size = ftell(file);
		fseek(file, 0, SEEK_SET);

		if (size > 0)
		{
			text.resize(size);
			fread(&text[0], 1, size, file);
		}
		fclose(file);
		return true;
	}
	return false;
}

//提取策略id和策略名称
void ExtractIdWithName(const CString& filePath, map<int, string>& idNameMap)
{
	ifstream file(filePath);

	//文件打开失败
	if (!file.is_open())
	{
		return;
	}

	//读取文件内容
	string logContent((istreambuf_iterator<char>(file)), istreambuf_iterator<char>());

	//关闭文件
	file.close();

	//创建Document对象
	Document document;

	//解析JSON
	document.Parse(logContent.c_str());

	//检查解析是否成功
	if (document.HasParseError())
	{
		return;
	}
	if (!document.IsObject())
	{
		return;
	}

	//检查json格式，判断是否包含"strategy"数组字段
	const Value& strategies = document["strategy"];
	//不包含"strategy"字段
	if (!strategies.IsArray())
	{
		return;
	}

	//给容器一个0值，选中0值得时候默认检测所有策略
	idNameMap[0] = "all strategies";

	//遍历 "strategy" 数组元素
	for (SizeType i = 0; i < strategies.Size(); ++i)
	{
		const Value& strategy = strategies[i];
		//包含id和name字段
		if (strategy.HasMember("id") && strategy.HasMember("name") && strategy["id"].IsInt() && strategy["name"].IsString())
		{
			//提取id和name
			int strategyId = strategy["id"].GetInt();
			string strategyName = strategy["name"].GetString();

			//存储到map
			idNameMap[strategyId] = strategyName.c_str();
		}
	}
}

/*
* 函数：获取运行程序目录
* 功能：可获取运行程序的目录
*/
CString GetProPath()
{

	WCHAR wcsAppDir[MAX_PATH];
	::GetModuleFileNameW(NULL, wcsAppDir, sizeof(wcsAppDir) / sizeof(WCHAR));
	int  iLen = ::wcslen(wcsAppDir);
	while (0 < iLen)
	{
		if (L'\\' == wcsAppDir[iLen - 1])
		{
			wcsAppDir[iLen - 1] = 0;
			break;
		}

		iLen--;
	}

	CString _str = wcsAppDir;
	return _str;
}

void ergodicFolder(const wchar_t * wch, std::list<std::wstring>& filename_list)
{
	CDirNode* top_node_p = new CDirNode(wch);
	CDirNode* cur_node_p = top_node_p;

	while (NULL != cur_node_p)
	{
		if (cur_node_p->isDealed())
		{//当前节点已经被处理过
			CDirNode* todel_p = cur_node_p;
			cur_node_p = cur_node_p->detach();
			todel_p->release();

			continue;
		}

		cur_node_p->setDealed(true); //标记已经处理

		std::wstring fld_path = cur_node_p->getFullPath();

		//遍历当前文件夹，找出子目录和文件

		//std::list<std::wstring>  filename_list; //保存遍历出来的文件名列表
		std::wstring srch_path = fld_path + L"\\*";

		WIN32_FIND_DATAW  findata;
		HANDLE hFind = FindFirstFileW(srch_path.c_str(), &findata);
		if (INVALID_HANDLE_VALUE == hFind)
		{
			CDirNode* todel_p = cur_node_p;
			cur_node_p = cur_node_p->detach();
			todel_p->release();

			continue;
		}

		while (true)
		{
			std::wstring filename = findata.cFileName;
			if (L"." == filename || L".." == filename)
			{
			}
			else if (FILE_ATTRIBUTE_DIRECTORY == (FILE_ATTRIBUTE_DIRECTORY & findata.dwFileAttributes))
			{//是目录
				cur_node_p->addChild(filename);
			}
			else
			{//其他视为文件
				filename_list.push_back(fld_path + filename);
			}

			if (!FindNextFileW(hFind, &findata)) break;
		}

		FindClose(hFind);

		//如果有子目录节点，那么深度优先展开
		CDirNode* childs = cur_node_p->getChilds();
		if (NULL != childs)
		{
			cur_node_p = childs;
		}
	}

	return;
}

string IniPath()
{
	CString wcsAppDir;
	wcsAppDir = GetProPath();
	CString _str;
	_str += wcsAppDir;
	_str += L"\\ldfcrTest.ini";

	USES_CONVERSION;//声明标识
	const char* _str_str = T2A(_str);

	return _str_str;
}

std::string GetCurrentTie()
{
	std::time_t currentTime = std::time(nullptr);

	std::tm* localtime = std::localtime(&currentTime);

	// [h:m:s]
	std::string strCurTime = "[" + std::to_string(localtime->tm_hour) + ":" + std::to_string(localtime->tm_min) + ":" + std::to_string(localtime->tm_sec) + "]";

	return strCurTime;
}