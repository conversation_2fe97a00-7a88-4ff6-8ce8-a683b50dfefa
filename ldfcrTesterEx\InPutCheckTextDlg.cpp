// InPutCheckTextDlg.cpp : 实现文件
//

#include "stdafx.h"
#include "ldfcrTester.h"
#include "InPutCheckTextDlg.h"
#include "afxdialogex.h"
#include "DlpULog.h"
#include "CodeT.h"

extern int g_log_handle;

// InPutCheckTextDlg 对话框

IMPLEMENT_DYNAMIC(InPutCheckTextDlg, CDialog)

InPutCheckTextDlg::InPutCheckTextDlg(CWnd* pParent /*=NULL*/)
	: CDialog(IDD_DIALOG_TEXT_CHECK, pParent)
	, m_Address(_T(""))
	, m_strCheckText(_T(""))
{

}

InPutCheckTextDlg::~InPutCheckTextDlg()
{
}

void InPutCheckTextDlg::DoDataExchange(CDataExchange* pDX)
{
	CDialog::DoDataExchange(pDX);
	DDX_Control(pDX, IDC_EDIT2, m_cEdit);
	DDX_Text(pDX, IDC_EDIT2, m_Address);
	DDX_Control(pDX, IDC_CHECK_TEXT, m_EditContext);
	DDX_Text(pDX, IDC_CHECK_TEXT, m_strCheckText);
}

void InPutCheckTextDlg::OnOK()
{
}

void InPutCheckTextDlg::OnCancel()
{
}


BEGIN_MESSAGE_MAP(InPutCheckTextDlg, CDialog)
	ON_BN_CLICKED(IDC_OPEN, &InPutCheckTextDlg::OnBnClickedOpen)
	ON_BN_CLICKED(IDC_INPUT, &InPutCheckTextDlg::OnBnClickedInput)
	ON_WM_CLOSE()
END_MESSAGE_MAP()


// InPutCheckTextDlg 消息处理程序
std::string InPutCheckTextDlg::GbkToUtf8(const char *src_str)
{
	int len = MultiByteToWideChar(CP_ACP, 0, src_str, -1, NULL, 0);
	wchar_t* wstr = new wchar_t[len + 1];
	memset(wstr, 0, len + 1);
	MultiByteToWideChar(CP_ACP, 0, src_str, -1, wstr, len);
	len = WideCharToMultiByte(CP_UTF8, 0, wstr, -1, NULL, 0, NULL, NULL);
	char* str = new char[len + 1];
	memset(str, 0, len + 1);
	WideCharToMultiByte(CP_UTF8, 0, wstr, -1, str, len, NULL, NULL);
	std::string strTemp = str;
	if (wstr) delete[] wstr;
	if (str) delete[] str;
	return strTemp;
}

std::string InPutCheckTextDlg::Utf8ToGbk(const char *src_str)
{
	int len = MultiByteToWideChar(CP_UTF8, 0, src_str, -1, NULL, 0);
	wchar_t* wszGBK = new wchar_t[len + 1];
	memset(wszGBK, 0, len * 2 + 2);
	MultiByteToWideChar(CP_UTF8, 0, src_str, -1, wszGBK, len);
	len = WideCharToMultiByte(CP_ACP, 0, wszGBK, -1, NULL, 0, NULL, NULL);
	char* szGBK = new char[len + 1];
	memset(szGBK, 0, len + 1);
	WideCharToMultiByte(CP_ACP, 0, wszGBK, -1, szGBK, len, NULL, NULL);
	std::string strTemp(szGBK);

	if (wszGBK)
		delete[] wszGBK;

	if (szGBK)
		delete[] szGBK;
	return strTemp;
}


void InPutCheckTextDlg::OnBnClickedOpen()
{
	// TODO: 在此添加控件通知处理程序代码
	this->UpdateData(TRUE);
	std::string strContext;
	if (!m_Address.IsEmpty())
	{
		std::string textname(wstringToString(this->m_Address.GetString()));
		dlplog_info(g_log_handle, ("文本名: " + textname).c_str());
		//行读取
		int count = 1;
		std::ifstream fin;
		std::string chFilePath = wstringToString(m_Address.GetBuffer());
		//in 向内输入 读文件
		//out 向外输入 写文件
		//trunc 覆盖删除原文件 
		//ate 初始位置，文件尾 
		//app 文件末尾 
		//binary 二进制方式
		fin.open(chFilePath, std::ios::in);
		if (!fin.is_open())
		{
			dlplog_debug(g_log_handle, "[%s] Failed to open text!", __FUNCTION__);
			return;
		}
		std::string line;
		while (getline(fin, line))
		{
			count++;
			strContext += line;
		}
		fin.close();
	}
	CString cstrRes;
	cstrRes = Utf8ToGbk(strContext.c_str()).c_str();
	m_EditContext.SetWindowTextW(cstrRes);
}


void InPutCheckTextDlg::OnBnClickedInput()
{
	this->UpdateData(TRUE);
	// TODO: 在此添加控件通知处理程序代码
	CDialog::EndDialog(IDOK);
}


void InPutCheckTextDlg::OnClose()
{
	// TODO: 在此添加消息处理程序代码和/或调用默认值
	EndDialog(IDCANCEL);
	CDialog::OnClose();
}
