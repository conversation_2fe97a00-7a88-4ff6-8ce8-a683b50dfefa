#pragma once


// InitChose 对话框

class InitChose : public CDialog
{
	DECLARE_DYNAMIC(InitChose)

public:
	InitChose(CWnd* pParent = NULL);   // 标准构造函数
	virtual ~InitChose();

// 对话框数据
#ifdef AFX_DESIGN_TIME
	enum { IDD = IDD_DIALOG_INIT };
#endif

protected:
	virtual void DoDataExchange(CDataExchange* pDX);    // DDX/DDV 支持

	DECLARE_MESSAGE_MAP()
public:
	afx_msg void OnBnClickedButton1();
	afx_msg void OnBnClickedButton2();

	afx_msg void setInitChose(int v_InitChose);
	afx_msg int getInitChose();

private:
	int m_InitChose;
};
