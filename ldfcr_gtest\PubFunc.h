#include <iostream>
#include <vector>
#include <string>
#include <dirent.h>
#include <sys/stat.h>
#include <sys/types.h>
#include <unistd.h>
#include <fstream>
#include <mutex>

#ifdef _WIN32
#elif defined __GNUC__
#endif

using namespace std;

//true：stop  false:start
bool stopFlag;

//两个结构体在linux需要，windows下不需要
typedef struct MEMPACKED         //定义一个mem occupy的结构体
{
    char name1[20];      //定义一个char类型的数组名name有20个元素
    unsigned long MemTotal;
    char name2[20];
    unsigned long MemFree;
    char name3[20];
    unsigned long Buffers;
    char name4[20];
    unsigned long Cached;
    char name5[20];
    unsigned long SwapCached;
}MEM_OCCUPY;

//proc/stat文件结构
//cpu  633666 46912 249878 176813696 782884 2859 19625 0
//cpu0 633666 46912 249878 176813696 782884 2859 19625 0
//intr 5812844
//ctxt 265816063
//btime 1455203832
//processes 596625
//procs_running 1
//procs_blocked 0

typedef struct CPUPACKED         //定义一个cpu occupy的结构体
{
    char name[20];      //定义一个char类型的数组名name有20个元素
    unsigned int user; //定义一个无符号的int类型的user
    unsigned int nice; //定义一个无符号的int类型的nice
    unsigned int system;//定义一个无符号的int类型的system
    unsigned int idle; //定义一个无符号的int类型的idle
    unsigned int lowait;
    unsigned int irq;
    unsigned int softirq;
}CPU_OCCUPY;

namespace Tipray{

    class CPubFunc
    {
        public:
        static double GetMemoryPercentage()
        {
            #ifdef _WIN32
            MEMORYSTATUS ms;
            ::GlobalMemoryStatus(&ms);
            int nMEM = ms.dwMemoryLoad;

            OSVERSIONINFOEX os;
            os.dwOSVersionInfoSize = sizeof(OSVERSIONINFOEX);
            GetVersionEx((OSVERSIONINFO *)&os);
            if(os.dwMajorVersion < 6)
            {
                unsigned long nTotal = ms.dwTotalPageFile/1024/1024;
                unsigned long nAvail = ms.dwAvailPageFile/1024/1024;
                nMEM = (nTotal -  nAvail) * 100 /nTotal;
            }

            return nMEM;
            #elif defined __GNUC__
            FILE *fd;
            char buff[256];
            MEM_OCCUPY m;
            fd = fopen("/proc/meminfo", "r");
            //MemTotal: 515164 kB
            //MemFree: 7348 kB
            //Buffers: 7892 kB
            //Cached: 241852  kB
            //SwapCached: 0 kB
            //从fd文件中读取长度为buff的字符串再存到起始地址为buff这个空间里
            fgets(buff, sizeof(buff), fd);
            sscanf(buff, "%s %lu ", m.name1, &m.MemTotal);
            fgets(buff, sizeof(buff), fd);
            sscanf(buff, "%s %lu ", m.name2, &m.MemFree);
            fgets(buff, sizeof(buff), fd);
            sscanf(buff, "%s %lu ", m.name3, &m.Buffers);
            fgets(buff, sizeof(buff), fd);
            sscanf(buff, "%s %lu ", m.name4, &m.Cached);
            fgets(buff, sizeof(buff), fd);
            sscanf(buff, "%s %lu", m.name5, &m.SwapCached);

            fclose(fd);     //关闭文件fd
            return 1-((double)(m.MemFree+m.Buffers +m.Cached)/m.MemTotal);
            #endif
        };

        static int get_cpuoccupy(CPU_OCCUPY *cpust) //对无类型get函数含有一个形参结构体类弄的指针O
        {
            FILE *fd;
            char buff[256];
            CPU_OCCUPY *cpu_occupy;
            cpu_occupy = cpust;

            fd = fopen("/proc/stat", "r");
            fgets(buff, sizeof(buff), fd);

            sscanf(buff, "%s %u %u %u %u %u %u %u", cpu_occupy->name, &cpu_occupy->user, &cpu_occupy->nice, &cpu_occupy->system, &cpu_occupy->idle, &cpu_occupy->lowait, &cpu_occupy->irq, &cpu_occupy->softirq);


            fclose(fd);

            return 0;
        }

        static double GetCpuPercentage(CPU_OCCUPY *o, CPU_OCCUPY *n)
        {
            #ifdef _WIN32
            PDH_HQUERY query;
            PDH_HCOUNTER counter;
            PdhOpenQuery(NULL, NULL, &query);
            PdhAddCounter(query, TEXT("\\Processor(_Total)\\% Processor Time"), NULL, &counter);
            PdhCollectQueryData(query);

            Sleep(1000L);

            PdhCollectQueryData(query);

            PDH_FMT_COUNTERVALUE pdhValue;
            DWORD dwValue;
            PdhGetFormattedCounterValue(counter, PDH_FMT_LONG, &dwValue, &pdhValue);
            PdhCloseQuery(query);

            //获取CPU使用率
            int nCPU = long(pdhValue.longValue) ;
            if (nCPU < 0)
            {
                return 0;
            }
            return nCPU;
            #elif defined __GNUC__
            unsigned long od, nd;
            double cpu_use = 0;

            od = (unsigned long)(o->user + o->nice + o->system + o->idle + o->lowait + o->irq + o->softirq);//第一次(用户+优先级+系统+空闲)的时间再赋给od
            nd = (unsigned long)(n->user + n->nice + n->system + n->idle + n->lowait + n->irq + n->softirq);//第二次(用户+优先级+系统+空闲)的时间再赋给od
            double sum = nd - od;
            double idle = n->idle - o->idle;
            cpu_use = idle / sum;
            idle = n->user + n->system + n->nice - o->user - o->system - o->nice;
            cpu_use = idle / sum;
            return cpu_use;
            #endif
        };
    };
}
