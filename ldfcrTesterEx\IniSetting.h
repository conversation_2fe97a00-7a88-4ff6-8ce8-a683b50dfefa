#pragma once
#include "SimpleIni.h"

class IniSetting
{
public:
	static IniSetting& getInstance();

	// 初始化
	bool initSetting();
	// 切换
	bool switchSetting(const int type);
	// 记录修改
	int recordStr2INI(const char* v_recodeType, const char* v_classification, const char* v_strValue);

	int recordCStr2INI(const char* v_recodeType, const char* v_classification, CString v_runningPath);

	int recordDigit2INI(const char* v_recodeType, const char* v_classification, int v_digit);

	int recordPATH2INI(const char* v_recodeType, const CString& v_Path);

	int returnCheckPoint();
public:
	// 各个获取
	// 1 adv filefp
	bool GetFileFp();
	// 2 adv svm 
	bool GetSVM();
	// 3 adv filedb
	bool GetFileDB();
	// 4 ocr type
	bool GetOCRType();
	// 5 ocr embed
	bool GetOCREmbed();
	// 6 ocr
	bool GetOCR();
	// 7 ocr enFM
	const char* GetOCRenFM();
	// 8 ocr enFM IN CHILD
	const char* GetOCRenFMInChild();
	// 9 optype
	int GetOptype();
	// 10 time_out
	bool GetOutTime();  // 超时选项
	// 11 outTimeJug
	unsigned long long GetOutTimes();
	// 12 circulate
	int GetCirCulate();
	// 13 contextLength
	int GetContextLength();
	// 14 checkAll
	bool GetCheckAll();
	// 15 tag
	bool GetCheckTag();
	// 16 errorpwdReturn
	bool GetPwdStop();
	// 17 password
	const char* GetPassWord();
	// 18 thread_count
	int GetThreadCount();
	// 19 strategy_path_common
	const char* GetComStrategyPath();
	// 20 strategy_path_drip
	const char* GetDripStrategyPath();
	// 21 test_path
	const char* GetTestPath();
	// 22 folder_path
	const char* GetFolderPath();
	// 23 ocr_type
	const char* GetOCRJSONtype();
	// 24 adv_key
	const char* GetADVKey();
private:
	IniSetting() = default;
	static IniSetting instance;

	std::string m_iniPath;  // ini path

	CSimpleIniA ini;

private:
	bool m_bfilefp;
	bool m_bsvm;
	bool m_bfiledb;
	bool m_bocr_type;
	bool m_bocr_embed;
	bool m_bocr;

	int m_pwdError;
	bool m_btag;
	bool m_bcheckALL;
	int optype;
	unsigned long long m_outTime; // 超时时间
	int m_btime_out;  // 是否超时阻断

	int circulate;
	int contextCount;

	const char* password;
	const char* adv_key;
	const char* m_enFM_OCR;
	const char* m_enFM_OCR_IN_CHILD;
	const char* m_OCR_JSON_TYPE;

	const char* log_path;
	const char* strategy_common;
	const char* strategy_drip;
	const char* test_path;
	const char* folder_path;
	int thread_count;

private:
	int m_iCheckPointType;
	enum ldfcrCheckType
	{
		pCheck = 0,
		pEncrypt,
		pEncryptTag,
		Global
	};
};

