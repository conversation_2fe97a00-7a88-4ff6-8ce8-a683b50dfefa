#-------------------------------------------------
#
# Project created by QtCreator 2019-09-23T08:33:38
#
#-------------------------------------------------

QT       += core gui

greaterThan(QT_MAJOR_VERSION, 4): QT += widgets

TARGET = ldfcrTester
TEMPLATE = app

# The following define makes your compiler emit warnings if you use
# any feature of Qt which has been marked as deprecated (the exact warnings
# depend on your compiler). Please consult the documentation of the
# deprecated API in order to know how to port your code away from it.
DEFINES += QT_DEPRECATED_WARNINGS

# You can also make your code fail to compile if you use deprecated APIs.
# In order to do so, uncomment the following line.
# You can also select to disable deprecated APIs only up to a certain version of Qt.
#DEFINES += QT_DISABLE_DEPRECATED_BEFORE=0x060000    # disables all the APIs deprecated before Qt 6.0.0

CONFIG += c++11

SOURCES += \
        DirNode.cpp \
        main.cpp \
        mainwindow.cpp \
        slots.cpp

HEADERS += \
        DirNode.h \
        mainwindow.h

FORMS += \
        mainwindow.ui

#QMAKE_LFLAGS += "-Wl,-rpath=/home/<USER>/build-ldfcrTester-Desktop_Qt_5_13_0_GCC_64bit-Release/libs_x64 -Wl,-Bsymbolic"
QMAKE_LFLAGS += "-Wl,-rpath=./libs_x64 -Wl,-Bsymbolic"


# Default rules for deployment.
qnx: target.path = /tmp/$${TARGET}/bin
else: unix:!android: target.path = /opt/$${TARGET}/bin
!isEmpty(target.path): INSTALLS += target

win32:CONFIG(release, debug|release): LIBS += -L$$PWD/../../../../../build-ldfcrTester-Desktop_Qt_5_13_0_GCC_64bit-Release/libs_x64/release/ -ltrcrt
else:win32:CONFIG(debug, debug|release): LIBS += -L$$PWD/../../../../../build-ldfcrTester-Desktop_Qt_5_13_0_GCC_64bit-Release/libs_x64/debug/ -ltrcrt
else:unix: LIBS += -L$$PWD/../../../../../build-ldfcrTester-Desktop_Qt_5_13_0_GCC_64bit-Release/libs_x64/ -ltrcrt

INCLUDEPATH += $$PWD/../../../../../build-ldfcrTester-Desktop_Qt_5_13_0_GCC_64bit-Release/libs_x64
DEPENDPATH += $$PWD/../../../../../build-ldfcrTester-Desktop_Qt_5_13_0_GCC_64bit-Release/libs_x64

win32:CONFIG(release, debug|release): LIBS += -L$$PWD/../../../../../build-ldfcrTester-Desktop_Qt_5_13_0_GCC_64bit-Release/libs_x64/release/ -lldfcr
else:win32:CONFIG(debug, debug|release): LIBS += -L$$PWD/../../../../../build-ldfcrTester-Desktop_Qt_5_13_0_GCC_64bit-Release/libs_x64/debug/ -lldfcr
else:unix: LIBS += -L$$PWD/../../../../../build-ldfcrTester-Desktop_Qt_5_13_0_GCC_64bit-Release/libs_x64/ -lldfcr

INCLUDEPATH += $$PWD/../../../../../build-ldfcrTester-Desktop_Qt_5_13_0_GCC_64bit-Release/libs_x64
DEPENDPATH += $$PWD/../../../../../build-ldfcrTester-Desktop_Qt_5_13_0_GCC_64bit-Release/libs_x64

win32:CONFIG(release, debug|release): LIBS += -L$$PWD/../../../../../build-ldfcrTester-Desktop_Qt_5_13_0_GCC_64bit-Release/libs_x64/release/ -lDlpULog
else:win32:CONFIG(debug, debug|release): LIBS += -L$$PWD/../../../../../build-ldfcrTester-Desktop_Qt_5_13_0_GCC_64bit-Release/libs_x64/debug/ -lDlpULog
else:unix: LIBS += -L$$PWD/../../../../../build-ldfcrTester-Desktop_Qt_5_13_0_GCC_64bit-Release/libs_x64/ -lDlpULog

INCLUDEPATH += $$PWD/../../../../../build-ldfcrTester-Desktop_Qt_5_13_0_GCC_64bit-Release/libs_x64
DEPENDPATH += $$PWD/../../../../../build-ldfcrTester-Desktop_Qt_5_13_0_GCC_64bit-Release/libs_x64
