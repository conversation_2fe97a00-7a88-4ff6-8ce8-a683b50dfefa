#pragma once
#include "DragEdit.h"
#include "afxwin.h"
#include <string>
#include <fstream>

 
// CInputCheckTextDlg 对话框

class CInputCheckTextDlg : public CDialog
{
	DECLARE_DYNAMIC(CInputCheckTextDlg)

public:
	CInputCheckTextDlg(CWnd* pParent = NULL);   // 标准构造函数
	virtual ~CInputCheckTextDlg();

// 对话框数据
	enum { IDD = IDD_DIALOG_INPUT_CHECK_TEXT };

protected:
	virtual void DoDataExchange(CDataExchange* pDX);    // DDX/DDV 支持

	DECLARE_MESSAGE_MAP()
public:
    CString m_strCheckText;
    afx_msg void OnBnClickedOk();
	afx_msg void OnEnChangeCheckText();
	virtual void OnOK();
	virtual void OnCancel();
	afx_msg void OnBnClickedinput();
	CDragEdit m_CEdit;
	CString m_Address;
	afx_msg std::string Ws2S(const std::wstring v_wstr);
	CEdit m_EditContext;
	afx_msg void OnBnClickedOpen();
	afx_msg std::string GbkToUtf8(const char *src_str);
	afx_msg std::string Utf8ToGbk(const char *src_str);
	std::string CInputCheckTextDlg::WstringToString(const std::wstring v_wstr);
};
