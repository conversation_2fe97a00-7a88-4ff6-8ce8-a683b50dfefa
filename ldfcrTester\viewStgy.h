#pragma once
#include "afxwin.h"


// viewStgy 对话框

class viewStgy : public CDialogEx
{
	DECLARE_DYNAMIC(viewStgy)

public:
	viewStgy(CWnd* pParent = NULL);   // 标准构造函数
	viewStgy(CString v_cstr,CWnd* pParent = NULL);   // 标准构造函数
	virtual ~viewStgy();

// 对话框数据
#ifdef AFX_DESIGN_TIME
	enum { IDD = IDD_DIALOG_VIEW };
#endif

protected:
	virtual void DoDataExchange(CDataExchange* pDX);    // DDX/DDV 支持

	DECLARE_MESSAGE_MAP()
public:
	CEdit View;
	afx_msg void OnBnClickedBtnViewStrategy();
	CString m_cstrStrategy;
	virtual BOOL OnInitDialog();
	virtual void PostNcDestroy();
	virtual void OnCancel();
};
