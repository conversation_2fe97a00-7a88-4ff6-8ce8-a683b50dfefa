#pragma once
#include <string>
#include <vector>
#include <iostream>
#include <windows.h>
#include <io.h>
#include <algorithm>
#include <psapi.h>
#include "PE/PE.h"

#pragma comment(lib, "PE/PE.lib")
#pragma comment(lib, "psapi.lib")

using namespace std;
class PEinfo
{
public:
	PEinfo();
	~PEinfo();

public:
	void GetPEinfo();  // 原始方法：按发现顺序输出所有DLL信息

	// 按预定义顺序输出组件信息，包含序号和完整路径
	void GetPEinfoOrdered();

	// 获取预定义的组件顺序列表
	static const vector<string> getOrderedLibs();

	// 类似Linux/Mac的收集和输出分离功能
	void GetVersionInfoByOrder();  // 收集组件版本信息（类似Linux/Mac）
	void PrintVersionInfo();       // 输出已收集的版本信息（类似Linux/Mac）

	// 新增：真实依赖检测功能
	void GetRealLoadedModules();    // 获取程序实际加载的所有模块
	void PrintRealLoadedModules();  // 输出实际加载的模块信息

private:
	wstring GetCurModuleFile();

	void getCurFiles(wstring &v_wstrPath, vector<wstring>& v_vecFiles);//遍历当前目录

	// 新增：不使用配置文件过滤的文件遍历方法
	void getCurFilesWithoutFilter(wstring &v_wstrPath, vector<wstring>& v_vecFiles);

	// 新增：检查是否为目标DLL（基于文件名模式匹配，不依赖配置文件）
	bool isTargetDLL(const wstring& filePath);

	bool JudgeFileLogic(const wchar_t * v_wstrFilePath);

	void InsertPEInfo(const wchar_t * v_wstrFilePath, vector<string>& v_vecFiles);

	void printfVec(vector<string>& v_vecFiles);

	void GetLdfcrRunDll();

	void InsertModules();

	bool judgeFileIsWorkPath(const wchar_t * v_wchFilePath);

private:
	string WstringToString(const wstring v_wstr);

	wstring StringToWstring(const string v_str);

	string srcFileName(const string v_wstrPath);

private:
	wstring m_p;
	vector<wstring> m_dllVec;
	PE * m_pe;

	// 存储收集的组件信息（类似Linux/Mac的做法）
	vector<string> m_collectedComponentInfos;  // 存储格式化的组件信息
	vector<string> m_collectedComponentPaths;  // 存储对应的完整路径
	vector<string> m_collectedComponentNames;  // 存储组件名称

	// 存储真实加载的模块信息
	vector<string> m_realLoadedModuleInfos;    // 存储实际加载模块的格式化信息
	vector<string> m_realLoadedModulePaths;    // 存储实际加载模块的完整路径
	vector<string> m_realLoadedModuleNames;    // 存储实际加载模块的名称
};

