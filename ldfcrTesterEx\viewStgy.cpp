// viewStgy.cpp : 实现文件
//

#include "stdafx.h"
#include "ldfcrTester.h"
#include "viewStgy.h"
#include "afxdialogex.h"


// viewStgy 对话框

IMPLEMENT_DYNAMIC(viewStgy, CDialogEx)

viewStgy::viewStgy(CWnd* pParent /*=NULL*/)
	: CDialogEx(IDD_DIALOG_VIEW, pParent)
{

}

viewStgy::viewStgy(CString v_cstr, CWnd * pParent)
{
	this->m_cstrStrategy = v_cstr;
}

viewStgy::~viewStgy()
{
}

void viewStgy::DoDataExchange(CDataExchange* pDX)
{
	CDialogEx::DoDataExchange(pDX);
	DDX_Control(pDX, IDC_VIEW_STGY, View);
}


BEGIN_MESSAGE_MAP(viewStgy, CDialogEx)
END_MESSAGE_MAP()


// viewStgy 消息处理程序


void viewStgy::OnCancel()
{
	// TODO: 在此添加专用代码和/或调用基类

	DestroyWindow();
}

BOOL viewStgy::OnInitDialog()
{
	CDialogEx::OnInitDialog();

	// TODO:  在此添加额外的初始化
	View.SetWindowText(m_cstrStrategy);

	return TRUE;  // return TRUE unless you set the focus to a control
				  // 异常: OCX 属性页应返回 FALSE
}
